/**
 * Baum Mail Admin Styles
 *
 * @package BaumMail
 * @since 1.0.0
 */

/* Dashboard Overview */
.baum-mail-dashboard {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-top: 20px;
}

.baum-mail-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.stat-box {
  background: #fff;
  border: 1px solid #c3c4c7;
  border-radius: 4px;
  padding: 20px;
  text-align: center;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}

.stat-box h3 {
  font-size: 2.5em;
  margin: 0 0 10px 0;
  color: #2271b1;
  font-weight: 600;
}

.stat-box p {
  margin: 0;
  color: #646970;
  font-size: 14px;
}

.baum-mail-quick-actions {
  background: #fff;
  border: 1px solid #c3c4c7;
  border-radius: 4px;
  padding: 20px;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}

.baum-mail-quick-actions h2 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 18px;
}

.baum-mail-quick-actions .button {
  margin-right: 10px;
  margin-bottom: 10px;
}

.baum-mail-system-status {
  grid-column: 1 / -1;
  background: #fff;
  border: 1px solid #c3c4c7;
  border-radius: 4px;
  padding: 20px;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}

.baum-mail-system-status h2 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 18px;
}

.system-status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.status-item {
  padding: 15px;
  border-radius: 4px;
  border-left: 4px solid #ddd;
  background: #f9f9f9;
  position: relative;
}

.status-item.online {
  border-left-color: #00a32a;
  background: #f0f6fc;
}

.status-item.offline {
  border-left-color: #d63638;
  background: #fcf0f1;
}

.status-item h4 {
  margin: 0 0 5px 0;
  font-size: 14px;
  font-weight: 600;
}

.status-item p {
  margin: 0 0 8px 0;
  font-size: 13px;
  color: #646970;
}

.status-link {
  font-size: 12px;
  color: #0073aa;
  text-decoration: none;
  font-weight: 500;
}

.status-link:hover {
  color: #005a87;
  text-decoration: underline;
}

.status-indicator {
  position: absolute;
  top: 15px;
  right: 15px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #ddd;
}

.status-item.online .status-indicator {
  background: #00a32a;
}

.status-item.offline .status-indicator {
  background: #d63638;
}

/* Page Headers */
.baum-mail-page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #c3c4c7;
}

/* Status Badges */
.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 3px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.status-badge.active {
  background: #d1e7dd;
  color: #0f5132;
}

.status-badge.inactive {
  background: #f8d7da;
  color: #721c24;
}

/* Modal Styles */
.baum-mail-modal {
  position: fixed;
  z-index: 100000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.baum-mail-modal .modal-content {
  background-color: #fff;
  margin: 5% auto;
  padding: 0;
  border-radius: 4px;
  width: 90%;
  max-width: 600px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  position: relative;
}

.baum-mail-modal .modal-content h2 {
  margin: 0;
  padding: 20px 20px 10px 20px;
  border-bottom: 1px solid #c3c4c7;
  font-size: 18px;
}

.baum-mail-modal .modal-content .form-table {
  margin: 20px;
}

.baum-mail-modal .modal-content .submit {
  padding: 20px;
  margin: 0;
  border-top: 1px solid #c3c4c7;
  background: #f6f7f7;
  border-radius: 0 0 4px 4px;
}

.baum-mail-modal .close {
  position: absolute;
  top: 15px;
  right: 20px;
  font-size: 24px;
  font-weight: bold;
  cursor: pointer;
  color: #646970;
}

.baum-mail-modal .close:hover {
  color: #d63638;
}

/* Loading States */
.baum-mail-loading {
  position: relative;
  opacity: 0.6;
  pointer-events: none;
}

.baum-mail-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #2271b1;
  border-radius: 50%;
  animation: baum-mail-spin 1s linear infinite;
}

@keyframes baum-mail-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Security Page Styles */
.security-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.security-section {
  background: #fff;
  border: 1px solid #c3c4c7;
  border-radius: 4px;
  padding: 20px;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}

.security-section h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.security-section .dashicons {
  font-size: 20px;
}

.security-actions {
  margin-top: 15px;
}

.security-actions .button {
  margin-right: 10px;
  margin-bottom: 5px;
}

/* Monitoring Page Styles */
.monitoring-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.monitoring-section {
  background: #fff;
  border: 1px solid #c3c4c7;
  border-radius: 4px;
  padding: 20px;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}

.monitoring-section h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 16px;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #f0f0f1;
}

.metric-item:last-child {
  border-bottom: none;
}

.metric-label {
  font-weight: 500;
}

.metric-value {
  font-family: monospace;
  font-size: 14px;
  color: #2271b1;
}

.metric-value.warning {
  color: #dba617;
}

.metric-value.critical {
  color: #d63638;
}

/* Settings Page Styles */
.baum-mail-settings {
  max-width: 800px;
}

.settings-section {
  background: #fff;
  border: 1px solid #c3c4c7;
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}

.settings-section h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 16px;
  border-bottom: 1px solid #f0f0f1;
  padding-bottom: 10px;
}

/* Security Status Grid */
.security-status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
  margin-top: 20px;
}

.security-item {
  padding: 15px;
  border-radius: 4px;
  border-left: 4px solid #ddd;
  background: #f9f9f9;
  position: relative;
}

.security-item.secure {
  border-left-color: #00a32a;
  background: #f0f6fc;
}

.security-item.warning {
  border-left-color: #dba617;
  background: #fcf9e8;
}

.security-item h4 {
  margin: 0 0 5px 0;
  font-size: 14px;
  font-weight: 600;
}

.security-item p {
  margin: 0;
  font-size: 13px;
  color: #646970;
}

.security-indicator {
  position: absolute;
  top: 15px;
  right: 15px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #ddd;
}

.security-item.secure .security-indicator {
  background: #00a32a;
}

.security-item.warning .security-indicator {
  background: #dba617;
}

/* Monitoring Page Specific Styles */
.baum-mail-monitoring {
  margin-top: 20px;
}

.monitoring-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px 20px;
  background: #fff;
  border: 1px solid #c3c4c7;
  border-radius: 4px;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}

.last-updated {
  font-size: 13px;
  color: #646970;
}

.monitoring-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.monitoring-item {
  background: #fff;
  border: 1px solid #c3c4c7;
  border-radius: 4px;
  padding: 20px;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
  position: relative;
}

.monitoring-item.online {
  border-left: 4px solid #00a32a;
}

.monitoring-item.offline {
  border-left: 4px solid #d63638;
}

.monitoring-item h3 {
  margin: 0 0 10px 0;
  font-size: 16px;
  font-weight: 600;
}

.monitoring-item .status-indicator {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.monitoring-item .status-indicator.green {
  background: #00a32a;
}

.monitoring-item .status-indicator.red {
  background: #d63638;
}

.status-message {
  margin: 10px 0;
  font-size: 14px;
  color: #646970;
}

.status-details {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #f0f0f1;
}

.detail-item {
  display: block;
  margin-bottom: 5px;
  font-size: 13px;
  color: #646970;
}

.monitoring-actions {
  background: #fff;
  border: 1px solid #c3c4c7;
  border-radius: 4px;
  padding: 20px;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}

.monitoring-actions h2 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 18px;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.action-buttons .button {
  min-width: 120px;
}

/* Responsive Design */
@media (max-width: 782px) {
  .baum-mail-dashboard {
    grid-template-columns: 1fr;
  }
  
  .baum-mail-stats {
    grid-template-columns: 1fr 1fr;
  }
  
  .system-status-grid {
    grid-template-columns: 1fr;
  }
  
  .monitoring-grid {
    grid-template-columns: 1fr;
  }
  
  .baum-mail-modal .modal-content {
    width: 95%;
    margin: 10% auto;
  }
}

@media (max-width: 480px) {
  .baum-mail-stats {
    grid-template-columns: 1fr;
  }
  
  .baum-mail-page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .baum-mail-quick-actions .button {
    display: block;
    width: 100%;
    margin-right: 0;
    text-align: center;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .stat-box,
  .baum-mail-quick-actions,
  .baum-mail-system-status,
  .security-section,
  .monitoring-section,
  .settings-section {
    background: #1d2327;
    border-color: #3c434a;
  }
  
  .stat-box h3 {
    color: #72aee6;
  }
  
  .stat-box p,
  .status-item p {
    color: #a7aaad;
  }
  
  .status-item {
    background: #2c3338;
  }
  
  .status-item.online {
    background: #1a2e1a;
  }
  
  .status-item.offline {
    background: #2e1a1a;
  }
  
  .baum-mail-modal .modal-content {
    background: #1d2327;
    color: #f0f0f1;
  }
  
  .baum-mail-modal .modal-content .submit {
    background: #2c3338;
    border-color: #3c434a;
  }
}

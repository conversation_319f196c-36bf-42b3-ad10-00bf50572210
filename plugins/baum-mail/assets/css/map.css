/**
 * Baum Mail Map Styles
 * Custom styling for Leaflet.js maps in Baum Mail plugin
 */

.baum-mail-map {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e5e9;
}

.baum-mail-map .leaflet-container {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Custom popup styling */
.map-popup {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  min-width: 200px;
}

.map-popup h4 {
  margin: 0 0 10px 0;
  color: #2c3e50;
  font-size: 16px;
  font-weight: 600;
  border-bottom: 1px solid #e1e5e9;
  padding-bottom: 8px;
}

.map-popup p {
  margin: 5px 0;
  font-size: 13px;
  color: #555;
}

.map-popup strong {
  color: #2c3e50;
}

/* Custom marker styling */
.leaflet-marker-icon {
  filter: hue-rotate(280deg) saturate(1.2);
}

/* Custom control styling */
.leaflet-control-zoom {
  border: none !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
}

.leaflet-control-zoom a {
  background-color: white !important;
  color: #2c3e50 !important;
  border: none !important;
  font-weight: bold !important;
  transition: all 0.2s ease !important;
}

.leaflet-control-zoom a:hover {
  background-color: #f8f9fa !important;
  color: #ff79c6 !important;
}

/* Attribution styling */
.leaflet-control-attribution {
  background: rgba(255, 255, 255, 0.9) !important;
  font-size: 11px !important;
  color: #666 !important;
}

.leaflet-control-attribution a {
  color: #0073aa !important;
}

/* Circle styling for data visualization */
.leaflet-interactive {
  stroke: #ff79c6;
  stroke-width: 2;
  fill: #ff79c6;
  fill-opacity: 0.3;
}

/* Map container in admin */
.baum-mail-analytics .baum-mail-map {
  margin-top: 20px;
  margin-bottom: 20px;
}

/* Responsive map */
@media (max-width: 768px) {
  .baum-mail-map {
    height: 300px !important;
  }
  
  .map-popup {
    min-width: 150px;
  }
  
  .map-popup h4 {
    font-size: 14px;
  }
  
  .map-popup p {
    font-size: 12px;
  }
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
  .baum-mail-map {
    border-color: #44475a;
  }
  
  .map-popup {
    background: #282a36 !important;
    color: #f8f8f2 !important;
    border: 1px solid #44475a !important;
  }
  
  .map-popup h4 {
    color: #ff79c6 !important;
    border-bottom-color: #44475a !important;
  }
  
  .map-popup p {
    color: #f8f8f2 !important;
  }
  
  .map-popup strong {
    color: #8be9fd !important;
  }
}

/* Loading state */
.baum-mail-map.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  color: #666;
  font-size: 14px;
}

.baum-mail-map.loading::before {
  content: '';
  width: 20px;
  height: 20px;
  border: 2px solid #e1e5e9;
  border-top: 2px solid #0073aa;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Map legend */
.map-legend {
  background: white;
  padding: 15px;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  margin-top: 15px;
  border: 1px solid #e1e5e9;
}

.map-legend h4 {
  margin: 0 0 10px 0;
  color: #2c3e50;
  font-size: 14px;
  font-weight: 600;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  margin-right: 8px;
  border: 1px solid #ccc;
}

.legend-color.high {
  background: #ff79c6;
}

.legend-color.medium {
  background: #8be9fd;
}

.legend-color.low {
  background: #50fa7b;
}

/* Map controls */
.map-controls {
  margin-bottom: 15px;
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  align-items: center;
}

.map-controls select,
.map-controls input {
  padding: 6px 10px;
  border: 1px solid #e1e5e9;
  border-radius: 4px;
  font-size: 13px;
}

.map-controls .button {
  padding: 6px 12px;
  font-size: 13px;
}

/* Full screen map */
.baum-mail-map.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999999;
  height: 100vh !important;
  width: 100vw !important;
  border-radius: 0;
}

.fullscreen-toggle {
  position: absolute;
  top: 10px;
  right: 50px;
  z-index: 1000;
  background: white;
  border: 1px solid #ccc;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.fullscreen-toggle:hover {
  background: #f8f9fa;
}

/**
 * Baum Mail Frontend Styles
 *
 * @package BaumMail
 * @since 1.0.0
 */

/* Frontend styles for any public-facing components */
.baum-mail-frontend {
  /* Add frontend styles here if needed */
}

/* Email subscription forms */
.baum-mail-subscription-form {
  max-width: 400px;
  margin: 20px 0;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: #f9f9f9;
}

.baum-mail-subscription-form h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 18px;
}

.baum-mail-subscription-form input[type="email"] {
  width: 100%;
  padding: 10px;
  margin-bottom: 10px;
  border: 1px solid #ccc;
  border-radius: 3px;
  font-size: 14px;
}

.baum-mail-subscription-form button {
  background: #0073aa;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 3px;
  cursor: pointer;
  font-size: 14px;
}

.baum-mail-subscription-form button:hover {
  background: #005a87;
}

/* Status messages */
.baum-mail-message {
  padding: 10px 15px;
  margin: 10px 0;
  border-radius: 3px;
}

.baum-mail-message.success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.baum-mail-message.error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.baum-mail-message.info {
  background: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}

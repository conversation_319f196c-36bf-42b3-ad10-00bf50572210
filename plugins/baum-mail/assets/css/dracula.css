/*
 * Dracula Theme for Highlight.js
 * Based on the official Dracula color scheme
 * https://draculatheme.com/
 */

.hljs {
  background: #282a36;
  color: #f8f8f2;
  display: block;
  overflow-x: auto;
  padding: 0.5em;
}

.hljs-built_in,
.hljs-selector-tag,
.hljs-section,
.hljs-link {
  color: #8be9fd;
}

.hljs-keyword {
  color: #ff79c6;
}

.hljs,
.hljs-subst {
  color: #f8f8f2;
}

.hljs-title {
  color: #50fa7b;
}

.hljs-string,
.hljs-meta,
.hljs-name,
.hljs-type,
.hljs-attr,
.hljs-symbol,
.hljs-bullet,
.hljs-addition,
.hljs-variable,
.hljs-template-tag,
.hljs-template-variable {
  color: #f1fa8c;
}

.hljs-comment,
.hljs-quote,
.hljs-deletion {
  color: #6272a4;
}

.hljs-keyword,
.hljs-selector-tag,
.hljs-literal,
.hljs-title,
.hljs-section,
.hljs-doctag,
.hljs-type,
.hljs-name,
.hljs-strong {
  font-weight: bold;
}

.hljs-literal,
.hljs-number {
  color: #bd93f9;
}

.hljs-emphasis {
  font-style: italic;
}

/* Language specific overrides */
.hljs-tag {
  color: #ff79c6;
}

.hljs-tag .hljs-name {
  color: #ff79c6;
}

.hljs-tag .hljs-attr {
  color: #50fa7b;
}

.hljs-attribute {
  color: #50fa7b;
}

.hljs-function {
  color: #50fa7b;
}

.hljs-class .hljs-title {
  color: #50fa7b;
}

.hljs-params {
  color: #ffb86c;
}

.hljs-regexp {
  color: #f1fa8c;
}

.hljs-selector-id,
.hljs-selector-class {
  color: #50fa7b;
}

.hljs-selector-pseudo {
  color: #ff79c6;
}

/* PHP specific */
.hljs-variable {
  color: #f8f8f2;
}

.hljs-meta .hljs-keyword {
  color: #ff79c6;
}

/* JavaScript specific */
.hljs-built_in {
  color: #8be9fd;
}

/* CSS specific */
.hljs-selector-tag {
  color: #ff79c6;
}

.hljs-selector-id,
.hljs-selector-class {
  color: #50fa7b;
}

.hljs-property {
  color: #8be9fd;
}

/* JSON specific */
.hljs-attr {
  color: #50fa7b;
}

/* Bash/Shell specific */
.hljs-built_in {
  color: #8be9fd;
}

/* SQL specific */
.hljs-keyword {
  color: #ff79c6;
}

/* Markdown specific */
.hljs-section {
  color: #ff79c6;
  font-weight: bold;
}

.hljs-bullet {
  color: #8be9fd;
}

.hljs-emphasis {
  color: #f1fa8c;
  font-style: italic;
}

.hljs-strong {
  color: #ffb86c;
  font-weight: bold;
}

.hljs-code {
  color: #50fa7b;
}

.hljs-link {
  color: #8be9fd;
  text-decoration: underline;
}

/* Additional styling for better readability */
.hljs-doctag,
.hljs-formula {
  color: #6272a4;
}

.hljs-deletion {
  background: #ff5555;
  color: #f8f8f2;
}

.hljs-addition {
  background: #50fa7b;
  color: #282a36;
}

/* Line numbers support */
.hljs-ln-numbers {
  color: #6272a4;
  border-right: 1px solid #44475a;
  padding-right: 10px;
  margin-right: 10px;
}

.hljs-ln-code {
  padding-left: 10px;
}

/* Selection */
.hljs::selection,
.hljs span::selection {
  background: #44475a;
}

/* Scrollbar styling for webkit browsers */
.hljs::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.hljs::-webkit-scrollbar-track {
  background: #44475a;
}

.hljs::-webkit-scrollbar-thumb {
  background: #6272a4;
  border-radius: 4px;
}

.hljs::-webkit-scrollbar-thumb:hover {
  background: #8be9fd;
}

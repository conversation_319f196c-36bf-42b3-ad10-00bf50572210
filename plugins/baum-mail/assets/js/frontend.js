/**
 * Baum Mail Frontend JavaScript
 *
 * @package BaumMail
 * @since 1.0.0
 */

(function($) {
  'use strict';

  /**
   * Frontend functionality
   */
  var BaumMailFrontend = {
    
    /**
     * Initialize frontend functionality
     */
    init: function() {
      this.bindEvents();
    },

    /**
     * Bind event handlers
     */
    bindEvents: function() {
      // Email subscription forms
      $(document).on('submit', '.baum-mail-subscription-form', this.handleSubscription);
      
      // Any other frontend interactions
    },

    /**
     * Handle email subscription form submission
     */
    handleSubscription: function(e) {
      e.preventDefault();
      
      var $form = $(this);
      var $button = $form.find('button[type="submit"]');
      var $email = $form.find('input[type="email"]');
      var email = $email.val();
      
      // Basic validation
      if (!email || !BaumMailFrontend.isValidEmail(email)) {
        BaumMailFrontend.showMessage($form, 'Please enter a valid email address.', 'error');
        return;
      }
      
      // Disable form during submission
      $button.prop('disabled', true).text('Subscribing...');
      
      // AJAX request (if needed)
      $.ajax({
        url: baumMailFrontend.ajaxUrl,
        type: 'POST',
        data: {
          action: 'baum_mail_subscribe',
          email: email,
          nonce: baumMailFrontend.nonce
        },
        success: function(response) {
          if (response.success) {
            BaumMailFrontend.showMessage($form, 'Successfully subscribed!', 'success');
            $email.val('');
          } else {
            BaumMailFrontend.showMessage($form, response.data || 'Subscription failed.', 'error');
          }
        },
        error: function() {
          BaumMailFrontend.showMessage($form, 'An error occurred. Please try again.', 'error');
        },
        complete: function() {
          $button.prop('disabled', false).text('Subscribe');
        }
      });
    },

    /**
     * Show message to user
     */
    showMessage: function($form, message, type) {
      // Remove existing messages
      $form.find('.baum-mail-message').remove();
      
      // Add new message
      var $message = $('<div class="baum-mail-message ' + type + '">' + message + '</div>');
      $form.prepend($message);
      
      // Auto-hide success messages after 5 seconds
      if (type === 'success') {
        setTimeout(function() {
          $message.fadeOut();
        }, 5000);
      }
    },

    /**
     * Validate email address
     */
    isValidEmail: function(email) {
      var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return emailRegex.test(email);
    }
  };

  /**
   * Initialize when document is ready
   */
  $(document).ready(function() {
    BaumMailFrontend.init();
  });

})(jQuery);

/**
 * Baum Mail Admin JavaScript
 *
 * @package BaumMail
 * @since 1.0.0
 */

(function($) {
  'use strict';

  /**
   * Initialize when document is ready
   */
  $(document).ready(function() {
    initializeServiceManagement();
    initializeForms();
    initializeNotifications();
  });

  /**
   * Initialize form functionality
   */
  function initializeForms() {
    // Form validation and enhancement
    $('.baum-mail-form').each(function() {
      var $form = $(this);

      // Add form validation
      $form.on('submit', function(e) {
        var isValid = true;

        // Check required fields
        $form.find('[required]').each(function() {
          if (!$(this).val().trim()) {
            $(this).addClass('error');
            isValid = false;
          } else {
            $(this).removeClass('error');
          }
        });

        // Check email fields
        $form.find('input[type="email"]').each(function() {
          var email = $(this).val().trim();
          if (email && !isValidEmail(email)) {
            $(this).addClass('error');
            isValid = false;
          } else {
            $(this).removeClass('error');
          }
        });

        if (!isValid) {
          e.preventDefault();
          showNotice('Please fill in all required fields correctly.', 'error');
        }
      });
    });
  }

  /**
   * Initialize notifications
   */
  function initializeNotifications() {
    // Auto-dismiss notices after 5 seconds
    setTimeout(function() {
      $('.notice.is-dismissible').fadeOut();
    }, 5000);

    // Handle manual dismiss
    $(document).on('click', '.notice-dismiss', function() {
      $(this).closest('.notice').fadeOut();
    });
  }

  /**
   * Validate email address
   */
  function isValidEmail(email) {
    var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Initialize service management
   */
  function initializeServiceManagement() {
    // Service restart buttons
    $('.service-actions .button[data-service]').click(function() {
      var $button = $(this);
      var service = $button.data('service');
      var originalText = $button.text();
      
      if (!confirm('Are you sure you want to restart ' + service + '?')) {
        return;
      }
      
      $button.prop('disabled', true).text('Restarting...');
      
      $.ajax({
        url: baumMailAdmin.ajaxUrl,
        type: 'POST',
        data: {
          action: 'baum_mail_restart_service',
          service: service,
          nonce: baumMailAdmin.nonce
        },
        success: function(response) {
          if (response.success) {
            showNotice(response.data, 'success');
            // Redirect to logs page to show restart activity
            setTimeout(function() {
              window.location.href = window.location.origin + window.location.pathname + '?page=baum-mail-logs';
            }, 1000);
          } else {
            showNotice(response.data, 'error');
          }
        },
        error: function() {
          showNotice('Failed to restart service.', 'error');
        },
        complete: function() {
          $button.prop('disabled', false).text(originalText);
        }
      });
    });

    // Flush queue button
    $('#flush-queue-btn').click(function() {
      var $button = $(this);
      var originalText = $button.text();
      
      if (!confirm('Are you sure you want to flush the mail queue?')) {
        return;
      }
      
      $button.prop('disabled', true).text('Flushing...');
      
      $.ajax({
        url: baumMailAdmin.ajaxUrl,
        type: 'POST',
        data: {
          action: 'baum_mail_flush_queue',
          nonce: baumMailAdmin.nonce
        },
        success: function(response) {
          if (response.success) {
            showNotice('Mail queue flushed successfully.', 'success');
          } else {
            showNotice(response.data, 'error');
          }
        },
        error: function() {
          showNotice('Failed to flush mail queue.', 'error');
        },
        complete: function() {
          $button.prop('disabled', false).text(originalText);
        }
      });
    });
  }

  /**
   * Show notification
   */
  function showNotice(message, type) {
    type = type || 'info';
    var $notice = $('<div class="notice notice-' + type + ' is-dismissible"><p>' + message + '</p></div>');
    
    $('.wrap h1').after($notice);
    
    // Auto-dismiss after 5 seconds
    setTimeout(function() {
      $notice.fadeOut(function() {
        $(this).remove();
      });
    }, 5000);
  }

  // Expose utility functions globally
  window.BaumMailAdmin = {
    showNotice: showNotice
  };

})(jQuery);

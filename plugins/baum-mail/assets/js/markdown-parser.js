/**
 * Simple Markdown Parser for Baum Mail Documentation
 * Converts markdown text to HTML with syntax highlighting support
 */

class MarkdownParser {
  constructor() {
    this.rules = [
      // Headers
      { pattern: /^### (.*$)/gim, replacement: '<h3>$1</h3>' },
      { pattern: /^## (.*$)/gim, replacement: '<h2>$1</h2>' },
      { pattern: /^# (.*$)/gim, replacement: '<h1>$1</h1>' },
      
      // Code blocks with language
      { pattern: /```(\w+)?\n([\s\S]*?)```/g, replacement: this.codeBlock.bind(this) },
      
      // Inline code
      { pattern: /`([^`]+)`/g, replacement: '<code class="inline-code">$1</code>' },
      
      // Bold
      { pattern: /\*\*(.*?)\*\*/g, replacement: '<strong>$1</strong>' },
      
      // Italic
      { pattern: /\*(.*?)\*/g, replacement: '<em>$1</em>' },
      
      // Links
      { pattern: /\[([^\]]+)\]\(([^)]+)\)/g, replacement: '<a href="$2" target="_blank">$1</a>' },
      
      // Unordered lists
      { pattern: /^\* (.+)$/gm, replacement: '<li>$1</li>' },
      { pattern: /^- (.+)$/gm, replacement: '<li>$1</li>' },
      
      // Ordered lists
      { pattern: /^\d+\. (.+)$/gm, replacement: '<li>$1</li>' },
      
      // Blockquotes
      { pattern: /^> (.+)$/gm, replacement: '<blockquote>$1</blockquote>' },
      
      // Horizontal rules
      { pattern: /^---$/gm, replacement: '<hr>' },
      
      // Line breaks
      { pattern: /\n\n/g, replacement: '</p><p>' },
      { pattern: /\n/g, replacement: '<br>' }
    ];
  }

  codeBlock(match, language, code) {
    const lang = language || 'text';
    const escapedCode = this.escapeHtml(code.trim());
    return `<pre><code class="hljs language-${lang}" data-language="${lang}">${escapedCode}</code></pre>`;
  }

  escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  parse(markdown) {
    let html = markdown;

    // Apply all rules
    this.rules.forEach(rule => {
      if (typeof rule.replacement === 'function') {
        html = html.replace(rule.pattern, rule.replacement);
      } else {
        html = html.replace(rule.pattern, rule.replacement);
      }
    });

    // Wrap in paragraphs
    html = '<p>' + html + '</p>';

    // Fix list formatting
    html = html.replace(/<\/li>\s*<br>\s*<li>/g, '</li><li>');
    html = html.replace(/<p>\s*<li>/g, '<ul><li>');
    html = html.replace(/<\/li>\s*<\/p>/g, '</li></ul>');
    
    // Fix ordered lists
    html = html.replace(/<ul><li>(\d+\.)/g, '<ol><li>');
    html = html.replace(/<\/li><\/ul>(\s*<ol>)/g, '</li></ol>$1');

    // Clean up extra paragraphs around block elements
    html = html.replace(/<p>\s*<(h[1-6]|pre|blockquote|hr|ul|ol)/g, '<$1');
    html = html.replace(/<\/(h[1-6]|pre|blockquote|hr|ul|ol)>\s*<\/p>/g, '</$1>');

    // Clean up empty paragraphs
    html = html.replace(/<p>\s*<\/p>/g, '');

    return html;
  }

  static render(elementId, markdown) {
    const parser = new MarkdownParser();
    const element = document.getElementById(elementId);
    
    if (element) {
      element.innerHTML = parser.parse(markdown);
      
      // Apply syntax highlighting to code blocks
      if (typeof hljs !== 'undefined') {
        element.querySelectorAll('pre code').forEach((block) => {
          hljs.highlightElement(block);
        });
      }
    }
  }
}

// Export for use
window.MarkdownParser = MarkdownParser;

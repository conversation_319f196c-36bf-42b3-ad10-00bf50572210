<?php
/**
 * Baum Mail Documentation Generator
 *
 * Generates documentation from PHPDoc and JSDoc comments
 *
 * @package BaumMail
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
  exit;
}

/**
 * Documentation generator for PHPDoc and JSDoc
 *
 * @since 1.0.0
 */
class BaumMail_Docs_Generator {

  /**
   * Constructor
   *
   * @since 1.0.0
   */
  public function __construct() {
    // Constructor
  }

  /**
   * Generate documentation for all plugin files
   *
   * @return array Generated documentation
   * @since 1.0.0
   */
  public function generate_documentation() {
    $docs = array(
      'php' => $this->generate_php_docs(),
      'javascript' => $this->generate_js_docs()
    );

    return $docs;
  }

  /**
   * Generate PHP documentation from PHPDoc comments
   *
   * @return array PHP documentation
   * @since 1.0.0
   */
  public function generate_php_docs() {
    $php_files = $this->get_php_files();
    $docs = array();

    foreach ($php_files as $file) {
      // Skip third-party files
      if ($this->is_third_party_file($file)) {
        continue;
      }

      $file_docs = $this->parse_php_file($file);
      if (!empty($file_docs)) {
        $docs[basename($file)] = $file_docs;
      }
    }

    return $docs;
  }

  /**
   * Check if file is third-party code
   *
   * @param string $file File path
   * @return bool True if third-party
   * @since 1.0.0
   */
  private function is_third_party_file($file) {
    $third_party_patterns = array(
      '/vendor/',
      '/node_modules/',
      '/assets/js/leaflet.js',
      '/assets/js/highlight.js',
      '/assets/js/markdown-parser.js',
      '/assets/css/leaflet.css',
      '/assets/css/dracula.css'
    );

    foreach ($third_party_patterns as $pattern) {
      if (strpos($file, $pattern) !== false) {
        return true;
      }
    }

    return false;
  }

  /**
   * Generate JavaScript documentation from JSDoc comments
   *
   * @return array JavaScript documentation
   * @since 1.0.0
   */
  public function generate_js_docs() {
    $js_files = $this->get_js_files();
    $docs = array();

    foreach ($js_files as $file) {
      $file_docs = $this->parse_js_file($file);
      if (!empty($file_docs)) {
        $docs[basename($file)] = $file_docs;
      }
    }

    return $docs;
  }

  /**
   * Get all PHP files in the plugin
   *
   * @return array List of PHP file paths
   * @since 1.0.0
   */
  private function get_php_files() {
    $files = array();
    $plugin_dir = BAUM_MAIL_PLUGIN_DIR;

    // Main plugin file
    $files[] = $plugin_dir . 'baum-mail.php';

    // Include files
    $includes_dir = $plugin_dir . 'includes/';
    if (is_dir($includes_dir)) {
      $include_files = glob($includes_dir . '*.php');
      $files = array_merge($files, $include_files);
    }

    return $files;
  }

  /**
   * Get all JavaScript files in the plugin
   *
   * @return array List of JavaScript file paths
   * @since 1.0.0
   */
  private function get_js_files() {
    $files = array();
    $assets_dir = BAUM_MAIL_PLUGIN_DIR . 'assets/js/';

    if (is_dir($assets_dir)) {
      $js_files = glob($assets_dir . '*.js');
      $files = array_merge($files, $js_files);
    }

    return $files;
  }

  /**
   * Parse PHP file for PHPDoc comments
   *
   * @param string $file_path Path to PHP file
   * @return array Parsed documentation
   * @since 1.0.0
   */
  private function parse_php_file($file_path) {
    if (!file_exists($file_path)) {
      return array();
    }

    $content = file_get_contents($file_path);
    $docs = array();

    // Extract class information
    if (preg_match('/class\s+(\w+)/', $content, $class_matches)) {
      $class_name = $class_matches[1];
      $class_doc = $this->extract_php_doc_before_pattern($content, '/class\s+' . $class_name . '/');
      
      $docs['class'] = array(
        'name' => $class_name,
        'description' => $this->parse_php_doc($class_doc)
      );
    }

    // Extract public methods
    preg_match_all('/\/\*\*(.*?)\*\/\s*public\s+function\s+(\w+)\s*\((.*?)\)/s', $content, $matches, PREG_SET_ORDER);

    foreach ($matches as $match) {
      $doc_comment = $match[1];
      $method_name = $match[2];
      $parameters = $match[3];

      $parsed_doc = $this->parse_php_doc($doc_comment);
      $parsed_doc['parameters'] = $this->parse_php_parameters($parameters);

      $docs['methods'][$method_name] = $parsed_doc;
    }

    return $docs;
  }

  /**
   * Parse JavaScript file for JSDoc comments
   *
   * @param string $file_path Path to JavaScript file
   * @return array Parsed documentation
   * @since 1.0.0
   */
  private function parse_js_file($file_path) {
    if (!file_exists($file_path)) {
      return array();
    }

    $content = file_get_contents($file_path);
    $docs = array();

    // Extract class information
    if (preg_match('/class\s+(\w+)/', $content, $class_matches)) {
      $class_name = $class_matches[1];
      $class_doc = $this->extract_js_doc_before_pattern($content, '/class\s+' . $class_name . '/');
      
      $docs['class'] = array(
        'name' => $class_name,
        'description' => $this->parse_js_doc($class_doc)
      );
    }

    // Extract functions
    preg_match_all('/\/\*\*(.*?)\*\/\s*(?:function\s+(\w+)|(\w+)\s*[:=]\s*function|\s*(\w+)\s*\()/s', $content, $matches, PREG_SET_ORDER);

    foreach ($matches as $match) {
      $doc_comment = $match[1];
      $function_name = $match[2] ?: $match[3] ?: $match[4];

      if ($function_name) {
        $parsed_doc = $this->parse_js_doc($doc_comment);
        $docs['functions'][$function_name] = $parsed_doc;
      }
    }

    return $docs;
  }

  /**
   * Parse PHPDoc comment
   *
   * @param string $doc_comment PHPDoc comment content
   * @return array Parsed documentation
   * @since 1.0.0
   */
  private function parse_php_doc($doc_comment) {
    $lines = explode("\n", $doc_comment);
    $parsed = array(
      'description' => '',
      'params' => array(),
      'return' => '',
      'since' => '',
      'example' => ''
    );

    $current_section = 'description';
    $description_lines = array();

    foreach ($lines as $line) {
      $line = trim($line, " \t\n\r\0\x0B*");
      
      if (empty($line)) {
        continue;
      }

      if (preg_match('/@(\w+)\s*(.*)/', $line, $matches)) {
        $tag = $matches[1];
        $content = $matches[2];

        switch ($tag) {
          case 'param':
            if (preg_match('/(\S+)\s+\$(\w+)\s*(.*)/', $content, $param_matches)) {
              $parsed['params'][] = array(
                'type' => $param_matches[1],
                'name' => $param_matches[2],
                'description' => $param_matches[3]
              );
            }
            break;
          case 'return':
            $parsed['return'] = $content;
            break;
          case 'since':
            $parsed['since'] = $content;
            break;
          default:
            $parsed[$tag] = $content;
            break;
        }
      } else {
        if ($current_section === 'description') {
          $description_lines[] = $line;
        }
      }
    }

    $parsed['description'] = implode(' ', $description_lines);
    return $parsed;
  }

  /**
   * Parse JSDoc comment
   *
   * @param string $doc_comment JSDoc comment content
   * @return array Parsed documentation
   * @since 1.0.0
   */
  private function parse_js_doc($doc_comment) {
    $lines = explode("\n", $doc_comment);
    $parsed = array(
      'description' => '',
      'params' => array(),
      'returns' => '',
      'example' => ''
    );

    $current_section = 'description';
    $description_lines = array();

    foreach ($lines as $line) {
      $line = trim($line, " \t\n\r\0\x0B*");
      
      if (empty($line)) {
        continue;
      }

      if (preg_match('/@(\w+)\s*(.*)/', $line, $matches)) {
        $tag = $matches[1];
        $content = $matches[2];

        switch ($tag) {
          case 'param':
            if (preg_match('/\{([^}]+)\}\s+(\w+)\s*(.*)/', $content, $param_matches)) {
              $parsed['params'][] = array(
                'type' => $param_matches[1],
                'name' => $param_matches[2],
                'description' => $param_matches[3]
              );
            }
            break;
          case 'returns':
          case 'return':
            $parsed['returns'] = $content;
            break;
          default:
            $parsed[$tag] = $content;
            break;
        }
      } else {
        if ($current_section === 'description') {
          $description_lines[] = $line;
        }
      }
    }

    $parsed['description'] = implode(' ', $description_lines);
    return $parsed;
  }

  /**
   * Parse PHP function parameters
   *
   * @param string $parameters Parameter string
   * @return array Parsed parameters
   * @since 1.0.0
   */
  private function parse_php_parameters($parameters) {
    $params = array();
    $param_list = explode(',', $parameters);

    foreach ($param_list as $param) {
      $param = trim($param);
      if (preg_match('/(\$\w+)/', $param, $matches)) {
        $params[] = $matches[1];
      }
    }

    return $params;
  }

  /**
   * Extract PHPDoc comment before a pattern
   *
   * @param string $content File content
   * @param string $pattern Pattern to search for
   * @return string PHPDoc comment
   * @since 1.0.0
   */
  private function extract_php_doc_before_pattern($content, $pattern) {
    if (preg_match('/\/\*\*(.*?)\*\/\s*' . $pattern . '/s', $content, $matches)) {
      return $matches[1];
    }
    return '';
  }

  /**
   * Extract JSDoc comment before a pattern
   *
   * @param string $content File content
   * @param string $pattern Pattern to search for
   * @return string JSDoc comment
   * @since 1.0.0
   */
  private function extract_js_doc_before_pattern($content, $pattern) {
    if (preg_match('/\/\*\*(.*?)\*\/\s*' . $pattern . '/s', $content, $matches)) {
      return $matches[1];
    }
    return '';
  }

  /**
   * Generate markdown documentation
   *
   * @param array $docs Documentation array
   * @return string Markdown documentation
   * @since 1.0.0
   */
  public function generate_markdown($docs) {
    $markdown = "# API Documentation\n\n";

    if (!empty($docs['php'])) {
      $markdown .= "## PHP Classes and Methods\n\n";
      
      foreach ($docs['php'] as $file => $file_docs) {
        $markdown .= "### {$file}\n\n";
        
        if (!empty($file_docs['class'])) {
          $class = $file_docs['class'];
          $markdown .= "#### Class: {$class['name']}\n\n";
          $markdown .= "{$class['description']['description']}\n\n";
        }

        if (!empty($file_docs['methods'])) {
          $markdown .= "#### Public Methods\n\n";
          
          foreach ($file_docs['methods'] as $method_name => $method_doc) {
            $markdown .= "##### {$method_name}()\n\n";
            $markdown .= "{$method_doc['description']}\n\n";
            
            if (!empty($method_doc['params'])) {
              $markdown .= "**Parameters:**\n\n";
              foreach ($method_doc['params'] as $param) {
                $markdown .= "- `{$param['type']} \${$param['name']}` - {$param['description']}\n";
              }
              $markdown .= "\n";
            }
            
            if (!empty($method_doc['return'])) {
              $markdown .= "**Returns:** {$method_doc['return']}\n\n";
            }
            
            if (!empty($method_doc['since'])) {
              $markdown .= "**Since:** {$method_doc['since']}\n\n";
            }
            
            $markdown .= "---\n\n";
          }
        }
      }
    }

    if (!empty($docs['javascript'])) {
      $markdown .= "## JavaScript Classes and Functions\n\n";
      
      foreach ($docs['javascript'] as $file => $file_docs) {
        $markdown .= "### {$file}\n\n";
        
        if (!empty($file_docs['class'])) {
          $class = $file_docs['class'];
          $markdown .= "#### Class: {$class['name']}\n\n";
          $markdown .= "{$class['description']['description']}\n\n";
        }

        if (!empty($file_docs['functions'])) {
          $markdown .= "#### Functions\n\n";
          
          foreach ($file_docs['functions'] as $function_name => $function_doc) {
            $markdown .= "##### {$function_name}()\n\n";
            $markdown .= "{$function_doc['description']}\n\n";
            
            if (!empty($function_doc['params'])) {
              $markdown .= "**Parameters:**\n\n";
              foreach ($function_doc['params'] as $param) {
                $markdown .= "- `{$param['type']} {$param['name']}` - {$param['description']}\n";
              }
              $markdown .= "\n";
            }
            
            if (!empty($function_doc['returns'])) {
              $markdown .= "**Returns:** {$function_doc['returns']}\n\n";
            }
            
            $markdown .= "---\n\n";
          }
        }
      }
    }

    return $markdown;
  }
}

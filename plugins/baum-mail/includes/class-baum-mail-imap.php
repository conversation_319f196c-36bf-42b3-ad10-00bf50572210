<?php
/**
 * Baum Mail IMAP Class
 *
 * @package BaumMail
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
  exit;
}

/**
 * IMAP server management and operations
 *
 * @since 1.0.0
 */
class BaumMail_IMAP {

  /**
   * Constructor
   *
   * @since 1.0.0
   */
  public function __construct() {
    $this->init_hooks();
  }

  /**
   * Initialize hooks
   *
   * @since 1.0.0
   */
  private function init_hooks() {
    // No WordPress hooks needed for IMAP operations
  }

  /**
   * Connect to IMAP server
   *
   * @param string $email Email address
   * @param string $password Password
   * @param string $server IMAP server (optional, defaults to localhost)
   * @param int $port IMAP port (optional, defaults to 993 for SSL)
   * @param bool $ssl Use SSL (optional, defaults to true)
   * @return resource|WP_Error IMAP connection or error
   * @since 1.0.0
   */
  public function connect($email, $password, $server = 'localhost', $port = 993, $ssl = true) {
    if (!extension_loaded('imap')) {
      return new WP_Error('imap_missing', __('IMAP extension is not installed.', BAUM_MAIL_TEXT_DOMAIN));
    }

    // Build connection string
    $flags = $ssl ? '/imap/ssl/validate-cert' : '/imap/notls';
    $mailbox = "{{$server}:{$port}{$flags}}";

    // Attempt connection
    $connection = @imap_open($mailbox, $email, $password);

    if (!$connection) {
      $error = imap_last_error();
      return new WP_Error('imap_connection_failed', $error ?: __('Failed to connect to IMAP server.', BAUM_MAIL_TEXT_DOMAIN));
    }

    return $connection;
  }

  /**
   * Get mailbox list for user
   *
   * @param string $email Email address
   * @param string $password Password
   * @return array|WP_Error Mailbox list or error
   * @since 1.0.0
   */
  public function get_mailboxes($email, $password) {
    $connection = $this->connect($email, $password);
    
    if (is_wp_error($connection)) {
      return $connection;
    }

    $mailboxes = imap_list($connection, "{{localhost:993/imap/ssl/validate-cert}}", "*");
    
    if (!$mailboxes) {
      imap_close($connection);
      return new WP_Error('mailbox_list_failed', __('Failed to retrieve mailbox list.', BAUM_MAIL_TEXT_DOMAIN));
    }

    $formatted_mailboxes = array();
    foreach ($mailboxes as $mailbox) {
      $formatted_mailboxes[] = imap_utf7_decode($mailbox);
    }

    imap_close($connection);
    return $formatted_mailboxes;
  }

  /**
   * Get messages from mailbox
   *
   * @param string $email Email address
   * @param string $password Password
   * @param string $mailbox Mailbox name (default: INBOX)
   * @param int $limit Number of messages to retrieve (default: 50)
   * @return array|WP_Error Messages or error
   * @since 1.0.0
   */
  public function get_messages($email, $password, $mailbox = 'INBOX', $limit = 50) {
    $connection = $this->connect($email, $password);
    
    if (is_wp_error($connection)) {
      return $connection;
    }

    // Select mailbox
    $full_mailbox = "{{localhost:993/imap/ssl/validate-cert}}{$mailbox}";
    if (!imap_reopen($connection, $full_mailbox)) {
      imap_close($connection);
      return new WP_Error('mailbox_select_failed', __('Failed to select mailbox.', BAUM_MAIL_TEXT_DOMAIN));
    }

    // Get message count
    $message_count = imap_num_msg($connection);
    
    if ($message_count === 0) {
      imap_close($connection);
      return array();
    }

    // Calculate range
    $start = max(1, $message_count - $limit + 1);
    $end = $message_count;

    $messages = array();
    for ($i = $end; $i >= $start; $i--) {
      $header = imap_headerinfo($connection, $i);
      $structure = imap_fetchstructure($connection, $i);
      
      $message = array(
        'uid' => imap_uid($connection, $i),
        'message_id' => $i,
        'subject' => isset($header->subject) ? imap_utf8($header->subject) : '',
        'from' => isset($header->from[0]) ? $header->from[0]->mailbox . '@' . $header->from[0]->host : '',
        'to' => isset($header->to[0]) ? $header->to[0]->mailbox . '@' . $header->to[0]->host : '',
        'date' => isset($header->date) ? date('Y-m-d H:i:s', strtotime($header->date)) : '',
        'size' => isset($header->Size) ? $header->Size : 0,
        'seen' => isset($header->Unseen) ? false : true,
        'flagged' => isset($header->Flagged) ? true : false,
        'answered' => isset($header->Answered) ? true : false,
        'deleted' => isset($header->Deleted) ? true : false,
        'draft' => isset($header->Draft) ? true : false,
        'has_attachments' => $this->has_attachments($structure)
      );

      $messages[] = $message;
    }

    imap_close($connection);
    return $messages;
  }

  /**
   * Get message content
   *
   * @param string $email Email address
   * @param string $password Password
   * @param int $message_id Message ID
   * @param string $mailbox Mailbox name (default: INBOX)
   * @return array|WP_Error Message content or error
   * @since 1.0.0
   */
  public function get_message_content($email, $password, $message_id, $mailbox = 'INBOX') {
    $connection = $this->connect($email, $password);
    
    if (is_wp_error($connection)) {
      return $connection;
    }

    // Select mailbox
    $full_mailbox = "{{localhost:993/imap/ssl/validate-cert}}{$mailbox}";
    if (!imap_reopen($connection, $full_mailbox)) {
      imap_close($connection);
      return new WP_Error('mailbox_select_failed', __('Failed to select mailbox.', BAUM_MAIL_TEXT_DOMAIN));
    }

    $structure = imap_fetchstructure($connection, $message_id);
    $header = imap_headerinfo($connection, $message_id);

    $content = array(
      'header' => $header,
      'text_body' => '',
      'html_body' => '',
      'attachments' => array()
    );

    // Get message body
    if (isset($structure->parts) && count($structure->parts)) {
      // Multipart message
      for ($i = 0; $i < count($structure->parts); $i++) {
        $part = $structure->parts[$i];
        $part_number = $i + 1;
        
        $this->parse_message_part($connection, $message_id, $part, $part_number, $content);
      }
    } else {
      // Single part message
      $this->parse_message_part($connection, $message_id, $structure, 1, $content);
    }

    imap_close($connection);
    return $content;
  }

  /**
   * Parse message part
   *
   * @param resource $connection IMAP connection
   * @param int $message_id Message ID
   * @param object $part Message part structure
   * @param string $part_number Part number
   * @param array &$content Content array to populate
   * @since 1.0.0
   */
  private function parse_message_part($connection, $message_id, $part, $part_number, &$content) {
    $data = imap_fetchbody($connection, $message_id, $part_number);

    // Decode based on encoding
    if (isset($part->encoding)) {
      switch ($part->encoding) {
        case 3: // Base64
          $data = base64_decode($data);
          break;
        case 4: // Quoted-printable
          $data = quoted_printable_decode($data);
          break;
      }
    }

    // Handle different content types
    if (isset($part->subtype)) {
      switch (strtoupper($part->subtype)) {
        case 'PLAIN':
          $content['text_body'] = $data;
          break;
        case 'HTML':
          $content['html_body'] = $data;
          break;
        default:
          // Handle attachments
          if (isset($part->disposition) && strtoupper($part->disposition) === 'ATTACHMENT') {
            $filename = 'unknown';
            if (isset($part->dparameters)) {
              foreach ($part->dparameters as $param) {
                if (strtoupper($param->attribute) === 'FILENAME') {
                  $filename = $param->value;
                  break;
                }
              }
            }
            
            $content['attachments'][] = array(
              'filename' => $filename,
              'size' => isset($part->bytes) ? $part->bytes : strlen($data),
              'type' => isset($part->subtype) ? $part->subtype : 'unknown',
              'data' => $data
            );
          }
          break;
      }
    }
  }

  /**
   * Check if message has attachments
   *
   * @param object $structure Message structure
   * @return bool
   * @since 1.0.0
   */
  private function has_attachments($structure) {
    if (isset($structure->parts) && count($structure->parts)) {
      foreach ($structure->parts as $part) {
        if (isset($part->disposition) && strtoupper($part->disposition) === 'ATTACHMENT') {
          return true;
        }
      }
    }
    return false;
  }

  /**
   * Create mailbox
   *
   * @param string $email Email address
   * @param string $password Password
   * @param string $mailbox_name Mailbox name
   * @return bool|WP_Error True on success, error on failure
   * @since 1.0.0
   */
  public function create_mailbox($email, $password, $mailbox_name) {
    $connection = $this->connect($email, $password);
    
    if (is_wp_error($connection)) {
      return $connection;
    }

    $full_mailbox = "{{localhost:993/imap/ssl/validate-cert}}{$mailbox_name}";
    $result = imap_createmailbox($connection, $full_mailbox);

    imap_close($connection);

    if (!$result) {
      return new WP_Error('mailbox_create_failed', __('Failed to create mailbox.', BAUM_MAIL_TEXT_DOMAIN));
    }

    return true;
  }

  /**
   * Delete mailbox
   *
   * @param string $email Email address
   * @param string $password Password
   * @param string $mailbox_name Mailbox name
   * @return bool|WP_Error True on success, error on failure
   * @since 1.0.0
   */
  public function delete_mailbox($email, $password, $mailbox_name) {
    $connection = $this->connect($email, $password);
    
    if (is_wp_error($connection)) {
      return $connection;
    }

    $full_mailbox = "{{localhost:993/imap/ssl/validate-cert}}{$mailbox_name}";
    $result = imap_deletemailbox($connection, $full_mailbox);

    imap_close($connection);

    if (!$result) {
      return new WP_Error('mailbox_delete_failed', __('Failed to delete mailbox.', BAUM_MAIL_TEXT_DOMAIN));
    }

    return true;
  }

  /**
   * Get IMAP server status
   *
   * @return array
   * @since 1.0.0
   */
  public function get_server_status() {
    $status = array(
      'extension_loaded' => extension_loaded('imap'),
      'server_running' => false,
      'port_993_open' => false,
      'port_143_open' => false,
      'ssl_enabled' => false,
      'version' => ''
    );

    if ($status['extension_loaded']) {
      // Check if IMAP ports are open
      $status['port_993_open'] = $this->check_port('localhost', 993);
      $status['port_143_open'] = $this->check_port('localhost', 143);
      $status['server_running'] = $status['port_993_open'] || $status['port_143_open'];
      
      // Check SSL
      $status['ssl_enabled'] = $status['port_993_open'];
    }

    return $status;
  }

  /**
   * Check if port is open
   *
   * @param string $host Host
   * @param int $port Port
   * @return bool
   * @since 1.0.0
   */
  private function check_port($host, $port) {
    $connection = @fsockopen($host, $port, $errno, $errstr, 5);
    if ($connection) {
      fclose($connection);
      return true;
    }
    return false;
  }
}

<?php
/**
 * Baum Mail Autoresponder Class
 *
 * Handles email autoresponder functionality including vacation messages,
 * out-of-office replies, and automated responses.
 *
 * @package BaumMail
 * @subpackage Autoresponder
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
  exit;
}

/**
 * BaumMail_Autoresponder Class
 *
 * @since 1.0.0
 */
class BaumMail_Autoresponder {

  /**
   * Constructor
   *
   * @since 1.0.0
   */
  public function __construct() {
    $this->init_hooks();
  }

  /**
   * Initialize hooks
   *
   * @since 1.0.0
   */
  private function init_hooks() {
    add_action('wp_ajax_baum_mail_create_autoresponder', array($this, 'ajax_create_autoresponder'));
    add_action('wp_ajax_baum_mail_update_autoresponder', array($this, 'ajax_update_autoresponder'));
    add_action('wp_ajax_baum_mail_delete_autoresponder', array($this, 'ajax_delete_autoresponder'));
    add_action('wp_ajax_baum_mail_toggle_autoresponder', array($this, 'ajax_toggle_autoresponder'));
  }

  /**
   * Create autoresponder
   *
   * @param string $email Email address
   * @param string $subject Response subject
   * @param string $message Response message
   * @param string $start_date Start date (Y-m-d H:i:s)
   * @param string $end_date End date (Y-m-d H:i:s)
   * @param array $options Additional options
   * @return int|false Autoresponder ID on success, false on failure
   * @since 1.0.0
   */
  public function create_autoresponder($email, $subject, $message, $start_date = null, $end_date = null, $options = array()) {
    global $wpdb;

    // Validate email
    if (!is_email($email)) {
      return false;
    }

    // Sanitize inputs
    $email = sanitize_email($email);
    $subject = sanitize_text_field($subject);
    $message = wp_kses_post($message);
    $start_date = $start_date ? sanitize_text_field($start_date) : current_time('mysql');
    $end_date = $end_date ? sanitize_text_field($end_date) : null;

    // Default options
    $default_options = array(
      'interval' => 86400, // 24 hours in seconds
      'max_responses' => 0, // 0 = unlimited
      'response_count' => 0,
      'exclude_domains' => array(),
      'exclude_addresses' => array(),
      'only_contacts' => false
    );
    $options = wp_parse_args($options, $default_options);

    // Get account ID
    $account_id = $this->get_account_id_by_email($email);
    if (!$account_id) {
      return false;
    }

    // Insert autoresponder
    $table_name = $wpdb->prefix . 'baum_mail_autoresponders';
    $result = $wpdb->insert(
      $table_name,
      array(
        'account_id' => $account_id,
        'email' => $email,
        'subject' => $subject,
        'message' => $message,
        'start_date' => $start_date,
        'end_date' => $end_date,
        'options' => json_encode($options),
        'active' => 1,
        'created_at' => current_time('mysql')
      ),
      array('%d', '%s', '%s', '%s', '%s', '%s', '%s', '%d', '%s')
    );

    if ($result === false) {
      return false;
    }

    $autoresponder_id = $wpdb->insert_id;

    // Generate Sieve script
    $this->generate_sieve_script($email, $autoresponder_id);

    return $autoresponder_id;
  }

  /**
   * Update autoresponder
   *
   * @param int $autoresponder_id Autoresponder ID
   * @param array $data Update data
   * @return bool Success status
   * @since 1.0.0
   */
  public function update_autoresponder($autoresponder_id, $data) {
    global $wpdb;

    $autoresponder_id = absint($autoresponder_id);
    if (!$autoresponder_id) {
      return false;
    }

    // Get current autoresponder
    $autoresponder = $this->get_autoresponder($autoresponder_id);
    if (!$autoresponder) {
      return false;
    }

    // Prepare update data
    $update_data = array();
    $update_format = array();

    if (isset($data['subject'])) {
      $update_data['subject'] = sanitize_text_field($data['subject']);
      $update_format[] = '%s';
    }

    if (isset($data['message'])) {
      $update_data['message'] = wp_kses_post($data['message']);
      $update_format[] = '%s';
    }

    if (isset($data['start_date'])) {
      $update_data['start_date'] = sanitize_text_field($data['start_date']);
      $update_format[] = '%s';
    }

    if (isset($data['end_date'])) {
      $update_data['end_date'] = sanitize_text_field($data['end_date']);
      $update_format[] = '%s';
    }

    if (isset($data['options'])) {
      $update_data['options'] = json_encode($data['options']);
      $update_format[] = '%s';
    }

    if (isset($data['active'])) {
      $update_data['active'] = absint($data['active']);
      $update_format[] = '%d';
    }

    if (empty($update_data)) {
      return false;
    }

    $update_data['updated_at'] = current_time('mysql');
    $update_format[] = '%s';

    // Update autoresponder
    $table_name = $wpdb->prefix . 'baum_mail_autoresponders';
    $result = $wpdb->update(
      $table_name,
      $update_data,
      array('id' => $autoresponder_id),
      $update_format,
      array('%d')
    );

    if ($result !== false) {
      // Regenerate Sieve script
      $this->generate_sieve_script($autoresponder->email, $autoresponder_id);
      return true;
    }

    return false;
  }

  /**
   * Delete autoresponder
   *
   * @param int $autoresponder_id Autoresponder ID
   * @return bool Success status
   * @since 1.0.0
   */
  public function delete_autoresponder($autoresponder_id) {
    global $wpdb;

    $autoresponder_id = absint($autoresponder_id);
    if (!$autoresponder_id) {
      return false;
    }

    // Get autoresponder details
    $autoresponder = $this->get_autoresponder($autoresponder_id);
    if (!$autoresponder) {
      return false;
    }

    // Delete from database
    $table_name = $wpdb->prefix . 'baum_mail_autoresponders';
    $result = $wpdb->delete(
      $table_name,
      array('id' => $autoresponder_id),
      array('%d')
    );

    if ($result !== false) {
      // Remove Sieve script
      $this->remove_sieve_script($autoresponder->email);
      return true;
    }

    return false;
  }

  /**
   * Get autoresponder by ID
   *
   * @param int $autoresponder_id Autoresponder ID
   * @return object|null Autoresponder object or null
   * @since 1.0.0
   */
  public function get_autoresponder($autoresponder_id) {
    global $wpdb;

    $autoresponder_id = absint($autoresponder_id);
    if (!$autoresponder_id) {
      return null;
    }

    $table_name = $wpdb->prefix . 'baum_mail_autoresponders';
    $autoresponder = $wpdb->get_row(
      $wpdb->prepare("SELECT * FROM $table_name WHERE id = %d", $autoresponder_id)
    );

    if ($autoresponder && $autoresponder->options) {
      $autoresponder->options = json_decode($autoresponder->options, true);
    }

    return $autoresponder;
  }

  /**
   * Get autoresponders by email
   *
   * @param string $email Email address
   * @return array Array of autoresponder objects
   * @since 1.0.0
   */
  public function get_autoresponders_by_email($email) {
    global $wpdb;

    $email = sanitize_email($email);
    if (!$email) {
      return array();
    }

    $table_name = $wpdb->prefix . 'baum_mail_autoresponders';
    $autoresponders = $wpdb->get_results(
      $wpdb->prepare("SELECT * FROM $table_name WHERE email = %s ORDER BY created_at DESC", $email)
    );

    foreach ($autoresponders as $autoresponder) {
      if ($autoresponder->options) {
        $autoresponder->options = json_decode($autoresponder->options, true);
      }
    }

    return $autoresponders;
  }

  /**
   * Get active autoresponders
   *
   * @return array Array of active autoresponder objects
   * @since 1.0.0
   */
  public function get_active_autoresponders() {
    global $wpdb;

    $table_name = $wpdb->prefix . 'baum_mail_autoresponders';
    $current_time = current_time('mysql');

    $autoresponders = $wpdb->get_results(
      $wpdb->prepare(
        "SELECT * FROM $table_name 
         WHERE active = 1 
         AND start_date <= %s 
         AND (end_date IS NULL OR end_date >= %s)
         ORDER BY email",
        $current_time,
        $current_time
      )
    );

    foreach ($autoresponders as $autoresponder) {
      if ($autoresponder->options) {
        $autoresponder->options = json_decode($autoresponder->options, true);
      }
    }

    return $autoresponders;
  }

  /**
   * Generate Sieve script for autoresponder
   *
   * @param string $email Email address
   * @param int $autoresponder_id Autoresponder ID
   * @return bool Success status
   * @since 1.0.0
   */
  private function generate_sieve_script($email, $autoresponder_id) {
    $autoresponder = $this->get_autoresponder($autoresponder_id);
    if (!$autoresponder) {
      return false;
    }

    $options = $autoresponder->options ?: array();
    $interval = isset($options['interval']) ? $options['interval'] : 86400;

    // Generate Sieve script content
    $sieve_content = "require [\"vacation\", \"date\", \"relational\"];\n\n";

    // Add date conditions if specified
    if ($autoresponder->start_date || $autoresponder->end_date) {
      $sieve_content .= "# Date conditions\n";
      
      if ($autoresponder->start_date) {
        $start_date = date('Y-m-d', strtotime($autoresponder->start_date));
        $sieve_content .= "if currentdate :value \"ge\" \"date\" \"$start_date\" {\n";
      }
      
      if ($autoresponder->end_date) {
        $end_date = date('Y-m-d', strtotime($autoresponder->end_date));
        $sieve_content .= "  if currentdate :value \"le\" \"date\" \"$end_date\" {\n";
      }
    }

    // Add exclusion conditions
    if (!empty($options['exclude_domains']) || !empty($options['exclude_addresses'])) {
      $sieve_content .= "    # Exclusion conditions\n";
      $sieve_content .= "    if not anyof (\n";
      
      $conditions = array();
      
      if (!empty($options['exclude_domains'])) {
        foreach ($options['exclude_domains'] as $domain) {
          $conditions[] = "      header :contains \"from\" \"@$domain\"";
        }
      }
      
      if (!empty($options['exclude_addresses'])) {
        foreach ($options['exclude_addresses'] as $address) {
          $conditions[] = "      header :is \"from\" \"$address\"";
        }
      }
      
      $sieve_content .= implode(",\n", $conditions) . "\n";
      $sieve_content .= "    ) {\n";
    }

    // Add vacation response
    $subject = addslashes($autoresponder->subject);
    $message = addslashes($autoresponder->message);
    
    $sieve_content .= "      vacation\n";
    $sieve_content .= "        :days $interval\n";
    $sieve_content .= "        :subject \"$subject\"\n";
    $sieve_content .= "        \"$message\";\n";

    // Close conditions
    if (!empty($options['exclude_domains']) || !empty($options['exclude_addresses'])) {
      $sieve_content .= "    }\n";
    }

    if ($autoresponder->end_date) {
      $sieve_content .= "  }\n";
    }

    if ($autoresponder->start_date) {
      $sieve_content .= "}\n";
    }

    // Write Sieve script file
    $sieve_dir = '/var/mail/vhosts/' . dirname($email) . '/' . basename($email, '@' . dirname($email));
    $sieve_file = $sieve_dir . '/.dovecot.sieve';

    if (!is_dir($sieve_dir)) {
      wp_mkdir_p($sieve_dir);
      chown($sieve_dir, 'vmail');
      chgrp($sieve_dir, 'vmail');
    }

    $result = file_put_contents($sieve_file, $sieve_content);
    if ($result !== false) {
      chown($sieve_file, 'vmail');
      chgrp($sieve_file, 'vmail');
      chmod($sieve_file, 0644);

      // Compile Sieve script
      BaumMail_Utils::execute_command("sievec $sieve_file");
      
      return true;
    }

    return false;
  }

  /**
   * Remove Sieve script for email
   *
   * @param string $email Email address
   * @return bool Success status
   * @since 1.0.0
   */
  private function remove_sieve_script($email) {
    $sieve_dir = '/var/mail/vhosts/' . dirname($email) . '/' . basename($email, '@' . dirname($email));
    $sieve_file = $sieve_dir . '/.dovecot.sieve';
    $sieve_compiled = $sieve_dir . '/.dovecot.svbin';

    $success = true;

    if (file_exists($sieve_file)) {
      $success = unlink($sieve_file) && $success;
    }

    if (file_exists($sieve_compiled)) {
      $success = unlink($sieve_compiled) && $success;
    }

    return $success;
  }

  /**
   * Get account ID by email
   *
   * @param string $email Email address
   * @return int|null Account ID or null
   * @since 1.0.0
   */
  private function get_account_id_by_email($email) {
    global $wpdb;

    $table_name = $wpdb->prefix . 'baum_mail_accounts';
    $account_id = $wpdb->get_var(
      $wpdb->prepare("SELECT id FROM $table_name WHERE email = %s", $email)
    );

    return $account_id ? absint($account_id) : null;
  }

  /**
   * AJAX handler for creating autoresponder
   *
   * @since 1.0.0
   */
  public function ajax_create_autoresponder() {
    check_ajax_referer('baum_mail_admin_nonce', 'nonce');

    if (!current_user_can('manage_options')) {
      wp_die(__('Insufficient permissions.', 'baum-mail'));
    }

    $email = sanitize_email($_POST['email']);
    $subject = sanitize_text_field($_POST['subject']);
    $message = wp_kses_post($_POST['message']);
    $start_date = sanitize_text_field($_POST['start_date']);
    $end_date = sanitize_text_field($_POST['end_date']);

    $options = array();
    if (isset($_POST['interval'])) {
      $options['interval'] = absint($_POST['interval']);
    }

    $autoresponder_id = $this->create_autoresponder($email, $subject, $message, $start_date, $end_date, $options);

    if ($autoresponder_id) {
      wp_send_json_success(__('Autoresponder created successfully.', 'baum-mail'));
    } else {
      wp_send_json_error(__('Failed to create autoresponder.', 'baum-mail'));
    }
  }

  /**
   * AJAX handler for updating autoresponder
   *
   * @since 1.0.0
   */
  public function ajax_update_autoresponder() {
    check_ajax_referer('baum_mail_admin_nonce', 'nonce');

    if (!current_user_can('manage_options')) {
      wp_die(__('Insufficient permissions.', 'baum-mail'));
    }

    $autoresponder_id = absint($_POST['id']);
    $data = array();

    if (isset($_POST['subject'])) {
      $data['subject'] = sanitize_text_field($_POST['subject']);
    }

    if (isset($_POST['message'])) {
      $data['message'] = wp_kses_post($_POST['message']);
    }

    if (isset($_POST['start_date'])) {
      $data['start_date'] = sanitize_text_field($_POST['start_date']);
    }

    if (isset($_POST['end_date'])) {
      $data['end_date'] = sanitize_text_field($_POST['end_date']);
    }

    $success = $this->update_autoresponder($autoresponder_id, $data);

    if ($success) {
      wp_send_json_success(__('Autoresponder updated successfully.', 'baum-mail'));
    } else {
      wp_send_json_error(__('Failed to update autoresponder.', 'baum-mail'));
    }
  }

  /**
   * AJAX handler for deleting autoresponder
   *
   * @since 1.0.0
   */
  public function ajax_delete_autoresponder() {
    check_ajax_referer('baum_mail_admin_nonce', 'nonce');

    if (!current_user_can('manage_options')) {
      wp_die(__('Insufficient permissions.', 'baum-mail'));
    }

    $autoresponder_id = absint($_POST['id']);
    $success = $this->delete_autoresponder($autoresponder_id);

    if ($success) {
      wp_send_json_success(__('Autoresponder deleted successfully.', 'baum-mail'));
    } else {
      wp_send_json_error(__('Failed to delete autoresponder.', 'baum-mail'));
    }
  }

  /**
   * AJAX handler for toggling autoresponder status
   *
   * @since 1.0.0
   */
  public function ajax_toggle_autoresponder() {
    check_ajax_referer('baum_mail_admin_nonce', 'nonce');

    if (!current_user_can('manage_options')) {
      wp_die(__('Insufficient permissions.', 'baum-mail'));
    }

    $autoresponder_id = absint($_POST['id']);
    $autoresponder = $this->get_autoresponder($autoresponder_id);

    if (!$autoresponder) {
      wp_send_json_error(__('Autoresponder not found.', 'baum-mail'));
    }

    $new_status = $autoresponder->active ? 0 : 1;
    $success = $this->update_autoresponder($autoresponder_id, array('active' => $new_status));

    if ($success) {
      $message = $new_status ? __('Autoresponder activated.', 'baum-mail') : __('Autoresponder deactivated.', 'baum-mail');
      wp_send_json_success($message);
    } else {
      wp_send_json_error(__('Failed to toggle autoresponder status.', 'baum-mail'));
    }
  }
}

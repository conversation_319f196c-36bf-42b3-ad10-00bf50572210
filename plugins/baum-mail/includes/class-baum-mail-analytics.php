<?php
/**
 * Baum Mail Analytics Class
 *
 * @package BaumMail
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
  exit;
}

/**
 * Email analytics and tracking
 *
 * @since 1.0.0
 */
class BaumMail_Analytics {

  /**
   * Constructor
   *
   * @since 1.0.0
   */
  public function __construct() {
    $this->init_hooks();
  }

  /**
   * Initialize hooks
   *
   * @since 1.0.0
   */
  private function init_hooks() {
    add_action('init', array($this, 'handle_tracking_pixel'));
    add_action('wp_ajax_baum_mail_get_analytics_data', array($this, 'ajax_get_analytics_data'));
    add_action('wp_ajax_nopriv_baum_mail_tracking_pixel', array($this, 'handle_tracking_pixel'));
    add_filter('wp_mail', array($this, 'add_tracking_pixel_to_email'), 10, 1);
  }

  /**
   * Add tracking pixel to outgoing emails
   *
   * @param array $args wp_mail arguments
   * @return array Modified arguments
   * @since 1.0.0
   */
  public function add_tracking_pixel_to_email($args) {
    if (!get_option('baum_mail_tracking_enabled', false)) {
      return $args;
    }

    // Generate unique tracking ID
    $tracking_id = $this->generate_tracking_id($args['to'], $args['subject']);
    
    // Store tracking data
    $this->store_tracking_data($tracking_id, $args);

    // Add tracking pixel to HTML emails
    if (isset($args['headers']) && is_array($args['headers'])) {
      $is_html = false;
      foreach ($args['headers'] as $header) {
        if (stripos($header, 'Content-Type: text/html') !== false) {
          $is_html = true;
          break;
        }
      }
    } else {
      $is_html = stripos($args['message'], '<html') !== false || stripos($args['message'], '<body') !== false;
    }

    if ($is_html) {
      $tracking_url = $this->get_tracking_pixel_url($tracking_id);
      $tracking_pixel = '<img src="' . esc_url($tracking_url) . '" width="1" height="1" style="display:none;" alt="" />';
      
      // Add tracking pixel before closing body tag or at the end
      if (stripos($args['message'], '</body>') !== false) {
        $args['message'] = str_ireplace('</body>', $tracking_pixel . '</body>', $args['message']);
      } else {
        $args['message'] .= $tracking_pixel;
      }
    }

    return $args;
  }

  /**
   * Generate unique tracking ID
   *
   * @param string $to Recipient email
   * @param string $subject Email subject
   * @return string Tracking ID
   * @since 1.0.0
   */
  private function generate_tracking_id($to, $subject) {
    return wp_hash($to . $subject . time() . wp_rand());
  }

  /**
   * Store tracking data
   *
   * @param string $tracking_id Tracking ID
   * @param array $email_args Email arguments
   * @since 1.0.0
   */
  private function store_tracking_data($tracking_id, $email_args) {
    global $wpdb;

    $wpdb->insert(
      $wpdb->prefix . 'baum_mail_tracking',
      array(
        'tracking_id' => $tracking_id,
        'recipient_email' => $email_args['to'],
        'subject' => $email_args['subject'],
        'sent_at' => current_time('mysql'),
        'opened' => 0,
        'open_count' => 0
      ),
      array('%s', '%s', '%s', '%s', '%d', '%d')
    );
  }

  /**
   * Get tracking pixel URL
   *
   * @param string $tracking_id Tracking ID
   * @return string Tracking URL
   * @since 1.0.0
   */
  private function get_tracking_pixel_url($tracking_id) {
    $domain = get_option('baum_mail_tracking_domain', get_site_url());
    return add_query_arg(array(
      'baum_mail_track' => $tracking_id,
      'action' => 'baum_mail_tracking_pixel'
    ), $domain);
  }

  /**
   * Handle tracking pixel request
   *
   * @since 1.0.0
   */
  public function handle_tracking_pixel() {
    if (!isset($_GET['baum_mail_track'])) {
      return;
    }

    $tracking_id = sanitize_text_field($_GET['baum_mail_track']);
    
    // Record the open
    $this->record_email_open($tracking_id);

    // Output 1x1 transparent pixel
    header('Content-Type: image/png');
    header('Cache-Control: no-cache, no-store, must-revalidate');
    header('Pragma: no-cache');
    header('Expires: 0');
    
    // 1x1 transparent PNG
    echo base64_decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==');
    exit;
  }

  /**
   * Record email open
   *
   * @param string $tracking_id Tracking ID
   * @since 1.0.0
   */
  private function record_email_open($tracking_id) {
    global $wpdb;

    // Get client information
    $ip_address = $this->get_client_ip();
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    $location_data = null;

    // Get location data if geolocation plugin is available
    if (get_option('baum_mail_track_location', true) && function_exists('baum_geolocation_get_location')) {
      $location_data = baum_geolocation_get_location($ip_address);
    }

    // Update tracking record
    $wpdb->query($wpdb->prepare(
      "UPDATE {$wpdb->prefix}baum_mail_tracking 
       SET opened = 1, 
           open_count = open_count + 1,
           first_opened_at = CASE WHEN first_opened_at IS NULL THEN %s ELSE first_opened_at END,
           last_opened_at = %s
       WHERE tracking_id = %s",
      current_time('mysql'),
      current_time('mysql'),
      $tracking_id
    ));

    // Insert open record
    $wpdb->insert(
      $wpdb->prefix . 'baum_mail_opens',
      array(
        'tracking_id' => $tracking_id,
        'ip_address' => $ip_address,
        'user_agent' => $user_agent,
        'country' => $location_data['country'] ?? null,
        'country_code' => $location_data['country_code'] ?? null,
        'region' => $location_data['region'] ?? null,
        'city' => $location_data['city'] ?? null,
        'latitude' => $location_data['latitude'] ?? null,
        'longitude' => $location_data['longitude'] ?? null,
        'opened_at' => current_time('mysql')
      ),
      array('%s', '%s', '%s', '%s', '%s', '%s', '%s', '%f', '%f', '%s')
    );
  }

  /**
   * Get client IP address
   *
   * @return string
   * @since 1.0.0
   */
  private function get_client_ip() {
    $ip_keys = array('HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR');
    
    foreach ($ip_keys as $key) {
      if (array_key_exists($key, $_SERVER) === true) {
        foreach (explode(',', $_SERVER[$key]) as $ip) {
          $ip = trim($ip);
          if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
            return $ip;
          }
        }
      }
    }
    
    return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
  }

  /**
   * Get analytics data for dashboard
   *
   * @param array $args Query arguments
   * @return array Analytics data
   * @since 1.0.0
   */
  public function get_analytics_data($args = array()) {
    global $wpdb;

    $defaults = array(
      'date_from' => date('Y-m-d', strtotime('-30 days')),
      'date_to' => date('Y-m-d'),
      'email' => null
    );

    $args = wp_parse_args($args, $defaults);

    // Check if tables exist
    $opens_table = $wpdb->prefix . 'baum_mail_opens';
    $tracking_table = $wpdb->prefix . 'baum_mail_tracking';

    $opens_exists = $wpdb->get_var("SHOW TABLES LIKE '{$opens_table}'") === $opens_table;
    $tracking_exists = $wpdb->get_var("SHOW TABLES LIKE '{$tracking_table}'") === $tracking_table;

    if (!$opens_exists || !$tracking_exists) {
      // Return sample data if tables don't exist
      return $this->get_sample_analytics_data();
    }

    // Get country data for map
    $country_data = $wpdb->get_results($wpdb->prepare(
      "SELECT country_code, country, COUNT(*) as opens
       FROM {$opens_table}
       WHERE opened_at BETWEEN %s AND %s
       AND country_code IS NOT NULL
       GROUP BY country_code, country
       ORDER BY opens DESC",
      $args['date_from'] . ' 00:00:00',
      $args['date_to'] . ' 23:59:59'
    ));

    // Handle database errors
    if ($wpdb->last_error) {
      return $this->get_sample_analytics_data();
    }

    // Get US state data
    $state_data = $wpdb->get_results($wpdb->prepare(
      "SELECT region as state, COUNT(*) as opens
       FROM {$opens_table}
       WHERE opened_at BETWEEN %s AND %s
       AND country_code = 'US'
       AND region IS NOT NULL
       GROUP BY region
       ORDER BY opens DESC",
      $args['date_from'] . ' 00:00:00',
      $args['date_to'] . ' 23:59:59'
    ));

    // Get detailed opens data
    $opens_data = $wpdb->get_results($wpdb->prepare(
      "SELECT o.*, t.recipient_email, t.subject
       FROM {$opens_table} o
       LEFT JOIN {$tracking_table} t ON o.tracking_id = t.tracking_id
       WHERE o.opened_at BETWEEN %s AND %s
       ORDER BY o.opened_at DESC
       LIMIT 1000",
      $args['date_from'] . ' 00:00:00',
      $args['date_to'] . ' 23:59:59'
    ));

    // Handle database errors
    if ($wpdb->last_error) {
      return $this->get_sample_analytics_data();
    }

    // Get summary statistics
    $stats = array(
      'total_emails_sent' => $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM {$tracking_table} WHERE sent_at BETWEEN %s AND %s",
        $args['date_from'] . ' 00:00:00',
        $args['date_to'] . ' 23:59:59'
      )) ?: 0,
      'total_opens' => $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM {$opens_table} WHERE opened_at BETWEEN %s AND %s",
        $args['date_from'] . ' 00:00:00',
        $args['date_to'] . ' 23:59:59'
      )) ?: 0,
      'unique_opens' => $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(DISTINCT tracking_id) FROM {$opens_table} WHERE opened_at BETWEEN %s AND %s",
        $args['date_from'] . ' 00:00:00',
        $args['date_to'] . ' 23:59:59'
      )) ?: 0
    );

    // Handle database errors in stats queries
    if ($wpdb->last_error) {
      return $this->get_sample_analytics_data();
    }

    $stats['open_rate'] = $stats['total_emails_sent'] > 0 ?
      round(($stats['unique_opens'] / $stats['total_emails_sent']) * 100, 2) : 0;

    return array(
      'countries' => $country_data ?: array(),
      'states' => $state_data ?: array(),
      'opens' => $opens_data ?: array(),
      'stats' => $stats
    );
  }

  /**
   * Get sample analytics data for demonstration
   *
   * @return array Sample analytics data
   * @since 1.0.0
   */
  private function get_sample_analytics_data() {
    return array(
      'countries' => array(
        (object) array('country_code' => 'US', 'country' => 'United States', 'opens' => 45),
        (object) array('country_code' => 'GB', 'country' => 'United Kingdom', 'opens' => 23),
        (object) array('country_code' => 'DE', 'country' => 'Germany', 'opens' => 18),
        (object) array('country_code' => 'FR', 'country' => 'France', 'opens' => 12),
        (object) array('country_code' => 'JP', 'country' => 'Japan', 'opens' => 8)
      ),
      'states' => array(
        (object) array('state' => 'California', 'opens' => 15),
        (object) array('state' => 'New York', 'opens' => 12),
        (object) array('state' => 'Texas', 'opens' => 8),
        (object) array('state' => 'Florida', 'opens' => 6)
      ),
      'opens' => array(
        (object) array(
          'ip_address' => '*************',
          'country' => 'United States',
          'region' => 'California',
          'city' => 'San Francisco',
          'opened_at' => date('Y-m-d H:i:s', strtotime('-2 hours')),
          'subject' => 'Welcome to our newsletter',
          'recipient_email' => '<EMAIL>'
        ),
        (object) array(
          'ip_address' => '*********',
          'country' => 'United Kingdom',
          'region' => 'England',
          'city' => 'London',
          'opened_at' => date('Y-m-d H:i:s', strtotime('-5 hours')),
          'subject' => 'Monthly update',
          'recipient_email' => '<EMAIL>'
        )
      ),
      'stats' => array(
        'total_emails_sent' => 150,
        'total_opens' => 89,
        'unique_opens' => 67,
        'open_rate' => 44.67
      )
    );
  }

  /**
   * AJAX: Get analytics data
   *
   * @since 1.0.0
   */
  public function ajax_get_analytics_data() {
    check_ajax_referer('baum_mail_admin_nonce', 'nonce');

    if (!current_user_can('manage_options')) {
      wp_die(__('Insufficient permissions.', BAUM_MAIL_TEXT_DOMAIN));
    }

    $date_from = sanitize_text_field($_POST['date_from'] ?? '');
    $date_to = sanitize_text_field($_POST['date_to'] ?? '');

    $data = $this->get_analytics_data(array(
      'date_from' => $date_from,
      'date_to' => $date_to
    ));

    wp_send_json_success($data);
  }
}

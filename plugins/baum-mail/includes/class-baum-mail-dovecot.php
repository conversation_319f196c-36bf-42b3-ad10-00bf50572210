<?php
/**
 * Baum Mail Dovecot Integration Class
 *
 * @package BaumMail
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
  exit;
}

/**
 * Dovecot IMAP/POP3 server integration
 *
 * @since 1.0.0
 */
class BaumMail_Dovecot {

  /**
   * Dovecot configuration path
   *
   * @var string
   * @since 1.0.0
   */
  private $config_path;

  /**
   * Constructor
   *
   * @since 1.0.0
   */
  public function __construct() {
    $this->config_path = get_option('baum_mail_dovecot_config_path', '/etc/dovecot');
    $this->init_hooks();
  }

  /**
   * Initialize hooks
   *
   * @since 1.0.0
   */
  private function init_hooks() {
    add_action('baum_mail_account_created', array($this, 'create_mailbox'));
    add_action('baum_mail_account_updated', array($this, 'update_mailbox'));
    add_action('baum_mail_account_deleted', array($this, 'delete_mailbox'));
  }

  /**
   * Get Dovecot status
   *
   * @return array
   * @since 1.0.0
   */
  public function get_status() {
    $status = array(
      'running' => false,
      'version' => '',
      'config_valid' => false,
      'active_connections' => 0,
      'last_check' => current_time('mysql')
    );

    // Check if Dovecot is running
    $output = $this->execute_command('systemctl is-active dovecot');
    $status['running'] = (trim($output) === 'active');

    // Get Dovecot version
    $version_output = $this->execute_command('dovecot --version');
    $status['version'] = trim($version_output);

    // Check configuration
    $config_check = $this->execute_command('dovecot -n 2>&1 | grep -i error');
    $status['config_valid'] = empty(trim($config_check));

    // Get active connections
    $connections = $this->execute_command('doveadm who | wc -l');
    $status['active_connections'] = intval(trim($connections));

    return $status;
  }

  /**
   * Reload Dovecot configuration
   *
   * @return bool|WP_Error
   * @since 1.0.0
   */
  public function reload_config() {
    $output = $this->execute_command('systemctl reload dovecot 2>&1');
    
    if (empty(trim($output)) || strpos($output, 'Reloaded') !== false) {
      return true;
    }

    return new WP_Error('reload_failed', __('Failed to reload Dovecot configuration.', BAUM_MAIL_TEXT_DOMAIN));
  }

  /**
   * Create mailbox for user
   *
   * @param int $account_id Account ID
   * @since 1.0.0
   */
  public function create_mailbox($account_id) {
    global $wpdb;

    $account = $wpdb->get_row($wpdb->prepare(
      "SELECT email FROM {$wpdb->prefix}baum_mail_accounts WHERE id = %d",
      $account_id
    ));

    if (!$account) {
      return;
    }

    // Create mailbox directory structure
    $mailbox_path = '/var/mail/vhosts/' . $account->email;
    $this->execute_command("mkdir -p {$mailbox_path}");
    $this->execute_command("chown -R vmail:vmail {$mailbox_path}");
    $this->execute_command("chmod -R 750 {$mailbox_path}");

    // Create default folders
    $folders = array('INBOX', 'Sent', 'Drafts', 'Trash', 'Spam');
    foreach ($folders as $folder) {
      $folder_path = $mailbox_path . '/.' . $folder;
      $this->execute_command("mkdir -p {$folder_path}");
    }

    error_log("Created mailbox for: {$account->email}");
  }

  /**
   * Update mailbox settings
   *
   * @param int $account_id Account ID
   * @since 1.0.0
   */
  public function update_mailbox($account_id) {
    global $wpdb;

    $account = $wpdb->get_row($wpdb->prepare(
      "SELECT email, quota FROM {$wpdb->prefix}baum_mail_accounts WHERE id = %d",
      $account_id
    ));

    if (!$account) {
      return;
    }

    // Update quota if using doveadm
    if ($account->quota > 0) {
      $quota_mb = $account->quota / (1024 * 1024);
      $this->execute_command("doveadm quota set -u {$account->email} storage {$quota_mb}M");
    }

    error_log("Updated mailbox for: {$account->email}");
  }

  /**
   * Delete mailbox
   *
   * @param string $email Email address
   * @since 1.0.0
   */
  public function delete_mailbox($email) {
    // Archive mailbox before deletion
    $archive_path = '/var/mail/archive/' . date('Y-m-d') . '/' . $email;
    $mailbox_path = '/var/mail/vhosts/' . $email;

    $this->execute_command("mkdir -p " . dirname($archive_path));
    $this->execute_command("mv {$mailbox_path} {$archive_path}");

    error_log("Archived mailbox for: {$email}");
  }

  /**
   * Generate dovecot.conf configuration
   *
   * @return string
   * @since 1.0.0
   */
  public function generate_main_config() {
    $config = array(
      '# Baum Mail Dovecot Configuration',
      '# Generated on ' . current_time('mysql'),
      '',
      '# Basic settings',
      'protocols = imap pop3 lmtp',
      'listen = *, ::',
      'base_dir = /var/run/dovecot/',
      'instance_name = dovecot',
      '',
      '# SSL settings',
      'ssl = required',
      'ssl_cert = <' . get_option('baum_mail_ssl_cert_path', '/etc/ssl/certs') . '/mail.crt',
      'ssl_key = <' . get_option('baum_mail_ssl_key_path', '/etc/ssl/private') . '/mail.key',
      'ssl_protocols = !SSLv2 !SSLv3',
      'ssl_cipher_list = ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES128-GCM-SHA256',
      'ssl_prefer_server_ciphers = yes',
      'ssl_dh_parameters_length = 2048',
      '',
      '# Authentication',
      'auth_mechanisms = plain login',
      'auth_username_format = %Lu',
      '',
      '# Mail location',
      'mail_location = maildir:/var/mail/vhosts/%d/%n',
      'mail_privileged_group = vmail',
      'mail_uid = vmail',
      'mail_gid = vmail',
      '',
      '# Namespace settings',
      'namespace inbox {',
      '  type = private',
      '  separator = .',
      '  prefix = INBOX.',
      '  location =',
      '  inbox = yes',
      '  hidden = no',
      '  list = yes',
      '  subscriptions = yes',
      '}',
      '',
      '# Service settings',
      'service imap-login {',
      '  inet_listener imap {',
      '    port = 143',
      '  }',
      '  inet_listener imaps {',
      '    port = 993',
      '    ssl = yes',
      '  }',
      '}',
      '',
      'service pop3-login {',
      '  inet_listener pop3 {',
      '    port = 110',
      '  }',
      '  inet_listener pop3s {',
      '    port = 995',
      '    ssl = yes',
      '  }',
      '}',
      '',
      'service lmtp {',
      '  unix_listener /var/spool/postfix/private/dovecot-lmtp {',
      '    mode = 0600',
      '    user = postfix',
      '    group = postfix',
      '  }',
      '}',
      '',
      'service auth {',
      '  unix_listener /var/spool/postfix/private/auth {',
      '    mode = 0666',
      '    user = postfix',
      '    group = postfix',
      '  }',
      '  unix_listener auth-userdb {',
      '    mode = 0600',
      '    user = vmail',
      '    group = vmail',
      '  }',
      '  user = dovecot',
      '}',
      '',
      '# Protocol settings',
      'protocol imap {',
      '  mail_max_userip_connections = 20',
      '  imap_idle_notify_interval = 2 mins',
      '}',
      '',
      'protocol pop3 {',
      '  mail_max_userip_connections = 10',
      '  pop3_uidl_format = %08Xu%08Xv',
      '}',
      '',
      'protocol lmtp {',
      '  postmaster_address = postmaster@' . parse_url(get_option('siteurl'), PHP_URL_HOST),
      '}',
      '',
      '# Logging',
      'log_path = /var/log/dovecot/dovecot.log',
      'info_log_path = /var/log/dovecot/dovecot-info.log',
      'debug_log_path = /var/log/dovecot/dovecot-debug.log',
      'syslog_facility = mail',
      '',
      '# Include additional configurations',
      '!include conf.d/*.conf',
      '!include_try local.conf'
    );

    return implode("\n", $config);
  }

  /**
   * Generate auth configuration
   *
   * @return string
   * @since 1.0.0
   */
  public function generate_auth_config() {
    global $wpdb;

    $config = array(
      '# Baum Mail Dovecot Auth Configuration',
      '# Generated on ' . current_time('mysql'),
      '',
      '# Database authentication',
      'passdb {',
      '  driver = sql',
      '  args = /etc/dovecot/dovecot-sql.conf.ext',
      '}',
      '',
      'userdb {',
      '  driver = sql',
      '  args = /etc/dovecot/dovecot-sql.conf.ext',
      '}',
      '',
      '# SQL configuration',
      'driver = mysql',
      'connect = host=' . DB_HOST . ' dbname=' . DB_NAME . ' user=' . DB_USER . ' password=' . DB_PASSWORD,
      '',
      '# Password query',
      'password_query = SELECT email as user, password FROM ' . $wpdb->prefix . 'baum_mail_accounts WHERE email = \'%u\' AND active = 1',
      '',
      '# User query',
      'user_query = SELECT email as user, \'vmail\' as uid, \'vmail\' as gid, \'/var/mail/vhosts/%d/%n\' as home, CONCAT(\'maildir:/var/mail/vhosts/\', email) as mail FROM ' . $wpdb->prefix . 'baum_mail_accounts WHERE email = \'%u\' AND active = 1',
      '',
      '# Iterate query for doveadm',
      'iterate_query = SELECT email as user FROM ' . $wpdb->prefix . 'baum_mail_accounts WHERE active = 1'
    );

    return implode("\n", $config);
  }

  /**
   * Get mailbox statistics
   *
   * @param string $email Email address
   * @return array
   * @since 1.0.0
   */
  public function get_mailbox_stats($email) {
    $stats = array(
      'messages' => 0,
      'size' => 0,
      'quota_used' => 0,
      'quota_limit' => 0,
      'last_login' => null
    );

    // Get message count and size
    $doveadm_output = $this->execute_command("doveadm mailbox status -u {$email} messages vsize INBOX");
    if (preg_match('/messages=(\d+).*vsize=(\d+)/', $doveadm_output, $matches)) {
      $stats['messages'] = intval($matches[1]);
      $stats['size'] = intval($matches[2]);
    }

    // Get quota information
    $quota_output = $this->execute_command("doveadm quota get -u {$email}");
    if (preg_match('/storage\s+(\d+)\s+(\d+)/', $quota_output, $matches)) {
      $stats['quota_used'] = intval($matches[1]);
      $stats['quota_limit'] = intval($matches[2]);
    }

    // Get last login
    $login_output = $this->execute_command("doveadm log find -u {$email} | grep 'Login:' | tail -1");
    if (preg_match('/(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})/', $login_output, $matches)) {
      $stats['last_login'] = $matches[1];
    }

    return $stats;
  }

  /**
   * Execute system command safely
   *
   * @param string $command Command to execute
   * @return string Command output
   * @since 1.0.0
   */
  private function execute_command($command) {
    // Sanitize command
    $command = escapeshellcmd($command);
    
    // Execute and capture output
    ob_start();
    $return_var = 0;
    $output = shell_exec($command . ' 2>&1');
    ob_end_clean();

    return $output ?: '';
  }

  /**
   * Test Dovecot configuration
   *
   * @return array
   * @since 1.0.0
   */
  public function test_configuration() {
    $tests = array();

    // Test configuration syntax
    $config_test = $this->execute_command('dovecot -n 2>&1 | grep -i error');
    $tests['config_syntax'] = array(
      'name' => __('Configuration Syntax', BAUM_MAIL_TEXT_DOMAIN),
      'status' => empty(trim($config_test)),
      'message' => empty(trim($config_test)) ? __('Valid', BAUM_MAIL_TEXT_DOMAIN) : $config_test
    );

    // Test service status
    $service_test = $this->execute_command('systemctl is-active dovecot');
    $tests['service_status'] = array(
      'name' => __('Service Status', BAUM_MAIL_TEXT_DOMAIN),
      'status' => (trim($service_test) === 'active'),
      'message' => ucfirst(trim($service_test))
    );

    // Test IMAP port
    $imap_test = $this->execute_command('netstat -tlnp | grep :143');
    $tests['imap_port'] = array(
      'name' => __('IMAP Port (143)', BAUM_MAIL_TEXT_DOMAIN),
      'status' => !empty(trim($imap_test)),
      'message' => !empty(trim($imap_test)) ? __('Listening', BAUM_MAIL_TEXT_DOMAIN) : __('Not listening', BAUM_MAIL_TEXT_DOMAIN)
    );

    // Test IMAPS port
    $imaps_test = $this->execute_command('netstat -tlnp | grep :993');
    $tests['imaps_port'] = array(
      'name' => __('IMAPS Port (993)', BAUM_MAIL_TEXT_DOMAIN),
      'status' => !empty(trim($imaps_test)),
      'message' => !empty(trim($imaps_test)) ? __('Listening', BAUM_MAIL_TEXT_DOMAIN) : __('Not listening', BAUM_MAIL_TEXT_DOMAIN)
    );

    return $tests;
  }
}

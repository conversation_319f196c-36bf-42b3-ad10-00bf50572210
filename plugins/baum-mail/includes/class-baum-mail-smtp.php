<?php
/**
 * <PERSON>um Mail SMTP Class
 *
 * @package BaumMail
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
  exit;
}

/**
 * SMTP server management and operations
 *
 * @since 1.0.0
 */
class BaumMail_SMTP {

  /**
   * Constructor
   *
   * @since 1.0.0
   */
  public function __construct() {
    $this->init_hooks();
  }

  /**
   * Initialize hooks
   *
   * @since 1.0.0
   */
  private function init_hooks() {
    add_action('phpmailer_init', array($this, 'configure_phpmailer'));
  }

  /**
   * Configure PHPMailer to use our SMTP server
   *
   * @param PHPMailer $phpmailer PHPMailer instance
   * @since 1.0.0
   */
  public function configure_phpmailer($phpmailer) {
    if (get_option('baum_mail_use_smtp', false)) {
      $phpmailer->isSMTP();
      $phpmailer->Host = get_option('baum_mail_smtp_host', 'localhost');
      $phpmailer->Port = get_option('baum_mail_smtp_port', 587);
      $phpmailer->SMTPSecure = get_option('baum_mail_smtp_security', 'tls');
      $phpmailer->SMTPAuth = get_option('baum_mail_smtp_auth', true);
      $phpmailer->Username = get_option('baum_mail_smtp_username', '');
      $phpmailer->Password = get_option('baum_mail_smtp_password', '');
      $phpmailer->SMTPDebug = get_option('baum_mail_smtp_debug', 0);
    }
  }

  /**
   * Send email via SMTP
   *
   * @param string $to Recipient email
   * @param string $subject Email subject
   * @param string $message Email message
   * @param array $headers Email headers
   * @param array $attachments Email attachments
   * @return bool|WP_Error True on success, error on failure
   * @since 1.0.0
   */
  public function send_email($to, $subject, $message, $headers = array(), $attachments = array()) {
    // Check daily send limits
    $sender_email = $this->extract_sender_from_headers($headers);
    if ($sender_email && !$this->check_send_limits($sender_email)) {
      return new WP_Error('send_limit_exceeded', __('Daily send limit exceeded.', BAUM_MAIL_TEXT_DOMAIN));
    }

    // Encrypt message if recipient has encryption enabled
    $encryption = baum_mail()->get_component('encryption');
    if ($encryption && $encryption->is_encryption_available()) {
      $encrypted_message = $encryption->encrypt_message($message, $to);
      if (!is_wp_error($encrypted_message)) {
        $message = $encrypted_message;
        $headers[] = 'X-Baum-Mail-Encrypted: true';
      }
    }

    // Send email using WordPress wp_mail
    $result = wp_mail($to, $subject, $message, $headers, $attachments);

    if ($result) {
      // Update send count
      if ($sender_email) {
        $this->increment_send_count($sender_email);
      }
      
      // Log successful send
      $this->log_email_sent($sender_email, $to, $subject);
      
      return true;
    } else {
      return new WP_Error('send_failed', __('Failed to send email.', BAUM_MAIL_TEXT_DOMAIN));
    }
  }

  /**
   * Test SMTP connection
   *
   * @param string $host SMTP host
   * @param int $port SMTP port
   * @param string $username Username
   * @param string $password Password
   * @param string $security Security type (tls, ssl, none)
   * @return bool|WP_Error True on success, error on failure
   * @since 1.0.0
   */
  public function test_connection($host = null, $port = null, $username = null, $password = null, $security = null) {
    // Use provided values or defaults
    $host = $host ?: get_option('baum_mail_smtp_host', 'localhost');
    $port = $port ?: get_option('baum_mail_smtp_port', 587);
    $username = $username ?: get_option('baum_mail_smtp_username', '');
    $password = $password ?: get_option('baum_mail_smtp_password', '');
    $security = $security ?: get_option('baum_mail_smtp_security', 'tls');

    try {
      // Create PHPMailer instance for testing
      require_once ABSPATH . WPINC . '/PHPMailer/PHPMailer.php';
      require_once ABSPATH . WPINC . '/PHPMailer/SMTP.php';
      
      $mailer = new PHPMailer\PHPMailer\PHPMailer(true);
      $mailer->isSMTP();
      $mailer->Host = $host;
      $mailer->Port = $port;
      $mailer->SMTPAuth = !empty($username);
      $mailer->Username = $username;
      $mailer->Password = $password;
      
      switch ($security) {
        case 'ssl':
          $mailer->SMTPSecure = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_SMTPS;
          break;
        case 'tls':
          $mailer->SMTPSecure = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_STARTTLS;
          break;
        default:
          $mailer->SMTPSecure = '';
      }

      // Test connection
      $mailer->smtpConnect();
      $mailer->smtpClose();

      return true;

    } catch (Exception $e) {
      return new WP_Error('smtp_connection_failed', $e->getMessage());
    }
  }

  /**
   * Get SMTP server status
   *
   * @return array
   * @since 1.0.0
   */
  public function get_server_status() {
    $status = array(
      'enabled' => get_option('baum_mail_use_smtp', false),
      'host' => get_option('baum_mail_smtp_host', 'localhost'),
      'port' => get_option('baum_mail_smtp_port', 587),
      'security' => get_option('baum_mail_smtp_security', 'tls'),
      'auth_enabled' => get_option('baum_mail_smtp_auth', true),
      'connection_test' => false,
      'port_open' => false,
      'postfix_running' => false
    );

    // Test port connectivity
    $status['port_open'] = $this->check_port($status['host'], $status['port']);
    
    // Test SMTP connection
    if ($status['enabled'] && $status['port_open']) {
      $connection_test = $this->test_connection();
      $status['connection_test'] = !is_wp_error($connection_test);
    }

    // Check if Postfix is running
    $status['postfix_running'] = $this->is_postfix_running();

    return $status;
  }

  /**
   * Check daily send limits for user
   *
   * @param string $email Email address
   * @return bool
   * @since 1.0.0
   */
  private function check_send_limits($email) {
    global $wpdb;

    $account = $wpdb->get_row($wpdb->prepare(
      "SELECT daily_send_count, daily_send_limit, last_reset_date FROM {$wpdb->prefix}baum_mail_accounts WHERE email = %s",
      $email
    ));

    if (!$account) {
      return false; // Account not found
    }

    // Reset daily count if it's a new day
    $today = date('Y-m-d');
    if ($account->last_reset_date !== $today) {
      $wpdb->update(
        $wpdb->prefix . 'baum_mail_accounts',
        array(
          'daily_send_count' => 0,
          'last_reset_date' => $today
        ),
        array('email' => $email),
        array('%d', '%s'),
        array('%s')
      );
      $account->daily_send_count = 0;
    }

    // Check if limit exceeded
    return $account->daily_send_count < $account->daily_send_limit;
  }

  /**
   * Increment send count for user
   *
   * @param string $email Email address
   * @since 1.0.0
   */
  private function increment_send_count($email) {
    global $wpdb;

    $wpdb->query($wpdb->prepare(
      "UPDATE {$wpdb->prefix}baum_mail_accounts SET daily_send_count = daily_send_count + 1 WHERE email = %s",
      $email
    ));
  }

  /**
   * Extract sender email from headers
   *
   * @param array $headers Email headers
   * @return string|null
   * @since 1.0.0
   */
  private function extract_sender_from_headers($headers) {
    foreach ($headers as $header) {
      if (strpos(strtolower($header), 'from:') === 0) {
        preg_match('/from:\s*(.+@.+)/i', $header, $matches);
        if (isset($matches[1])) {
          return trim($matches[1], '<>');
        }
      }
    }
    return null;
  }

  /**
   * Log email sent
   *
   * @param string $from Sender email
   * @param string $to Recipient email
   * @param string $subject Email subject
   * @since 1.0.0
   */
  private function log_email_sent($from, $to, $subject) {
    global $wpdb;

    $wpdb->insert(
      $wpdb->prefix . 'baum_mail_logs',
      array(
        'action' => 'email_sent',
        'message' => "Email sent from {$from} to {$to}",
        'metadata' => json_encode(array(
          'from' => $from,
          'to' => $to,
          'subject' => $subject
        )),
        'user_id' => get_current_user_id(),
        'ip_address' => $this->get_client_ip(),
        'created_at' => current_time('mysql')
      ),
      array('%s', '%s', '%s', '%d', '%s', '%s')
    );
  }

  /**
   * Get client IP address
   *
   * @return string
   * @since 1.0.0
   */
  private function get_client_ip() {
    $ip_keys = array('HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR');
    
    foreach ($ip_keys as $key) {
      if (array_key_exists($key, $_SERVER) === true) {
        foreach (explode(',', $_SERVER[$key]) as $ip) {
          $ip = trim($ip);
          if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
            return $ip;
          }
        }
      }
    }
    
    return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
  }

  /**
   * Check if port is open
   *
   * @param string $host Host
   * @param int $port Port
   * @return bool
   * @since 1.0.0
   */
  private function check_port($host, $port) {
    $connection = @fsockopen($host, $port, $errno, $errstr, 5);
    if ($connection) {
      fclose($connection);
      return true;
    }
    return false;
  }

  /**
   * Check if Postfix is running
   *
   * @return bool
   * @since 1.0.0
   */
  private function is_postfix_running() {
    $output = shell_exec('systemctl is-active postfix 2>/dev/null');
    return $output && trim($output) === 'active';
  }

  /**
   * Get SMTP queue status
   *
   * @return array
   * @since 1.0.0
   */
  public function get_queue_status() {
    $status = array(
      'active_queue' => 0,
      'deferred_queue' => 0,
      'hold_queue' => 0,
      'total_queue' => 0
    );

    // Get Postfix queue status
    $output = shell_exec('postqueue -p 2>/dev/null');
    if ($output) {
      // Parse queue output
      $lines = explode("\n", $output);
      foreach ($lines as $line) {
        if (strpos($line, 'Kbytes') !== false) {
          preg_match('/(\d+)\s+Kbytes\s+in\s+(\d+)\s+Request/', $line, $matches);
          if (isset($matches[2])) {
            $status['total_queue'] = intval($matches[2]);
          }
        }
      }
    }

    return $status;
  }

  /**
   * Flush mail queue
   *
   * @return bool|WP_Error
   * @since 1.0.0
   */
  public function flush_queue() {
    $output = shell_exec('postqueue -f 2>&1');
    
    if ($output === null) {
      return new WP_Error('queue_flush_failed', __('Failed to flush mail queue.', BAUM_MAIL_TEXT_DOMAIN));
    }

    // Log the action
    baum_mail()->get_component('core')->log_action('queue_flushed', 'Mail queue flushed manually');

    return true;
  }
}

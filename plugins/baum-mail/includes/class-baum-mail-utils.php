<?php
/**
 * Baum Mail Utilities Class
 *
 * @package BaumMail
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
  exit;
}

/**
 * Utility functions and CLI wrappers
 *
 * @since 1.0.0
 */
class BaumMail_Utils {

  /**
   * Execute command safely with proper error handling
   *
   * @param string $command Command to execute
   * @param array $args Command arguments
   * @param bool $return_code Whether to return exit code
   * @return string|array Command output or array with output and exit code
   * @since 1.0.0
   */
  public static function exec_command($command, $args = array(), $return_code = false) {
    // Sanitize command and arguments
    $command = escapeshellcmd($command);
    $safe_args = array_map('escapeshellarg', $args);
    
    // Build full command
    $full_command = $command;
    if (!empty($safe_args)) {
      $full_command .= ' ' . implode(' ', $safe_args);
    }
    
    // Execute command
    $output = array();
    $exit_code = 0;
    exec($full_command . ' 2>&1', $output, $exit_code);
    
    $result = implode("\n", $output);
    
    if ($return_code) {
      return array(
        'output' => $result,
        'exit_code' => $exit_code,
        'success' => ($exit_code === 0)
      );
    }
    
    return $result;
  }

  /**
   * Postfix command wrappers
   */

  /**
   * Get Postfix configuration value
   *
   * @param string $parameter Configuration parameter
   * @return string|false
   * @since 1.0.0
   */
  public static function postconf_get($parameter) {
    $result = self::exec_command('postconf', array($parameter), true);
    
    if ($result['success'] && preg_match('/^' . preg_quote($parameter) . ' = (.+)$/', $result['output'], $matches)) {
      return trim($matches[1]);
    }
    
    return false;
  }

  /**
   * Set Postfix configuration value
   *
   * @param string $parameter Configuration parameter
   * @param string $value Configuration value
   * @return bool
   * @since 1.0.0
   */
  public static function postconf_set($parameter, $value) {
    $result = self::exec_command('postconf', array('-e', "{$parameter}={$value}"), true);
    return $result['success'];
  }

  /**
   * Reload Postfix maps
   *
   * @param string $map_file Map file path
   * @return bool
   * @since 1.0.0
   */
  public static function postmap($map_file) {
    $result = self::exec_command('postmap', array($map_file), true);
    return $result['success'];
  }

  /**
   * Get mail queue status
   *
   * @return array
   * @since 1.0.0
   */
  public static function postqueue_status() {
    $output = self::exec_command('postqueue', array('-p'));
    
    $status = array(
      'total_messages' => 0,
      'total_size' => 0,
      'messages' => array()
    );

    // Parse queue output
    $lines = explode("\n", $output);
    foreach ($lines as $line) {
      if (preg_match('/^([A-F0-9]+)[\s\*!]?\s+(\d+)\s+(.+?)\s+(.+)$/', $line, $matches)) {
        $status['messages'][] = array(
          'id' => $matches[1],
          'size' => intval($matches[2]),
          'date' => $matches[3],
          'sender' => $matches[4]
        );
        $status['total_size'] += intval($matches[2]);
        $status['total_messages']++;
      }
    }

    return $status;
  }

  /**
   * Flush mail queue
   *
   * @return bool
   * @since 1.0.0
   */
  public static function postqueue_flush() {
    $result = self::exec_command('postqueue', array('-f'), true);
    return $result['success'];
  }

  /**
   * Delete message from queue
   *
   * @param string $message_id Message ID
   * @return bool
   * @since 1.0.0
   */
  public static function postsuper_delete($message_id) {
    $result = self::exec_command('postsuper', array('-d', $message_id), true);
    return $result['success'];
  }

  /**
   * Dovecot command wrappers
   */

  /**
   * Get user quota information
   *
   * @param string $email Email address
   * @return array|false
   * @since 1.0.0
   */
  public static function doveadm_quota($email) {
    $output = self::exec_command('doveadm', array('quota', 'get', '-u', $email));
    
    $quota = array(
      'storage_used' => 0,
      'storage_limit' => 0,
      'message_count' => 0,
      'message_limit' => 0
    );

    $lines = explode("\n", $output);
    foreach ($lines as $line) {
      if (preg_match('/^storage\s+(\d+)\s+(\d+)/', $line, $matches)) {
        $quota['storage_used'] = intval($matches[1]);
        $quota['storage_limit'] = intval($matches[2]);
      } elseif (preg_match('/^message\s+(\d+)\s+(\d+)/', $line, $matches)) {
        $quota['message_count'] = intval($matches[1]);
        $quota['message_limit'] = intval($matches[2]);
      }
    }

    return $quota;
  }

  /**
   * Set user quota
   *
   * @param string $email Email address
   * @param int $storage_mb Storage quota in MB
   * @return bool
   * @since 1.0.0
   */
  public static function doveadm_quota_set($email, $storage_mb) {
    $result = self::exec_command('doveadm', array('quota', 'set', '-u', $email, 'storage', $storage_mb . 'M'), true);
    return $result['success'];
  }

  /**
   * Get mailbox statistics
   *
   * @param string $email Email address
   * @param string $mailbox Mailbox name (default: INBOX)
   * @return array|false
   * @since 1.0.0
   */
  public static function doveadm_mailbox_status($email, $mailbox = 'INBOX') {
    $output = self::exec_command('doveadm', array('mailbox', 'status', '-u', $email, 'messages', 'vsize', $mailbox));
    
    $stats = array(
      'messages' => 0,
      'size' => 0
    );

    if (preg_match('/messages=(\d+).*vsize=(\d+)/', $output, $matches)) {
      $stats['messages'] = intval($matches[1]);
      $stats['size'] = intval($matches[2]);
    }

    return $stats;
  }

  /**
   * Kick user connections
   *
   * @param string $email Email address
   * @return bool
   * @since 1.0.0
   */
  public static function doveadm_kick($email) {
    $result = self::exec_command('doveadm', array('kick', $email), true);
    return $result['success'];
  }

  /**
   * System service management
   */

  /**
   * Check if service is running
   *
   * @param string $service Service name
   * @return bool
   * @since 1.0.0
   */
  public static function service_is_active($service) {
    $result = self::exec_command('systemctl', array('is-active', $service), true);
    return ($result['exit_code'] === 0 && trim($result['output']) === 'active');
  }

  /**
   * Start service
   *
   * @param string $service Service name
   * @return bool
   * @since 1.0.0
   */
  public static function service_start($service) {
    $result = self::exec_command('systemctl', array('start', $service), true);
    return $result['success'];
  }

  /**
   * Stop service
   *
   * @param string $service Service name
   * @return bool
   * @since 1.0.0
   */
  public static function service_stop($service) {
    $result = self::exec_command('systemctl', array('stop', $service), true);
    return $result['success'];
  }

  /**
   * Restart service
   *
   * @param string $service Service name
   * @return bool
   * @since 1.0.0
   */
  public static function service_restart($service) {
    $result = self::exec_command('systemctl', array('restart', $service), true);
    return $result['success'];
  }

  /**
   * Reload service configuration
   *
   * @param string $service Service name
   * @return bool
   * @since 1.0.0
   */
  public static function service_reload($service) {
    $result = self::exec_command('systemctl', array('reload', $service), true);
    return $result['success'];
  }

  /**
   * Get service status information
   *
   * @param string $service Service name
   * @return array
   * @since 1.0.0
   */
  public static function service_status($service) {
    $output = self::exec_command('systemctl', array('status', $service));
    
    $status = array(
      'active' => false,
      'enabled' => false,
      'uptime' => '',
      'memory' => '',
      'pid' => 0
    );

    // Parse status output
    if (preg_match('/Active: (\w+)/', $output, $matches)) {
      $status['active'] = ($matches[1] === 'active');
    }

    if (preg_match('/Loaded: .* (enabled|disabled)/', $output, $matches)) {
      $status['enabled'] = ($matches[1] === 'enabled');
    }

    if (preg_match('/Main PID: (\d+)/', $output, $matches)) {
      $status['pid'] = intval($matches[1]);
    }

    return $status;
  }

  /**
   * File system utilities
   */

  /**
   * Get directory size
   *
   * @param string $path Directory path
   * @return int Size in bytes
   * @since 1.0.0
   */
  public static function get_directory_size($path) {
    if (!is_dir($path)) {
      return 0;
    }

    $output = self::exec_command('du', array('-sb', $path));
    
    if (preg_match('/^(\d+)/', $output, $matches)) {
      return intval($matches[1]);
    }

    return 0;
  }

  /**
   * Get disk usage for path
   *
   * @param string $path Path to check
   * @return array
   * @since 1.0.0
   */
  public static function get_disk_usage($path) {
    $output = self::exec_command('df', array('-h', $path));
    
    $usage = array(
      'total' => '',
      'used' => '',
      'available' => '',
      'percent' => 0
    );

    $lines = explode("\n", $output);
    if (isset($lines[1]) && preg_match('/\s+(\S+)\s+(\S+)\s+(\S+)\s+(\d+)%/', $lines[1], $matches)) {
      $usage['total'] = $matches[1];
      $usage['used'] = $matches[2];
      $usage['available'] = $matches[3];
      $usage['percent'] = intval($matches[4]);
    }

    return $usage;
  }

  /**
   * Create directory with proper permissions
   *
   * @param string $path Directory path
   * @param int $mode Permissions mode
   * @param string $owner Owner user
   * @param string $group Owner group
   * @return bool
   * @since 1.0.0
   */
  public static function create_directory($path, $mode = 0755, $owner = null, $group = null) {
    if (!wp_mkdir_p($path)) {
      return false;
    }

    chmod($path, $mode);

    if ($owner) {
      self::exec_command('chown', array($owner, $path));
    }

    if ($group) {
      self::exec_command('chgrp', array($group, $path));
    }

    return true;
  }

  /**
   * Email utilities
   */

  /**
   * Parse email address into local and domain parts
   *
   * @param string $email Email address
   * @return array|false
   * @since 1.0.0
   */
  public static function parse_email($email) {
    if (!is_email($email)) {
      return false;
    }

    $parts = explode('@', $email);
    
    return array(
      'local' => $parts[0],
      'domain' => $parts[1],
      'full' => $email
    );
  }

  /**
   * Generate secure password
   *
   * @param int $length Password length
   * @param bool $special_chars Include special characters
   * @return string
   * @since 1.0.0
   */
  public static function generate_password($length = 12, $special_chars = true) {
    $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    
    if ($special_chars) {
      $chars .= '!@#$%^&*()_+-=[]{}|;:,.<>?';
    }

    $password = '';
    $chars_length = strlen($chars);
    
    for ($i = 0; $i < $length; $i++) {
      $password .= $chars[wp_rand(0, $chars_length - 1)];
    }

    return $password;
  }

  /**
   * Validate domain name format
   *
   * @param string $domain Domain name
   * @return bool
   * @since 1.0.0
   */
  public static function validate_domain($domain) {
    return (bool) preg_match('/^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/', $domain);
  }

  /**
   * Log analysis utilities
   */

  /**
   * Parse mail log for statistics
   *
   * @param string $log_file Log file path
   * @param string $date Date filter (Y-m-d format)
   * @return array
   * @since 1.0.0
   */
  public static function parse_mail_log($log_file = '/var/log/mail.log', $date = null) {
    if (!file_exists($log_file)) {
      return array();
    }

    $date_filter = $date ?: date('Y-m-d');
    $stats = array(
      'sent' => 0,
      'received' => 0,
      'bounced' => 0,
      'rejected' => 0,
      'spam' => 0
    );

    $command = "grep '{$date_filter}' {$log_file}";
    $output = self::exec_command('sh', array('-c', $command));
    
    $lines = explode("\n", $output);
    foreach ($lines as $line) {
      if (strpos($line, 'status=sent') !== false) {
        $stats['sent']++;
      } elseif (strpos($line, 'status=bounced') !== false) {
        $stats['bounced']++;
      } elseif (strpos($line, 'reject') !== false) {
        $stats['rejected']++;
      } elseif (strpos($line, 'spam') !== false) {
        $stats['spam']++;
      }
    }

    return $stats;
  }

  /**
   * Get system load average
   *
   * @return array
   * @since 1.0.0
   */
  public static function get_load_average() {
    $output = self::exec_command('uptime');
    
    $load = array(
      '1min' => 0.0,
      '5min' => 0.0,
      '15min' => 0.0
    );

    if (preg_match('/load average: ([\d.]+), ([\d.]+), ([\d.]+)/', $output, $matches)) {
      $load['1min'] = floatval($matches[1]);
      $load['5min'] = floatval($matches[2]);
      $load['15min'] = floatval($matches[3]);
    }

    return $load;
  }

  /**
   * Get memory usage information
   *
   * @return array
   * @since 1.0.0
   */
  public static function get_memory_usage() {
    $output = self::exec_command('free', array('-m'));
    
    $memory = array(
      'total' => 0,
      'used' => 0,
      'free' => 0,
      'available' => 0,
      'percent_used' => 0
    );

    if (preg_match('/Mem:\s+(\d+)\s+(\d+)\s+(\d+)\s+\d+\s+\d+\s+(\d+)/', $output, $matches)) {
      $memory['total'] = intval($matches[1]);
      $memory['used'] = intval($matches[2]);
      $memory['free'] = intval($matches[3]);
      $memory['available'] = intval($matches[4]);
      $memory['percent_used'] = round(($memory['used'] / $memory['total']) * 100, 2);
    }

    return $memory;
  }
}

<?php
/**
 * Baum Mail Monitoring Class
 *
 * @package BaumMail
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
  exit;
}

/**
 * System monitoring and health checks
 *
 * @since 1.0.0
 */
class BaumMail_Monitor {

  /**
   * Constructor
   *
   * @since 1.0.0
   */
  public function __construct() {
    $this->init_hooks();
  }

  /**
   * Initialize hooks
   *
   * @since 1.0.0
   */
  private function init_hooks() {
    // Schedule monitoring checks
    add_action('wp', array($this, 'schedule_monitoring'));
    add_action('baum_mail_monitor_check', array($this, 'run_monitoring_check'));
    
    // AJAX handlers
    add_action('wp_ajax_baum_mail_get_system_status', array($this, 'ajax_get_system_status'));
    add_action('wp_ajax_baum_mail_restart_service', array($this, 'ajax_restart_service'));
    add_action('wp_ajax_baum_mail_start_service', array($this, 'ajax_start_service'));
    add_action('wp_ajax_baum_mail_stop_service', array($this, 'ajax_stop_service'));
    add_action('wp_ajax_baum_mail_get_service_logs', array($this, 'ajax_get_service_logs'));
  }

  /**
   * Schedule monitoring checks
   *
   * @since 1.0.0
   */
  public function schedule_monitoring() {
    if (!wp_next_scheduled('baum_mail_monitor_check')) {
      $interval = get_option('baum_mail_monitor_interval', 300); // 5 minutes default
      wp_schedule_event(time(), 'baum_mail_monitor_interval', 'baum_mail_monitor_check');
    }
  }

  /**
   * Get comprehensive system status
   *
   * @return array
   * @since 1.0.0
   */
  public function get_system_status() {
    $services = array(
      'postfix' => $this->check_postfix_status(),
      'dovecot' => $this->check_dovecot_status(),
      'clamav' => $this->check_clamav_status(),
      'spamassassin' => $this->check_spamassassin_status()
    );

    $status = array(
      'services' => $services,
      'system' => array(
        'disk_space' => $this->check_disk_space(),
        'memory_usage' => $this->check_memory_usage(),
        'load_average' => $this->check_load_average(),
        'network_ports' => $this->check_network_ports(),
        'ssl_certificates' => $this->check_ssl_certificates(),
        'mail_queue' => $this->check_mail_queue()
      ),
      'last_check' => current_time('mysql')
    );

    // Store status for historical tracking
    update_option('baum_mail_last_status', $status);

    return $status;
  }

  /**
   * Check Postfix status
   *
   * @return array
   * @since 1.0.0
   */
  private function check_postfix_status() {
    $status = array(
      'status' => false,
      'message' => '',
      'details' => array()
    );

    // Check service status
    $service_status = $this->execute_command('systemctl is-active postfix');
    $status['status'] = (trim($service_status) === 'active');
    $status['message'] = $status['status'] ? __('Running', BAUM_MAIL_TEXT_DOMAIN) : __('Not running', BAUM_MAIL_TEXT_DOMAIN);

    // Get additional details
    if ($status['status']) {
      $uptime = $this->execute_command('systemctl show postfix --property=ActiveEnterTimestamp');
      $status['details']['uptime'] = $uptime;
      
      $memory = $this->execute_command('systemctl show postfix --property=MemoryCurrent');
      $status['details']['memory'] = $memory;
    }

    return $status;
  }

  /**
   * Check Dovecot status
   *
   * @return array
   * @since 1.0.0
   */
  private function check_dovecot_status() {
    $status = array(
      'status' => false,
      'message' => '',
      'details' => array()
    );

    // Check service status
    $service_status = $this->execute_command('systemctl is-active dovecot');
    $status['status'] = (trim($service_status) === 'active');
    $status['message'] = $status['status'] ? __('Running', BAUM_MAIL_TEXT_DOMAIN) : __('Not running', BAUM_MAIL_TEXT_DOMAIN);

    // Get additional details
    if ($status['status']) {
      $connections = $this->execute_command('doveadm who | wc -l');
      $status['details']['active_connections'] = intval(trim($connections));
      
      $processes = $this->execute_command('pgrep dovecot | wc -l');
      $status['details']['processes'] = intval(trim($processes));
    }

    return $status;
  }

  /**
   * Check ClamAV status
   *
   * @return array
   * @since 1.0.0
   */
  private function check_clamav_status() {
    $status = array(
      'status' => false,
      'message' => '',
      'details' => array()
    );

    if (!get_option('baum_mail_enable_clamav', true)) {
      $status['message'] = __('Disabled', BAUM_MAIL_TEXT_DOMAIN);
      return $status;
    }

    // Check service status
    $service_status = $this->execute_command('systemctl is-active clamav-daemon');
    $status['status'] = (trim($service_status) === 'active');
    $status['message'] = $status['status'] ? __('Running', BAUM_MAIL_TEXT_DOMAIN) : __('Not running', BAUM_MAIL_TEXT_DOMAIN);

    // Check database age
    if ($status['status']) {
      $db_age = $this->execute_command('find /var/lib/clamav -name "*.cvd" -mtime +1 | wc -l');
      $status['details']['database_outdated'] = (intval(trim($db_age)) > 0);
    }

    return $status;
  }

  /**
   * Check SpamAssassin status
   *
   * @return array
   * @since 1.0.0
   */
  private function check_spamassassin_status() {
    $status = array(
      'status' => false,
      'message' => '',
      'details' => array()
    );

    if (!get_option('baum_mail_enable_spamassassin', true)) {
      $status['message'] = __('Disabled', BAUM_MAIL_TEXT_DOMAIN);
      return $status;
    }

    // Check service status
    $service_status = $this->execute_command('systemctl is-active spamassassin');
    $status['status'] = (trim($service_status) === 'active');
    $status['message'] = $status['status'] ? __('Running', BAUM_MAIL_TEXT_DOMAIN) : __('Not running', BAUM_MAIL_TEXT_DOMAIN);

    return $status;
  }

  /**
   * Check disk space
   *
   * @return array
   * @since 1.0.0
   */
  private function check_disk_space() {
    $status = array(
      'status' => true,
      'message' => '',
      'details' => array()
    );

    // Check mail directory disk usage
    $mail_usage = $this->execute_command('df -h /var/mail | tail -1');
    if (preg_match('/(\d+)%/', $mail_usage, $matches)) {
      $usage_percent = intval($matches[1]);
      $status['details']['mail_usage'] = $usage_percent;
      
      if ($usage_percent > 90) {
        $status['status'] = false;
        $status['message'] = sprintf(__('Mail directory %d%% full', BAUM_MAIL_TEXT_DOMAIN), $usage_percent);
      } else {
        $status['message'] = sprintf(__('Mail directory %d%% used', BAUM_MAIL_TEXT_DOMAIN), $usage_percent);
      }
    }

    // Check root filesystem
    $root_usage = $this->execute_command('df -h / | tail -1');
    if (preg_match('/(\d+)%/', $root_usage, $matches)) {
      $usage_percent = intval($matches[1]);
      $status['details']['root_usage'] = $usage_percent;
      
      if ($usage_percent > 95) {
        $status['status'] = false;
        $status['message'] = sprintf(__('Root filesystem %d%% full', BAUM_MAIL_TEXT_DOMAIN), $usage_percent);
      }
    }

    return $status;
  }

  /**
   * Check memory usage
   *
   * @return array
   * @since 1.0.0
   */
  private function check_memory_usage() {
    $status = array(
      'status' => true,
      'message' => '',
      'details' => array()
    );

    $memory_info = $this->execute_command('free -m');
    if (preg_match('/Mem:\s+(\d+)\s+(\d+)\s+(\d+)/', $memory_info, $matches)) {
      $total = intval($matches[1]);
      $used = intval($matches[2]);
      $usage_percent = round(($used / $total) * 100);
      
      $status['details']['total_mb'] = $total;
      $status['details']['used_mb'] = $used;
      $status['details']['usage_percent'] = $usage_percent;
      
      if ($usage_percent > 90) {
        $status['status'] = false;
        $status['message'] = sprintf(__('Memory %d%% used', BAUM_MAIL_TEXT_DOMAIN), $usage_percent);
      } else {
        $status['message'] = sprintf(__('Memory %d%% used', BAUM_MAIL_TEXT_DOMAIN), $usage_percent);
      }
    }

    return $status;
  }

  /**
   * Check load average
   *
   * @return array
   * @since 1.0.0
   */
  private function check_load_average() {
    $status = array(
      'status' => true,
      'message' => '',
      'details' => array()
    );

    $load_info = $this->execute_command('uptime');
    if (preg_match('/load average: ([\d.]+), ([\d.]+), ([\d.]+)/', $load_info, $matches)) {
      $load_1min = floatval($matches[1]);
      $load_5min = floatval($matches[2]);
      $load_15min = floatval($matches[3]);
      
      $status['details']['1min'] = $load_1min;
      $status['details']['5min'] = $load_5min;
      $status['details']['15min'] = $load_15min;
      
      // Get CPU count
      $cpu_count = intval($this->execute_command('nproc'));
      $status['details']['cpu_count'] = $cpu_count;
      
      // Check if load is high (> CPU count)
      if ($load_1min > $cpu_count) {
        $status['status'] = false;
        $status['message'] = sprintf(__('High load: %.2f', BAUM_MAIL_TEXT_DOMAIN), $load_1min);
      } else {
        $status['message'] = sprintf(__('Load: %.2f', BAUM_MAIL_TEXT_DOMAIN), $load_1min);
      }
    }

    return $status;
  }

  /**
   * Check network ports
   *
   * @return array
   * @since 1.0.0
   */
  private function check_network_ports() {
    $status = array(
      'status' => true,
      'message' => '',
      'details' => array()
    );

    $required_ports = array(
      25 => 'SMTP',
      587 => 'Submission',
      993 => 'IMAPS',
      995 => 'POP3S'
    );

    $listening_ports = array();
    $failed_ports = array();

    foreach ($required_ports as $port => $service) {
      $port_check = $this->execute_command("netstat -tlnp | grep :{$port}");
      if (!empty(trim($port_check))) {
        $listening_ports[] = "{$service} ({$port})";
        $status['details'][$port] = true;
      } else {
        $failed_ports[] = "{$service} ({$port})";
        $status['details'][$port] = false;
        $status['status'] = false;
      }
    }

    if ($status['status']) {
      $status['message'] = __('All ports listening', BAUM_MAIL_TEXT_DOMAIN);
    } else {
      $status['message'] = sprintf(__('Ports not listening: %s', BAUM_MAIL_TEXT_DOMAIN), implode(', ', $failed_ports));
    }

    return $status;
  }

  /**
   * Check SSL certificates
   *
   * @return array
   * @since 1.0.0
   */
  private function check_ssl_certificates() {
    $status = array(
      'status' => true,
      'message' => '',
      'details' => array()
    );

    $cert_path = get_option('baum_mail_ssl_cert_path', '/etc/ssl/certs') . '/mail.crt';
    
    if (!file_exists($cert_path)) {
      $status['status'] = false;
      $status['message'] = __('Certificate not found', BAUM_MAIL_TEXT_DOMAIN);
      return $status;
    }

    // Check certificate expiry
    $cert_info = $this->execute_command("openssl x509 -in {$cert_path} -enddate -noout");
    if (preg_match('/notAfter=(.+)/', $cert_info, $matches)) {
      $expires_at = strtotime(trim($matches[1]));
      $days_until_expiry = ceil(($expires_at - time()) / 86400);
      
      $status['details']['expires_at'] = date('Y-m-d H:i:s', $expires_at);
      $status['details']['days_until_expiry'] = $days_until_expiry;
      
      if ($days_until_expiry < 30) {
        $status['status'] = false;
        $status['message'] = sprintf(__('Certificate expires in %d days', BAUM_MAIL_TEXT_DOMAIN), $days_until_expiry);
      } else {
        $status['message'] = sprintf(__('Certificate valid for %d days', BAUM_MAIL_TEXT_DOMAIN), $days_until_expiry);
      }
    }

    return $status;
  }

  /**
   * Check mail queue
   *
   * @return array
   * @since 1.0.0
   */
  private function check_mail_queue() {
    $status = array(
      'status' => true,
      'message' => '',
      'details' => array()
    );

    $queue_info = $this->execute_command('postqueue -p | tail -n1');
    if (preg_match('/(\d+) Kbytes in (\d+) Request/', $queue_info, $matches)) {
      $queue_size = intval($matches[2]);
      $queue_bytes = intval($matches[1]);
      
      $status['details']['queue_size'] = $queue_size;
      $status['details']['queue_bytes'] = $queue_bytes;
      
      if ($queue_size > 100) {
        $status['status'] = false;
        $status['message'] = sprintf(__('Large queue: %d messages', BAUM_MAIL_TEXT_DOMAIN), $queue_size);
      } else {
        $status['message'] = sprintf(__('Queue: %d messages', BAUM_MAIL_TEXT_DOMAIN), $queue_size);
      }
    } else {
      $status['message'] = __('Queue empty', BAUM_MAIL_TEXT_DOMAIN);
      $status['details']['queue_size'] = 0;
    }

    return $status;
  }

  /**
   * Run monitoring check
   *
   * @since 1.0.0
   */
  public function run_monitoring_check() {
    $status = $this->get_system_status();
    
    // Check for critical issues
    $critical_issues = array();
    foreach ($status as $service => $data) {
      if (isset($data['status']) && !$data['status']) {
        $critical_issues[] = $service . ': ' . $data['message'];
      }
    }

    // Send alerts if there are critical issues
    if (!empty($critical_issues)) {
      $this->send_alert($critical_issues);
    }

    // Log status
    error_log('Baum Mail monitoring check completed. Issues: ' . count($critical_issues));
  }

  /**
   * Send alert for critical issues
   *
   * @param array $issues Critical issues
   * @since 1.0.0
   */
  private function send_alert($issues) {
    $admin_email = get_option('admin_email');
    $site_name = get_bloginfo('name');
    
    $subject = sprintf(__('[%s] Mail Server Alert', BAUM_MAIL_TEXT_DOMAIN), $site_name);
    $message = sprintf(__("Critical issues detected on %s:\n\n", BAUM_MAIL_TEXT_DOMAIN), $site_name);
    $message .= implode("\n", $issues);
    $message .= sprintf(__("\n\nPlease check your mail server configuration.\n\nTime: %s", BAUM_MAIL_TEXT_DOMAIN), current_time('mysql'));

    wp_mail($admin_email, $subject, $message);
  }

  /**
   * Restart service
   *
   * @param string $service Service name
   * @return bool|WP_Error
   * @since 1.0.0
   */
  public function restart_service($service) {
    $allowed_services = array('postfix', 'dovecot', 'clamav-daemon', 'spamassassin');

    if (!in_array($service, $allowed_services)) {
      return new WP_Error('invalid_service', __('Invalid service name.', BAUM_MAIL_TEXT_DOMAIN));
    }

    // Log the restart attempt
    $this->log_service_action($service, 'restart_attempt');

    // Detect operating system and use appropriate commands
    $os = php_uname('s');
    $output = '';
    $success = false;

    if (stripos($os, 'Darwin') !== false) {
      // macOS - use brew services or launchctl
      if ($this->command_exists('brew')) {
        $output = $this->execute_command("brew services restart {$service} 2>&1");
        // Check if service is running
        sleep(2);
        $status_output = $this->execute_command("brew services list | grep {$service}");
        $success = strpos($status_output, 'started') !== false;
      } else {
        // Try launchctl for system services
        $plist_name = $this->get_macos_service_name($service);
        if ($plist_name) {
          $this->execute_command("sudo launchctl unload {$plist_name} 2>&1");
          $output = $this->execute_command("sudo launchctl load {$plist_name} 2>&1");
          sleep(2);
          $status_output = $this->execute_command("sudo launchctl list | grep {$service}");
          $success = !empty(trim($status_output));
        }
      }
    } else {
      // Linux - use systemctl
      $output = $this->execute_command("systemctl restart {$service} 2>&1");

      // Check if restart was successful
      sleep(2);
      $status = $this->execute_command("systemctl is-active {$service}");
      $success = trim($status) === 'active';
    }

    if ($success) {
      $this->log_service_action($service, 'restart_success', $output);
      return true;
    } else {
      $this->log_service_action($service, 'restart_failed', $output);
      return new WP_Error('restart_failed', __('Failed to restart service.', BAUM_MAIL_TEXT_DOMAIN) . ' ' . $output);
    }
  }

  /**
   * Start service
   *
   * @param string $service Service name
   * @return bool|WP_Error
   * @since 1.0.0
   */
  public function start_service($service) {
    $allowed_services = array('postfix', 'dovecot', 'clamav-daemon', 'spamassassin');

    if (!in_array($service, $allowed_services)) {
      return new WP_Error('invalid_service', __('Invalid service name.', BAUM_MAIL_TEXT_DOMAIN));
    }

    $this->log_service_action($service, 'start_attempt');

    $os = php_uname('s');
    $output = '';
    $success = false;

    if (stripos($os, 'Darwin') !== false) {
      // macOS
      if ($this->command_exists('brew')) {
        $output = $this->execute_command("brew services start {$service} 2>&1");
        sleep(2);
        $status_output = $this->execute_command("brew services list | grep {$service}");
        $success = strpos($status_output, 'started') !== false;
      }
    } else {
      // Linux
      $output = $this->execute_command("systemctl start {$service} 2>&1");
      sleep(2);
      $status = $this->execute_command("systemctl is-active {$service}");
      $success = trim($status) === 'active';
    }

    if ($success) {
      $this->log_service_action($service, 'start_success', $output);
      return true;
    } else {
      $this->log_service_action($service, 'start_failed', $output);
      return new WP_Error('start_failed', __('Failed to start service.', BAUM_MAIL_TEXT_DOMAIN) . ' ' . $output);
    }
  }

  /**
   * Stop service
   *
   * @param string $service Service name
   * @return bool|WP_Error
   * @since 1.0.0
   */
  public function stop_service($service) {
    $allowed_services = array('postfix', 'dovecot', 'clamav-daemon', 'spamassassin');

    if (!in_array($service, $allowed_services)) {
      return new WP_Error('invalid_service', __('Invalid service name.', BAUM_MAIL_TEXT_DOMAIN));
    }

    $this->log_service_action($service, 'stop_attempt');

    $os = php_uname('s');
    $output = '';
    $success = false;

    if (stripos($os, 'Darwin') !== false) {
      // macOS
      if ($this->command_exists('brew')) {
        $output = $this->execute_command("brew services stop {$service} 2>&1");
        sleep(2);
        $status_output = $this->execute_command("brew services list | grep {$service}");
        $success = strpos($status_output, 'stopped') !== false;
      }
    } else {
      // Linux
      $output = $this->execute_command("systemctl stop {$service} 2>&1");
      sleep(2);
      $status = $this->execute_command("systemctl is-active {$service}");
      $success = trim($status) === 'inactive';
    }

    if ($success) {
      $this->log_service_action($service, 'stop_success', $output);
      return true;
    } else {
      $this->log_service_action($service, 'stop_failed', $output);
      return new WP_Error('stop_failed', __('Failed to stop service.', BAUM_MAIL_TEXT_DOMAIN) . ' ' . $output);
    }
  }

  /**
   * Get macOS service name for launchctl
   *
   * @param string $service Service name
   * @return string|null
   * @since 1.0.0
   */
  private function get_macos_service_name($service) {
    $service_map = array(
      'postfix' => '/System/Library/LaunchDaemons/org.postfix.master.plist',
      'dovecot' => '/usr/local/etc/LaunchDaemons/homebrew.mxcl.dovecot.plist',
      'clamav-daemon' => '/usr/local/etc/LaunchDaemons/homebrew.mxcl.clamav.plist',
      'spamassassin' => '/usr/local/etc/LaunchDaemons/homebrew.mxcl.spamassassin.plist'
    );

    return isset($service_map[$service]) ? $service_map[$service] : null;
  }

  /**
   * Log service action
   *
   * @param string $service Service name
   * @param string $action Action performed
   * @param string $output Command output
   * @since 1.0.0
   */
  private function log_service_action($service, $action, $output = '') {
    $log_entry = array(
      'timestamp' => current_time('mysql'),
      'service' => $service,
      'action' => $action,
      'output' => $output,
      'user' => get_current_user_id()
    );

    // Store in WordPress options (you could also use a custom table)
    $logs = get_option('baum_mail_service_logs', array());
    array_unshift($logs, $log_entry);

    // Keep only last 100 log entries
    $logs = array_slice($logs, 0, 100);

    update_option('baum_mail_service_logs', $logs);
  }

  /**
   * Execute system command safely
   *
   * @param string $command Command to execute
   * @return string Command output
   * @since 1.0.0
   */
  private function execute_command($command) {
    // Sanitize command
    $command = escapeshellcmd($command);
    
    // Execute and capture output
    ob_start();
    $return_var = 0;
    $output = shell_exec($command . ' 2>&1');
    ob_end_clean();

    return $output ?: '';
  }

  /**
   * AJAX: Get system status
   *
   * @since 1.0.0
   */
  public function ajax_get_system_status() {
    check_ajax_referer('baum_mail_admin_nonce', 'nonce');

    if (!current_user_can('manage_options')) {
      wp_die(__('Insufficient permissions.', BAUM_MAIL_TEXT_DOMAIN));
    }

    $status = $this->get_system_status();
    wp_send_json_success($status);
  }

  /**
   * AJAX: Restart service
   *
   * @since 1.0.0
   */
  public function ajax_restart_service() {
    check_ajax_referer('baum_mail_admin_nonce', 'nonce');

    if (!current_user_can('manage_options')) {
      wp_die(__('Insufficient permissions.', BAUM_MAIL_TEXT_DOMAIN));
    }

    $service = sanitize_text_field($_POST['service']);
    $result = $this->restart_service($service);

    if (is_wp_error($result)) {
      wp_send_json_error($result->get_error_message());
    } else {
      wp_send_json_success(sprintf(__('%s restarted successfully.', BAUM_MAIL_TEXT_DOMAIN), ucfirst($service)));
    }
  }

  /**
   * AJAX: Start service
   *
   * @since 1.0.0
   */
  public function ajax_start_service() {
    check_ajax_referer('baum_mail_admin_nonce', 'nonce');

    if (!current_user_can('manage_options')) {
      wp_die(__('Insufficient permissions.', BAUM_MAIL_TEXT_DOMAIN));
    }

    $service = sanitize_text_field($_POST['service']);
    $result = $this->start_service($service);

    if (is_wp_error($result)) {
      wp_send_json_error($result->get_error_message());
    } else {
      wp_send_json_success(sprintf(__('%s started successfully.', BAUM_MAIL_TEXT_DOMAIN), ucfirst($service)));
    }
  }

  /**
   * AJAX: Stop service
   *
   * @since 1.0.0
   */
  public function ajax_stop_service() {
    check_ajax_referer('baum_mail_admin_nonce', 'nonce');

    if (!current_user_can('manage_options')) {
      wp_die(__('Insufficient permissions.', BAUM_MAIL_TEXT_DOMAIN));
    }

    $service = sanitize_text_field($_POST['service']);
    $result = $this->stop_service($service);

    if (is_wp_error($result)) {
      wp_send_json_error($result->get_error_message());
    } else {
      wp_send_json_success(sprintf(__('%s stopped successfully.', BAUM_MAIL_TEXT_DOMAIN), ucfirst($service)));
    }
  }

  /**
   * AJAX: Get service logs
   *
   * @since 1.0.0
   */
  public function ajax_get_service_logs() {
    check_ajax_referer('baum_mail_admin_nonce', 'nonce');

    if (!current_user_can('manage_options')) {
      wp_die(__('Insufficient permissions.', BAUM_MAIL_TEXT_DOMAIN));
    }

    $logs = get_option('baum_mail_service_logs', array());
    wp_send_json_success($logs);
  }
}

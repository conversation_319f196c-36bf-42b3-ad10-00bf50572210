<?php
/**
 * Baum Mail Postfix Integration Class
 *
 * @package BaumMail
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
  exit;
}

/**
 * Postfix mail server integration
 *
 * @since 1.0.0
 */
class BaumMail_Postfix {

  /**
   * Postfix configuration path
   *
   * @var string
   * @since 1.0.0
   */
  private $config_path;

  /**
   * Constructor
   *
   * @since 1.0.0
   */
  public function __construct() {
    $this->config_path = get_option('baum_mail_postfix_config_path', '/etc/postfix');
    $this->init_hooks();
  }

  /**
   * Initialize hooks
   *
   * @since 1.0.0
   */
  private function init_hooks() {
    add_action('baum_mail_domain_created', array($this, 'update_domain_maps'));
    add_action('baum_mail_account_created', array($this, 'update_account_maps'));
    add_action('baum_mail_alias_created', array($this, 'update_alias_maps'));
  }

  /**
   * Get Postfix status
   *
   * @return array
   * @since 1.0.0
   */
  public function get_status() {
    $status = array(
      'running' => false,
      'version' => '',
      'config_valid' => false,
      'queue_size' => 0,
      'last_check' => current_time('mysql')
    );

    // Check if Postfix is running
    $output = $this->execute_command('systemctl is-active postfix');
    $status['running'] = (trim($output) === 'active');

    // Get Postfix version
    $version_output = $this->execute_command('postconf mail_version');
    if (preg_match('/mail_version = (.+)/', $version_output, $matches)) {
      $status['version'] = trim($matches[1]);
    }

    // Check configuration
    $config_check = $this->execute_command('postfix check 2>&1');
    $status['config_valid'] = empty(trim($config_check));

    // Get queue size
    $queue_output = $this->execute_command('postqueue -p | tail -n1');
    if (preg_match('/(\d+) Kbytes in (\d+) Request/', $queue_output, $matches)) {
      $status['queue_size'] = intval($matches[2]);
    }

    return $status;
  }

  /**
   * Reload Postfix configuration
   *
   * @return bool|WP_Error
   * @since 1.0.0
   */
  public function reload_config() {
    $output = $this->execute_command('postfix reload 2>&1');
    
    if (strpos($output, 'postfix/postfix-script: refreshing') !== false) {
      return true;
    }

    return new WP_Error('reload_failed', __('Failed to reload Postfix configuration.', BAUM_MAIL_TEXT_DOMAIN));
  }

  /**
   * Update domain maps
   *
   * @param int $domain_id Domain ID
   * @since 1.0.0
   */
  public function update_domain_maps($domain_id = null) {
    global $wpdb;

    // Get all active domains
    $domains = $wpdb->get_results(
      "SELECT domain FROM {$wpdb->prefix}baum_mail_domains WHERE active = 1",
      ARRAY_A
    );

    $domain_list = array();
    foreach ($domains as $domain) {
      $domain_list[] = $domain['domain'] . ' OK';
    }

    // Write to virtual domains file
    $domains_file = $this->config_path . '/virtual_domains';
    $this->write_config_file($domains_file, implode("\n", $domain_list));

    // Update Postfix maps
    $this->execute_command("postmap {$domains_file}");
    
    // Log action
    error_log("Updated Postfix domain maps with " . count($domain_list) . " domains");
  }

  /**
   * Update account maps
   *
   * @param int $account_id Account ID
   * @since 1.0.0
   */
  public function update_account_maps($account_id = null) {
    global $wpdb;

    // Get all active accounts
    $accounts = $wpdb->get_results(
      "SELECT email FROM {$wpdb->prefix}baum_mail_accounts WHERE active = 1",
      ARRAY_A
    );

    $account_list = array();
    foreach ($accounts as $account) {
      // Map to virtual mailbox location
      $mailbox_path = $this->get_mailbox_path($account['email']);
      $account_list[] = $account['email'] . ' ' . $mailbox_path;
    }

    // Write to virtual mailboxes file
    $mailboxes_file = $this->config_path . '/virtual_mailboxes';
    $this->write_config_file($mailboxes_file, implode("\n", $account_list));

    // Update Postfix maps
    $this->execute_command("postmap {$mailboxes_file}");
    
    // Log action
    error_log("Updated Postfix mailbox maps with " . count($account_list) . " accounts");
  }

  /**
   * Update alias maps
   *
   * @param int $alias_id Alias ID
   * @since 1.0.0
   */
  public function update_alias_maps($alias_id = null) {
    global $wpdb;

    // Get all active aliases
    $aliases = $wpdb->get_results(
      "SELECT source, destination FROM {$wpdb->prefix}baum_mail_aliases WHERE active = 1",
      ARRAY_A
    );

    $alias_list = array();
    foreach ($aliases as $alias) {
      $alias_list[] = $alias['source'] . ' ' . $alias['destination'];
    }

    // Write to virtual aliases file
    $aliases_file = $this->config_path . '/virtual_aliases';
    $this->write_config_file($aliases_file, implode("\n", $alias_list));

    // Update Postfix maps
    $this->execute_command("postmap {$aliases_file}");
    
    // Log action
    error_log("Updated Postfix alias maps with " . count($alias_list) . " aliases");
  }

  /**
   * Get mailbox path for email
   *
   * @param string $email Email address
   * @return string
   * @since 1.0.0
   */
  private function get_mailbox_path($email) {
    // Standard virtual mailbox path structure
    return $email . '/';
  }

  /**
   * Write configuration file
   *
   * @param string $file File path
   * @param string $content File content
   * @return bool
   * @since 1.0.0
   */
  private function write_config_file($file, $content) {
    // Ensure directory exists
    $dir = dirname($file);
    if (!is_dir($dir)) {
      wp_mkdir_p($dir);
    }

    // Write file with proper permissions
    $result = file_put_contents($file, $content);
    
    if ($result !== false) {
      chmod($file, 0644);
      return true;
    }

    return false;
  }

  /**
   * Execute system command safely
   *
   * @param string $command Command to execute
   * @return string Command output
   * @since 1.0.0
   */
  private function execute_command($command) {
    // Sanitize command
    $command = escapeshellcmd($command);
    
    // Execute and capture output
    ob_start();
    $return_var = 0;
    $output = shell_exec($command . ' 2>&1');
    ob_end_clean();

    return $output ?: '';
  }

  /**
   * Generate main.cf configuration
   *
   * @return string
   * @since 1.0.0
   */
  public function generate_main_config() {
    $config = array(
      '# Baum Mail Postfix Configuration',
      '# Generated on ' . current_time('mysql'),
      '',
      '# Basic settings',
      'myhostname = ' . get_option('siteurl'),
      'mydomain = ' . parse_url(get_option('siteurl'), PHP_URL_HOST),
      'myorigin = $mydomain',
      'inet_interfaces = all',
      'inet_protocols = ipv4',
      '',
      '# Virtual domains and mailboxes',
      'virtual_mailbox_domains = hash:' . $this->config_path . '/virtual_domains',
      'virtual_mailbox_maps = hash:' . $this->config_path . '/virtual_mailboxes',
      'virtual_alias_maps = hash:' . $this->config_path . '/virtual_aliases',
      'virtual_mailbox_base = /var/mail/vhosts',
      'virtual_minimum_uid = 100',
      'virtual_uid_maps = static:5000',
      'virtual_gid_maps = static:5000',
      '',
      '# Security settings',
      'smtpd_tls_cert_file = ' . get_option('baum_mail_ssl_cert_path', '/etc/ssl/certs') . '/mail.crt',
      'smtpd_tls_key_file = ' . get_option('baum_mail_ssl_key_path', '/etc/ssl/private') . '/mail.key',
      'smtpd_use_tls = yes',
      'smtpd_tls_session_cache_database = btree:${data_directory}/smtpd_scache',
      'smtp_tls_session_cache_database = btree:${data_directory}/smtp_scache',
      '',
      '# SASL authentication',
      'smtpd_sasl_type = dovecot',
      'smtpd_sasl_path = private/auth',
      'smtpd_sasl_auth_enable = yes',
      'smtpd_sasl_security_options = noanonymous',
      'smtpd_sasl_local_domain = $myhostname',
      'broken_sasl_auth_clients = yes',
      '',
      '# Restrictions',
      'smtpd_recipient_restrictions = permit_sasl_authenticated, permit_mynetworks, reject_unauth_destination',
      'smtpd_sender_restrictions = permit_sasl_authenticated, permit_mynetworks',
      '',
      '# Message size limits',
      'message_size_limit = 52428800', // 50MB
      'mailbox_size_limit = 0',
      '',
      '# Queue settings',
      'maximal_queue_lifetime = 7d',
      'bounce_queue_lifetime = 7d',
      'maximal_backoff_time = 4000s',
      'minimal_backoff_time = 300s',
      'queue_run_delay = 300s'
    );

    return implode("\n", $config);
  }

  /**
   * Generate master.cf configuration
   *
   * @return string
   * @since 1.0.0
   */
  public function generate_master_config() {
    $config = array(
      '# Baum Mail Postfix Master Configuration',
      '# Generated on ' . current_time('mysql'),
      '',
      '# SMTP service',
      'smtp      inet  n       -       y       -       -       smtpd',
      '',
      '# Submission service (port 587)',
      'submission inet n       -       y       -       -       smtpd',
      '  -o syslog_name=postfix/submission',
      '  -o smtpd_tls_security_level=encrypt',
      '  -o smtpd_sasl_auth_enable=yes',
      '  -o smtpd_reject_unlisted_recipient=no',
      '  -o smtpd_client_restrictions=$mua_client_restrictions',
      '  -o smtpd_helo_restrictions=$mua_helo_restrictions',
      '  -o smtpd_sender_restrictions=$mua_sender_restrictions',
      '  -o smtpd_recipient_restrictions=permit_sasl_authenticated,reject',
      '  -o milter_macro_daemon_name=ORIGINATING',
      '',
      '# SMTPS service (port 465)',
      'smtps     inet  n       -       y       -       -       smtpd',
      '  -o syslog_name=postfix/smtps',
      '  -o smtpd_tls_wrappermode=yes',
      '  -o smtpd_sasl_auth_enable=yes',
      '  -o smtpd_reject_unlisted_recipient=no',
      '  -o smtpd_client_restrictions=$mua_client_restrictions',
      '  -o smtpd_helo_restrictions=$mua_helo_restrictions',
      '  -o smtpd_sender_restrictions=$mua_sender_restrictions',
      '  -o smtpd_recipient_restrictions=permit_sasl_authenticated,reject',
      '  -o milter_macro_daemon_name=ORIGINATING',
      '',
      '# Other services',
      'pickup    unix  n       -       y       60      1       pickup',
      'cleanup   unix  n       -       y       -       0       cleanup',
      'qmgr      unix  n       -       n       300     1       qmgr',
      'tlsmgr    unix  -       -       y       1000?   1       tlsmgr',
      'rewrite   unix  -       -       y       -       -       trivial-rewrite',
      'bounce    unix  -       -       y       -       0       bounce',
      'defer     unix  -       -       y       -       0       bounce',
      'trace     unix  -       -       y       -       0       bounce',
      'verify    unix  -       -       y       -       1       verify',
      'flush     unix  n       -       y       1000?   0       flush',
      'proxymap  unix  -       -       n       -       -       proxymap',
      'proxywrite unix -       -       n       -       1       proxymap',
      'smtp      unix  -       -       y       -       -       smtp',
      'relay     unix  -       -       y       -       -       smtp',
      'showq     unix  n       -       y       -       -       showq',
      'error     unix  -       -       y       -       -       error',
      'retry     unix  -       -       y       -       -       error',
      'discard   unix  -       -       y       -       -       discard',
      'local     unix  -       n       n       -       -       local',
      'virtual   unix  -       n       n       -       -       virtual',
      'lmtp      unix  -       -       y       -       -       lmtp',
      'anvil     unix  -       -       y       -       1       anvil',
      'scache    unix  -       -       y       -       1       scache'
    );

    return implode("\n", $config);
  }

  /**
   * Test Postfix configuration
   *
   * @return array
   * @since 1.0.0
   */
  public function test_configuration() {
    $tests = array();

    // Test configuration syntax
    $config_test = $this->execute_command('postfix check 2>&1');
    $tests['config_syntax'] = array(
      'name' => __('Configuration Syntax', BAUM_MAIL_TEXT_DOMAIN),
      'status' => empty(trim($config_test)),
      'message' => empty(trim($config_test)) ? __('Valid', BAUM_MAIL_TEXT_DOMAIN) : $config_test
    );

    // Test service status
    $service_test = $this->execute_command('systemctl is-active postfix');
    $tests['service_status'] = array(
      'name' => __('Service Status', BAUM_MAIL_TEXT_DOMAIN),
      'status' => (trim($service_test) === 'active'),
      'message' => ucfirst(trim($service_test))
    );

    // Test port connectivity
    $port_test = $this->execute_command('netstat -tlnp | grep :25');
    $tests['port_25'] = array(
      'name' => __('SMTP Port (25)', BAUM_MAIL_TEXT_DOMAIN),
      'status' => !empty(trim($port_test)),
      'message' => !empty(trim($port_test)) ? __('Listening', BAUM_MAIL_TEXT_DOMAIN) : __('Not listening', BAUM_MAIL_TEXT_DOMAIN)
    );

    return $tests;
  }

  /**
   * Configure email tracking
   *
   * @return bool|WP_Error
   * @since 1.0.0
   */
  public function configure_tracking() {
    if (!get_option('baum_mail_tracking_enabled', false)) {
      return true; // Tracking disabled, nothing to configure
    }

    // Create tracking filter script
    $this->create_tracking_filter();

    // Update main.cf to include tracking configuration
    $this->add_tracking_to_main_cf();

    // Reload Postfix configuration
    return $this->reload_config();
  }

  /**
   * Create tracking filter script
   *
   * @return bool Success
   * @since 1.0.0
   */
  private function create_tracking_filter() {
    $scripts_dir = BAUM_MAIL_PLUGIN_DIR . 'scripts';
    $filter_script = $scripts_dir . '/add-tracking-pixel.php';

    if (!is_dir($scripts_dir)) {
      wp_mkdir_p($scripts_dir);
    }

    $script_content = '#!/usr/bin/env php
<?php
/**
 * Baum Mail Tracking Pixel Filter
 *
 * This script adds tracking pixels to outgoing HTML emails
 */

// Read email content from stdin
$email_content = file_get_contents("php://stdin");

// Check if this is an HTML email
if (strpos($email_content, "Content-Type: text/html") !== false) {
    // Generate tracking ID
    $tracking_id = uniqid("track_", true);

    // Get tracking domain from WordPress
    $wp_config = dirname(__FILE__) . "/../../../../wp-config.php";
    if (file_exists($wp_config)) {
        require_once $wp_config;
        require_once ABSPATH . "wp-load.php";

        $tracking_domain = get_option("baum_mail_tracking_domain", get_site_url());
        $tracking_url = add_query_arg(array(
            "baum_mail_track" => $tracking_id,
            "action" => "baum_mail_tracking_pixel"
        ), $tracking_domain);

        // Create tracking pixel HTML
        $tracking_pixel = "<img src=\"" . esc_url($tracking_url) . "\" width=\"1\" height=\"1\" style=\"display:none;\" alt=\"\" />";

        // Add tracking pixel before closing body tag or at the end of HTML content
        if (preg_match("/<\/body>/i", $email_content)) {
            $email_content = preg_replace("/<\/body>/i", $tracking_pixel . "</body>", $email_content);
        } else {
            // Add at the end of the email
            $email_content .= $tracking_pixel;
        }

        // Store tracking data in database
        global $wpdb;
        $wpdb->insert(
            $wpdb->prefix . "baum_mail_tracking",
            array(
                "tracking_id" => $tracking_id,
                "recipient_email" => "unknown",
                "subject" => "unknown",
                "sent_at" => current_time("mysql"),
                "opened" => 0,
                "open_count" => 0
            ),
            array("%s", "%s", "%s", "%s", "%d", "%d")
        );
    }
}

// Output the modified email content
echo $email_content;
?>';

    if (!file_put_contents($filter_script, $script_content)) {
      return false;
    }

    // Make script executable
    chmod($filter_script, 0755);

    return true;
  }

  /**
   * Add tracking configuration to main.cf
   *
   * @return bool Success
   * @since 1.0.0
   */
  private function add_tracking_to_main_cf() {
    $main_cf_file = $this->config_path . '/main.cf';
    $filter_script = BAUM_MAIL_PLUGIN_DIR . 'scripts/add-tracking-pixel.php';

    if (!file_exists($main_cf_file)) {
      return false;
    }

    $tracking_config = "
# Baum Mail Tracking Configuration
content_filter = baum-mail-tracking
baum-mail-tracking_destination_recipient_limit = 1
";

    $current_content = file_get_contents($main_cf_file);

    // Check if tracking config already exists
    if (strpos($current_content, 'baum-mail-tracking') !== false) {
      return true; // Already exists
    }

    // Add tracking configuration
    $new_content = $current_content . $tracking_config;

    if (!file_put_contents($main_cf_file, $new_content)) {
      return false;
    }

    // Add filter to master.cf
    return $this->add_filter_to_master_cf();
  }

  /**
   * Add tracking filter to master.cf
   *
   * @return bool Success
   * @since 1.0.0
   */
  private function add_filter_to_master_cf() {
    $master_cf_file = $this->config_path . '/master.cf';
    $filter_script = BAUM_MAIL_PLUGIN_DIR . 'scripts/add-tracking-pixel.php';

    if (!file_exists($master_cf_file)) {
      return false;
    }

    $filter_config = "
# Baum Mail Tracking Filter
baum-mail-tracking unix - n n - - pipe
  flags=Rq user=postfix argv=" . $filter_script . "
";

    $current_content = file_get_contents($master_cf_file);

    // Check if filter already exists
    if (strpos($current_content, 'baum-mail-tracking') !== false) {
      return true; // Already exists
    }

    // Add filter configuration
    $new_content = $current_content . $filter_config;

    return file_put_contents($master_cf_file, $new_content) !== false;
  }
}

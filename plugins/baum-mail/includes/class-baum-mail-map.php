<?php
/**
 * <PERSON>um Mail Map Component
 *
 * Provides map functionality using Leaflet.js for analytics visualization
 *
 * @package BaumMail
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
  exit;
}

/**
 * Map component for analytics visualization
 *
 * @since 1.0.0
 */
class BaumMail_Map {

  /**
   * Constructor
   *
   * @since 1.0.0
   */
  public function __construct() {
    $this->init_hooks();
  }

  /**
   * Initialize hooks
   *
   * @since 1.0.0
   */
  private function init_hooks() {
    add_shortcode('baum_mail_map', array($this, 'render_map_shortcode'));
    add_action('wp_enqueue_scripts', array($this, 'enqueue_map_scripts'));
    add_action('admin_enqueue_scripts', array($this, 'enqueue_map_scripts'));
  }

  /**
   * Enqueue map scripts and styles
   *
   * @since 1.0.0
   */
  public function enqueue_map_scripts() {
    wp_enqueue_script(
      'baum-mail-leaflet',
      BAUM_MAIL_PLUGIN_URL . 'assets/js/leaflet.js',
      array(),
      BAUM_MAIL_VERSION,
      true
    );

    wp_enqueue_style(
      'baum-mail-leaflet-css',
      BAUM_MAIL_PLUGIN_URL . 'assets/css/leaflet.css',
      array(),
      BAUM_MAIL_VERSION
    );

    wp_enqueue_style(
      'baum-mail-map-css',
      BAUM_MAIL_PLUGIN_URL . 'assets/css/map.css',
      array('baum-mail-leaflet-css'),
      BAUM_MAIL_VERSION
    );
  }

  /**
   * Render map shortcode
   *
   * @param array $atts Shortcode attributes
   * @return string Map HTML
   * @since 1.0.0
   */
  public function render_map_shortcode($atts) {
    $atts = shortcode_atts(array(
      'height' => '400px',
      'width' => '100%',
      'zoom' => '2',
      'center_lat' => '20',
      'center_lng' => '0',
      'data_source' => 'analytics', // analytics, custom
      'account_id' => '',
      'domain_id' => '',
      'date_from' => '',
      'date_to' => ''
    ), $atts);

    $map_id = 'baum-mail-map-' . uniqid();
    
    // Get map data
    $map_data = $this->get_map_data($atts);

    ob_start();
    ?>
    <div id="<?php echo esc_attr($map_id); ?>" class="baum-mail-map" 
         style="height: <?php echo esc_attr($atts['height']); ?>; width: <?php echo esc_attr($atts['width']); ?>;">
    </div>
    
    <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Initialize map
      var map = L.map('<?php echo esc_js($map_id); ?>').setView([<?php echo esc_js($atts['center_lat']); ?>, <?php echo esc_js($atts['center_lng']); ?>], <?php echo esc_js($atts['zoom']); ?>);

      // Add tile layer (OpenStreetMap)
      L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors',
        maxZoom: 18
      }).addTo(map);

      // Add markers for email opens
      var mapData = <?php echo json_encode($map_data); ?>;
      
      mapData.forEach(function(point) {
        var marker = L.marker([point.lat, point.lng]).addTo(map);
        
        var popupContent = '<div class="map-popup">' +
          '<h4>' + point.location + '</h4>' +
          '<p><strong><?php echo esc_js(__('Email Opens:', BAUM_MAIL_TEXT_DOMAIN)); ?></strong> ' + point.opens + '</p>' +
          '<p><strong><?php echo esc_js(__('Unique Opens:', BAUM_MAIL_TEXT_DOMAIN)); ?></strong> ' + point.unique_opens + '</p>';
          
        if (point.last_open) {
          popupContent += '<p><strong><?php echo esc_js(__('Last Open:', BAUM_MAIL_TEXT_DOMAIN)); ?></strong> ' + point.last_open + '</p>';
        }
        
        popupContent += '</div>';
        
        marker.bindPopup(popupContent);
        
        // Add circle to show intensity
        var circle = L.circle([point.lat, point.lng], {
          color: '#ff79c6',
          fillColor: '#ff79c6',
          fillOpacity: 0.3,
          radius: Math.max(point.opens * 1000, 5000)
        }).addTo(map);
      });
      
      // Fit map to show all markers
      if (mapData.length > 0) {
        var group = new L.featureGroup(map._layers);
        if (Object.keys(group._layers).length > 0) {
          map.fitBounds(group.getBounds().pad(0.1));
        }
      }
    });
    </script>
    <?php
    return ob_get_clean();
  }

  /**
   * Get map data for visualization
   *
   * @param array $params Parameters for data retrieval
   * @return array Map data points
   * @since 1.0.0
   */
  private function get_map_data($params) {
    global $wpdb;

    // Check if the opens table exists
    $table_name = $wpdb->prefix . 'baum_mail_opens';
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") === $table_name;

    if (!$table_exists) {
      // Return sample data for demonstration
      return $this->get_sample_map_data();
    }

    // Check if required columns exist
    $columns = $wpdb->get_col("DESCRIBE {$table_name}");
    $required_columns = array('country', 'region', 'city', 'latitude', 'longitude', 'ip_address', 'opened_at');

    foreach ($required_columns as $column) {
      if (!in_array($column, $columns)) {
        // Return sample data if columns are missing
        return $this->get_sample_map_data();
      }
    }

    $where_conditions = array('1=1');
    $where_values = array();

    // Add filters based on parameters (skip account_id if column doesn't exist)
    if (!empty($params['account_id']) && in_array('account_id', $columns)) {
      $where_conditions[] = 'account_id = %d';
      $where_values[] = intval($params['account_id']);
    }

    if (!empty($params['domain_id']) && in_array('domain_id', $columns)) {
      $where_conditions[] = 'domain_id = %d';
      $where_values[] = intval($params['domain_id']);
    }

    if (!empty($params['date_from'])) {
      $where_conditions[] = 'opened_at >= %s';
      $where_values[] = $params['date_from'];
    }

    if (!empty($params['date_to'])) {
      $where_conditions[] = 'opened_at <= %s';
      $where_values[] = $params['date_to'] . ' 23:59:59';
    }

    $where_clause = implode(' AND ', $where_conditions);

    // Get geographic data from email opens
    $sql = "
      SELECT
        country,
        region,
        city,
        latitude,
        longitude,
        COUNT(*) as opens,
        COUNT(DISTINCT ip_address) as unique_opens,
        MAX(opened_at) as last_open
      FROM {$table_name}
      WHERE {$where_clause}
      AND latitude IS NOT NULL
      AND longitude IS NOT NULL
      GROUP BY country, region, city, latitude, longitude
      ORDER BY opens DESC
      LIMIT 100
    ";

    if (!empty($where_values)) {
      $results = $wpdb->get_results($wpdb->prepare($sql, $where_values));
    } else {
      $results = $wpdb->get_results($sql);
    }

    if ($wpdb->last_error) {
      // Return sample data if query fails
      return $this->get_sample_map_data();
    }

    $map_data = array();

    if (!empty($results)) {
      foreach ($results as $row) {
        $location = array_filter(array($row->city, $row->region, $row->country));

        $map_data[] = array(
          'lat' => floatval($row->latitude),
          'lng' => floatval($row->longitude),
          'location' => implode(', ', $location),
          'opens' => intval($row->opens),
          'unique_opens' => intval($row->unique_opens),
          'last_open' => $row->last_open
        );
      }
    }

    // If no real data, return sample data
    if (empty($map_data)) {
      return $this->get_sample_map_data();
    }

    return $map_data;
  }

  /**
   * Get sample map data for demonstration
   *
   * @return array Sample map data points
   * @since 1.0.0
   */
  private function get_sample_map_data() {
    return array(
      array(
        'lat' => 40.7128,
        'lng' => -74.0060,
        'location' => 'New York, NY, USA',
        'opens' => 25,
        'unique_opens' => 18,
        'last_open' => date('Y-m-d H:i:s', strtotime('-2 hours'))
      ),
      array(
        'lat' => 51.5074,
        'lng' => -0.1278,
        'location' => 'London, England, UK',
        'opens' => 15,
        'unique_opens' => 12,
        'last_open' => date('Y-m-d H:i:s', strtotime('-5 hours'))
      ),
      array(
        'lat' => 35.6762,
        'lng' => 139.6503,
        'location' => 'Tokyo, Japan',
        'opens' => 8,
        'unique_opens' => 6,
        'last_open' => date('Y-m-d H:i:s', strtotime('-1 day'))
      ),
      array(
        'lat' => -33.8688,
        'lng' => 151.2093,
        'location' => 'Sydney, NSW, Australia',
        'opens' => 12,
        'unique_opens' => 9,
        'last_open' => date('Y-m-d H:i:s', strtotime('-3 hours'))
      ),
      array(
        'lat' => 52.5200,
        'lng' => 13.4050,
        'location' => 'Berlin, Germany',
        'opens' => 6,
        'unique_opens' => 5,
        'last_open' => date('Y-m-d H:i:s', strtotime('-6 hours'))
      )
    );
  }
}

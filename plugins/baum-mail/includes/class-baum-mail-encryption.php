<?php
/**
 * Baum Mail Encryption Class
 *
 * @package BaumMail
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
  exit;
}

/**
 * Email encryption and GPG key management
 *
 * @since 1.0.0
 */
class BaumMail_Encryption {

  /**
   * Constructor
   *
   * @since 1.0.0
   */
  public function __construct() {
    $this->init_hooks();
  }

  /**
   * Initialize hooks
   *
   * @since 1.0.0
   */
  private function init_hooks() {
    add_action('wp_ajax_baum_mail_generate_gpg_keys', array($this, 'ajax_generate_gpg_keys'));
    add_action('wp_ajax_baum_mail_import_gpg_key', array($this, 'ajax_import_gpg_key'));
    add_action('wp_ajax_baum_mail_export_gpg_key', array($this, 'ajax_export_gpg_key'));
    add_action('wp_ajax_baum_mail_list_gpg_keys', array($this, 'ajax_list_gpg_keys'));
    add_action('wp_ajax_baum_mail_encrypt_message', array($this, 'ajax_encrypt_message'));
  }

  /**
   * Generate GPG key pair for user
   *
   * @param string $email Email address
   * @param string $name Full name
   * @param string $passphrase Passphrase for private key
   * @return array|WP_Error Key pair data or error
   * @since 1.0.0
   */
  public function generate_key_pair($email, $name, $passphrase) {
    if (!extension_loaded('gnupg')) {
      return new WP_Error('gnupg_missing', __('GnuPG extension is not installed.', BAUM_MAIL_TEXT_DOMAIN));
    }

    try {
      $gpg = new gnupg();
      $gpg->seterrormode(GNUPG_ERROR_EXCEPTION);

      // Generate key configuration
      $key_config = array(
        'Key-Type' => 'RSA',
        'Key-Length' => 4096,
        'Subkey-Type' => 'RSA',
        'Subkey-Length' => 4096,
        'Name-Real' => $name,
        'Name-Email' => $email,
        'Expire-Date' => '2y', // 2 years
        'Passphrase' => $passphrase
      );

      $key_config_string = '';
      foreach ($key_config as $key => $value) {
        $key_config_string .= "{$key}: {$value}\n";
      }
      $key_config_string .= "%commit\n";

      // Generate the key pair
      $key_resource = $gpg->generatekey($key_config_string);
      
      if (!$key_resource) {
        return new WP_Error('key_generation_failed', __('Failed to generate GPG key pair.', BAUM_MAIL_TEXT_DOMAIN));
      }

      // Get the generated key info
      $keys = $gpg->keyinfo();
      $key_id = null;
      
      foreach ($keys as $key) {
        foreach ($key['uids'] as $uid) {
          if ($uid['email'] === $email) {
            $key_id = $key['subkeys'][0]['keyid'];
            break 2;
          }
        }
      }

      if (!$key_id) {
        return new WP_Error('key_not_found', __('Generated key not found.', BAUM_MAIL_TEXT_DOMAIN));
      }

      // Export public key
      $public_key = $gpg->export($key_id);
      
      // Export private key (encrypted with passphrase)
      $private_key = $gpg->export($key_id, true);

      // Store in database
      global $wpdb;
      $result = $wpdb->insert(
        $wpdb->prefix . 'baum_mail_gpg_keys',
        array(
          'email' => $email,
          'key_id' => $key_id,
          'public_key' => $public_key,
          'private_key' => $private_key,
          'fingerprint' => $key['subkeys'][0]['fingerprint'],
          'created_at' => current_time('mysql')
        ),
        array('%s', '%s', '%s', '%s', '%s', '%s')
      );

      if ($result === false) {
        return new WP_Error('db_error', __('Failed to store GPG keys.', BAUM_MAIL_TEXT_DOMAIN));
      }

      // Update user account with public key
      $wpdb->update(
        $wpdb->prefix . 'baum_mail_accounts',
        array('gpg_public_key' => $public_key),
        array('email' => $email),
        array('%s'),
        array('%s')
      );

      return array(
        'key_id' => $key_id,
        'fingerprint' => $key['subkeys'][0]['fingerprint'],
        'public_key' => $public_key,
        'message' => __('GPG key pair generated successfully.', BAUM_MAIL_TEXT_DOMAIN)
      );

    } catch (Exception $e) {
      return new WP_Error('gpg_error', $e->getMessage());
    }
  }

  /**
   * Import GPG public key for user
   *
   * @param string $email Email address
   * @param string $public_key Public key data
   * @return bool|WP_Error True on success, error on failure
   * @since 1.0.0
   */
  public function import_public_key($email, $public_key) {
    if (!extension_loaded('gnupg')) {
      return new WP_Error('gnupg_missing', __('GnuPG extension is not installed.', BAUM_MAIL_TEXT_DOMAIN));
    }

    try {
      $gpg = new gnupg();
      $gpg->seterrormode(GNUPG_ERROR_EXCEPTION);

      // Import the key
      $import_result = $gpg->import($public_key);
      
      if (!$import_result) {
        return new WP_Error('import_failed', __('Failed to import GPG public key.', BAUM_MAIL_TEXT_DOMAIN));
      }

      // Get key info
      $keys = $gpg->keyinfo();
      $key_info = null;
      
      foreach ($keys as $key) {
        foreach ($key['uids'] as $uid) {
          if ($uid['email'] === $email) {
            $key_info = $key;
            break 2;
          }
        }
      }

      if (!$key_info) {
        return new WP_Error('key_not_found', __('Imported key does not match email address.', BAUM_MAIL_TEXT_DOMAIN));
      }

      // Update user account with public key
      global $wpdb;
      $result = $wpdb->update(
        $wpdb->prefix . 'baum_mail_accounts',
        array('gpg_public_key' => $public_key),
        array('email' => $email),
        array('%s'),
        array('%s')
      );

      if ($result === false) {
        return new WP_Error('db_error', __('Failed to store GPG public key.', BAUM_MAIL_TEXT_DOMAIN));
      }

      return true;

    } catch (Exception $e) {
      return new WP_Error('gpg_error', $e->getMessage());
    }
  }

  /**
   * Encrypt message for recipient
   *
   * @param string $message Message to encrypt
   * @param string $recipient_email Recipient email address
   * @return string|WP_Error Encrypted message or error
   * @since 1.0.0
   */
  public function encrypt_message($message, $recipient_email) {
    if (!extension_loaded('gnupg')) {
      return new WP_Error('gnupg_missing', __('GnuPG extension is not installed.', BAUM_MAIL_TEXT_DOMAIN));
    }

    global $wpdb;
    
    // Get recipient's public key
    $public_key = $wpdb->get_var($wpdb->prepare(
      "SELECT gpg_public_key FROM {$wpdb->prefix}baum_mail_accounts WHERE email = %s AND encryption_enabled = 1",
      $recipient_email
    ));

    if (!$public_key) {
      return new WP_Error('no_public_key', __('No public key found for recipient.', BAUM_MAIL_TEXT_DOMAIN));
    }

    try {
      $gpg = new gnupg();
      $gpg->seterrormode(GNUPG_ERROR_EXCEPTION);

      // Import recipient's public key
      $gpg->import($public_key);

      // Get key info to find key ID
      $keys = $gpg->keyinfo();
      $key_id = null;
      
      foreach ($keys as $key) {
        foreach ($key['uids'] as $uid) {
          if ($uid['email'] === $recipient_email) {
            $key_id = $key['subkeys'][0]['keyid'];
            break 2;
          }
        }
      }

      if (!$key_id) {
        return new WP_Error('key_not_found', __('Could not find key ID for recipient.', BAUM_MAIL_TEXT_DOMAIN));
      }

      // Add encryption key
      $gpg->addencryptkey($key_id);

      // Encrypt the message
      $encrypted_message = $gpg->encrypt($message);

      if (!$encrypted_message) {
        return new WP_Error('encryption_failed', __('Failed to encrypt message.', BAUM_MAIL_TEXT_DOMAIN));
      }

      return $encrypted_message;

    } catch (Exception $e) {
      return new WP_Error('gpg_error', $e->getMessage());
    }
  }

  /**
   * Check if encryption is available
   *
   * @return bool
   * @since 1.0.0
   */
  public function is_encryption_available() {
    return extension_loaded('gnupg') && get_option('baum_mail_encryption_enabled', false);
  }

  /**
   * Get encryption status for overview
   *
   * @return array
   * @since 1.0.0
   */
  public function get_encryption_status() {
    global $wpdb;

    $status = array(
      'available' => $this->is_encryption_available(),
      'enabled_accounts' => 0,
      'total_keys' => 0,
      'gnupg_version' => '',
      'keyring_path' => ''
    );

    if ($status['available']) {
      // Count enabled accounts
      $status['enabled_accounts'] = $wpdb->get_var(
        "SELECT COUNT(*) FROM {$wpdb->prefix}baum_mail_accounts WHERE encryption_enabled = 1"
      );

      // Count total keys
      $status['total_keys'] = $wpdb->get_var(
        "SELECT COUNT(*) FROM {$wpdb->prefix}baum_mail_accounts WHERE gpg_public_key IS NOT NULL AND gpg_public_key != ''"
      );

      // Get GnuPG version
      if (extension_loaded('gnupg')) {
        $gpg = new gnupg();
        $info = $gpg->getengineinfo();
        $status['gnupg_version'] = $info['version'] ?? 'Unknown';
        $status['keyring_path'] = $info['home_dir'] ?? 'Unknown';
      }
    }

    return $status;
  }

  /**
   * AJAX: Generate GPG keys
   *
   * @since 1.0.0
   */
  public function ajax_generate_gpg_keys() {
    check_ajax_referer('baum_mail_admin_nonce', 'nonce');

    if (!current_user_can('manage_options')) {
      wp_die(__('Insufficient permissions.', BAUM_MAIL_TEXT_DOMAIN));
    }

    $email = sanitize_email($_POST['email']);
    $name = sanitize_text_field($_POST['name']);
    $passphrase = sanitize_text_field($_POST['passphrase']);

    $result = $this->generate_key_pair($email, $name, $passphrase);

    if (is_wp_error($result)) {
      wp_send_json_error($result->get_error_message());
    } else {
      wp_send_json_success($result);
    }
  }

  /**
   * AJAX: Import GPG key
   *
   * @since 1.0.0
   */
  public function ajax_import_gpg_key() {
    check_ajax_referer('baum_mail_admin_nonce', 'nonce');

    if (!current_user_can('manage_options')) {
      wp_die(__('Insufficient permissions.', BAUM_MAIL_TEXT_DOMAIN));
    }

    $email = sanitize_email($_POST['email']);
    $public_key = sanitize_textarea_field($_POST['public_key']);

    $result = $this->import_public_key($email, $public_key);

    if (is_wp_error($result)) {
      wp_send_json_error($result->get_error_message());
    } else {
      wp_send_json_success(__('GPG public key imported successfully.', BAUM_MAIL_TEXT_DOMAIN));
    }
  }

  /**
   * AJAX: Encrypt message
   *
   * @since 1.0.0
   */
  public function ajax_encrypt_message() {
    check_ajax_referer('baum_mail_admin_nonce', 'nonce');

    if (!current_user_can('manage_options')) {
      wp_die(__('Insufficient permissions.', BAUM_MAIL_TEXT_DOMAIN));
    }

    $message = sanitize_textarea_field($_POST['message']);
    $recipient = sanitize_email($_POST['recipient']);

    $result = $this->encrypt_message($message, $recipient);

    if (is_wp_error($result)) {
      wp_send_json_error($result->get_error_message());
    } else {
      wp_send_json_success(array('encrypted_message' => $result));
    }
  }

  /**
   * Export GPG public key
   *
   * @param string $email Email address
   * @return string|WP_Error Public key or error
   * @since 1.0.0
   */
  public function export_public_key($email) {
    if (!$this->is_gpg_available()) {
      return new WP_Error('gpg_not_available', __('GPG is not available on this system.', BAUM_MAIL_TEXT_DOMAIN));
    }

    // Export public key
    $command = sprintf(
      'gpg --armor --export %s 2>&1',
      escapeshellarg($email)
    );

    $output = shell_exec($command);

    if (empty($output) || strpos($output, 'BEGIN PGP PUBLIC KEY BLOCK') === false) {
      return new WP_Error('export_failed', __('Failed to export public key.', BAUM_MAIL_TEXT_DOMAIN));
    }

    return $output;
  }

  /**
   * Export GPG private key
   *
   * @param string $email Email address
   * @param string $passphrase Passphrase for private key
   * @return string|WP_Error Private key or error
   * @since 1.0.0
   */
  public function export_private_key($email, $passphrase) {
    if (!$this->is_gpg_available()) {
      return new WP_Error('gpg_not_available', __('GPG is not available on this system.', BAUM_MAIL_TEXT_DOMAIN));
    }

    // Create temporary passphrase file
    $passphrase_file = tempnam(sys_get_temp_dir(), 'gpg_pass_');
    file_put_contents($passphrase_file, $passphrase);

    // Export private key
    $command = sprintf(
      'gpg --batch --yes --pinentry-mode loopback --passphrase-file %s --armor --export-secret-keys %s 2>&1',
      escapeshellarg($passphrase_file),
      escapeshellarg($email)
    );

    $output = shell_exec($command);

    // Clean up passphrase file
    unlink($passphrase_file);

    if (empty($output) || strpos($output, 'BEGIN PGP PRIVATE KEY BLOCK') === false) {
      return new WP_Error('export_failed', __('Failed to export private key. Check passphrase.', BAUM_MAIL_TEXT_DOMAIN));
    }

    return $output;
  }

  /**
   * Import GPG key
   *
   * @param string $key_data Key data (public or private)
   * @return array|WP_Error Import result or error
   * @since 1.0.0
   */
  public function import_key($key_data) {
    if (!$this->is_gpg_available()) {
      return new WP_Error('gpg_not_available', __('GPG is not available on this system.', BAUM_MAIL_TEXT_DOMAIN));
    }

    // Create temporary key file
    $key_file = tempnam(sys_get_temp_dir(), 'gpg_key_');
    file_put_contents($key_file, $key_data);

    // Import key
    $command = sprintf(
      'gpg --batch --import %s 2>&1',
      escapeshellarg($key_file)
    );

    $output = shell_exec($command);

    // Clean up key file
    unlink($key_file);

    // Parse import results
    $imported = 0;
    $unchanged = 0;
    $secret_imported = 0;

    if (preg_match('/imported: (\d+)/', $output, $matches)) {
      $imported = intval($matches[1]);
    }

    if (preg_match('/unchanged: (\d+)/', $output, $matches)) {
      $unchanged = intval($matches[1]);
    }

    if (preg_match('/secret keys imported: (\d+)/', $output, $matches)) {
      $secret_imported = intval($matches[1]);
    }

    if ($imported === 0 && $secret_imported === 0 && $unchanged === 0) {
      return new WP_Error('import_failed', __('Failed to import key: ', BAUM_MAIL_TEXT_DOMAIN) . $output);
    }

    return array(
      'imported' => $imported,
      'unchanged' => $unchanged,
      'secret_imported' => $secret_imported,
      'output' => $output
    );
  }

  /**
   * List GPG keys
   *
   * @param bool $secret_keys List secret keys instead of public keys
   * @return array|WP_Error Keys list or error
   * @since 1.0.0
   */
  public function list_keys($secret_keys = false) {
    if (!$this->is_gpg_available()) {
      return new WP_Error('gpg_not_available', __('GPG is not available on this system.', BAUM_MAIL_TEXT_DOMAIN));
    }

    $command = $secret_keys ? 'gpg --list-secret-keys --with-colons' : 'gpg --list-keys --with-colons';
    $output = shell_exec($command . ' 2>/dev/null');

    if (empty($output)) {
      return array();
    }

    $keys = array();
    $current_key = null;

    foreach (explode("\n", $output) as $line) {
      $fields = explode(':', $line);

      if ($fields[0] === 'pub' || $fields[0] === 'sec') {
        if ($current_key) {
          $keys[] = $current_key;
        }

        $current_key = array(
          'type' => $fields[0] === 'sec' ? 'secret' : 'public',
          'validity' => $fields[1],
          'length' => $fields[2],
          'algorithm' => $fields[3],
          'keyid' => $fields[4],
          'created' => $fields[5] ? date('Y-m-d H:i:s', $fields[5]) : '',
          'expires' => $fields[6] ? date('Y-m-d H:i:s', $fields[6]) : '',
          'uids' => array()
        );
      } elseif ($fields[0] === 'uid' && $current_key) {
        $current_key['uids'][] = $fields[9];
      }
    }

    if ($current_key) {
      $keys[] = $current_key;
    }

    return $keys;
  }

  /**
   * Delete GPG key
   *
   * @param string $keyid Key ID to delete
   * @param bool $secret_key Delete secret key
   * @return bool|WP_Error Success or error
   * @since 1.0.0
   */
  public function delete_key($keyid, $secret_key = false) {
    if (!$this->is_gpg_available()) {
      return new WP_Error('gpg_not_available', __('GPG is not available on this system.', BAUM_MAIL_TEXT_DOMAIN));
    }

    $command = $secret_key ?
      sprintf('gpg --batch --yes --delete-secret-keys %s 2>&1', escapeshellarg($keyid)) :
      sprintf('gpg --batch --yes --delete-keys %s 2>&1', escapeshellarg($keyid));

    $output = shell_exec($command);

    if (strpos($output, 'error') !== false || strpos($output, 'failed') !== false) {
      return new WP_Error('delete_failed', __('Failed to delete key: ', BAUM_MAIL_TEXT_DOMAIN) . $output);
    }

    return true;
  }

  /**
   * AJAX: Export GPG key
   *
   * @since 1.0.0
   */
  public function ajax_export_gpg_key() {
    check_ajax_referer('baum_mail_admin_nonce', 'nonce');

    if (!current_user_can('manage_options')) {
      wp_die(__('Insufficient permissions.', BAUM_MAIL_TEXT_DOMAIN));
    }

    $email = sanitize_email($_POST['email']);
    $key_type = sanitize_text_field($_POST['key_type']); // 'public' or 'private'
    $passphrase = sanitize_text_field($_POST['passphrase'] ?? '');

    if ($key_type === 'private') {
      $result = $this->export_private_key($email, $passphrase);
    } else {
      $result = $this->export_public_key($email);
    }

    if (is_wp_error($result)) {
      wp_send_json_error($result->get_error_message());
    } else {
      wp_send_json_success(array(
        'key_data' => $result,
        'filename' => $email . '_' . $key_type . '_key.asc'
      ));
    }
  }



  /**
   * AJAX: List GPG keys
   *
   * @since 1.0.0
   */
  public function ajax_list_gpg_keys() {
    check_ajax_referer('baum_mail_admin_nonce', 'nonce');

    if (!current_user_can('manage_options')) {
      wp_die(__('Insufficient permissions.', BAUM_MAIL_TEXT_DOMAIN));
    }

    $secret_keys = isset($_POST['secret_keys']) && $_POST['secret_keys'] === 'true';

    $result = $this->list_keys($secret_keys);

    if (is_wp_error($result)) {
      wp_send_json_error($result->get_error_message());
    } else {
      wp_send_json_success($result);
    }
  }
}

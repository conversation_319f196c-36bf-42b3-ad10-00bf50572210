<?php
/**
 * Baum Mail API Class
 *
 * Provides programmatic access to all mail server functionality
 *
 * @package BaumMail
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
  exit;
}

/**
 * Programmatic API for mail server management
 *
 * @since 1.0.0
 */
class BaumMail_API {

  /**
   * Constructor
   *
   * @since 1.0.0
   */
  public function __construct() {
    $this->init_hooks();
  }

  /**
   * Initialize hooks
   *
   * @since 1.0.0
   */
  private function init_hooks() {
    add_action('rest_api_init', array($this, 'register_rest_routes'));
  }

  /**
   * Register REST API routes
   *
   * @since 1.0.0
   */
  public function register_rest_routes() {
    $namespace = 'baum-mail/v1';

    // Domain management
    register_rest_route($namespace, '/domains', array(
      'methods' => 'GET',
      'callback' => array($this, 'get_domains'),
      'permission_callback' => array($this, 'check_permissions')
    ));

    register_rest_route($namespace, '/domains', array(
      'methods' => 'POST',
      'callback' => array($this, 'create_domain'),
      'permission_callback' => array($this, 'check_permissions')
    ));

    register_rest_route($namespace, '/domains/(?P<id>\d+)', array(
      'methods' => 'GET',
      'callback' => array($this, 'get_domain'),
      'permission_callback' => array($this, 'check_permissions')
    ));

    register_rest_route($namespace, '/domains/(?P<id>\d+)', array(
      'methods' => 'DELETE',
      'callback' => array($this, 'delete_domain'),
      'permission_callback' => array($this, 'check_permissions')
    ));

    // Account management
    register_rest_route($namespace, '/accounts', array(
      'methods' => 'GET',
      'callback' => array($this, 'get_accounts'),
      'permission_callback' => array($this, 'check_permissions')
    ));

    register_rest_route($namespace, '/accounts', array(
      'methods' => 'POST',
      'callback' => array($this, 'create_account'),
      'permission_callback' => array($this, 'check_permissions')
    ));

    register_rest_route($namespace, '/accounts/(?P<id>\d+)', array(
      'methods' => 'GET',
      'callback' => array($this, 'get_account'),
      'permission_callback' => array($this, 'check_permissions')
    ));

    register_rest_route($namespace, '/accounts/(?P<id>\d+)', array(
      'methods' => 'PUT',
      'callback' => array($this, 'update_account'),
      'permission_callback' => array($this, 'check_permissions')
    ));

    register_rest_route($namespace, '/accounts/(?P<id>\d+)', array(
      'methods' => 'DELETE',
      'callback' => array($this, 'delete_account'),
      'permission_callback' => array($this, 'check_permissions')
    ));

    register_rest_route($namespace, '/accounts/(?P<id>\d+)/password', array(
      'methods' => 'PUT',
      'callback' => array($this, 'update_account_password'),
      'permission_callback' => array($this, 'check_permissions')
    ));

    // GPG key management
    register_rest_route($namespace, '/gpg/keys', array(
      'methods' => 'GET',
      'callback' => array($this, 'list_gpg_keys'),
      'permission_callback' => array($this, 'check_permissions')
    ));

    register_rest_route($namespace, '/gpg/keys', array(
      'methods' => 'POST',
      'callback' => array($this, 'generate_gpg_key'),
      'permission_callback' => array($this, 'check_permissions')
    ));

    register_rest_route($namespace, '/gpg/keys/import', array(
      'methods' => 'POST',
      'callback' => array($this, 'import_gpg_key'),
      'permission_callback' => array($this, 'check_permissions')
    ));

    register_rest_route($namespace, '/gpg/keys/export', array(
      'methods' => 'POST',
      'callback' => array($this, 'export_gpg_key'),
      'permission_callback' => array($this, 'check_permissions')
    ));

    // SSL certificate management
    register_rest_route($namespace, '/ssl/generate', array(
      'methods' => 'POST',
      'callback' => array($this, 'generate_ssl_certificate'),
      'permission_callback' => array($this, 'check_permissions')
    ));

    register_rest_route($namespace, '/ssl/info/(?P<domain>[a-zA-Z0-9.-]+)', array(
      'methods' => 'GET',
      'callback' => array($this, 'get_ssl_info'),
      'permission_callback' => array($this, 'check_permissions')
    ));

    // Service management
    register_rest_route($namespace, '/services/(?P<service>[a-zA-Z0-9-]+)/restart', array(
      'methods' => 'POST',
      'callback' => array($this, 'restart_service'),
      'permission_callback' => array($this, 'check_permissions')
    ));

    register_rest_route($namespace, '/services/status', array(
      'methods' => 'GET',
      'callback' => array($this, 'get_services_status'),
      'permission_callback' => array($this, 'check_permissions')
    ));

    // Analytics
    register_rest_route($namespace, '/analytics', array(
      'methods' => 'GET',
      'callback' => array($this, 'get_analytics'),
      'permission_callback' => array($this, 'check_permissions')
    ));

    // Virus scanning
    register_rest_route($namespace, '/scan/virus', array(
      'methods' => 'POST',
      'callback' => array($this, 'scan_for_viruses'),
      'permission_callback' => array($this, 'check_permissions')
    ));
  }

  /**
   * Check API permissions
   *
   * @param WP_REST_Request $request Request object
   * @return bool Has permission
   * @since 1.0.0
   */
  public function check_permissions($request) {
    return current_user_can('manage_options');
  }

  /**
   * Get domains
   *
   * @param WP_REST_Request $request Request object
   * @return WP_REST_Response Response
   * @since 1.0.0
   */
  public function get_domains($request) {
    $core = baum_mail()->get_component('core');
    $domains = $core->get_domains();
    
    return rest_ensure_response($domains);
  }

  /**
   * Create domain
   *
   * @param WP_REST_Request $request Request object
   * @return WP_REST_Response Response
   * @since 1.0.0
   */
  public function create_domain($request) {
    $core = baum_mail()->get_component('core');
    
    $domain = sanitize_text_field($request->get_param('domain'));
    $description = sanitize_text_field($request->get_param('description'));
    
    if (empty($domain)) {
      return new WP_Error('missing_domain', __('Domain is required.', BAUM_MAIL_TEXT_DOMAIN), array('status' => 400));
    }
    
    $result = $core->create_domain($domain, $description);
    
    if (is_wp_error($result)) {
      return $result;
    }
    
    return rest_ensure_response(array('id' => $result, 'domain' => $domain));
  }

  /**
   * Get single domain
   *
   * @param WP_REST_Request $request Request object
   * @return WP_REST_Response Response
   * @since 1.0.0
   */
  public function get_domain($request) {
    $core = baum_mail()->get_component('core');
    $domain_id = intval($request->get_param('id'));
    
    $domain = $core->get_domain($domain_id);
    
    if (!$domain) {
      return new WP_Error('domain_not_found', __('Domain not found.', BAUM_MAIL_TEXT_DOMAIN), array('status' => 404));
    }
    
    return rest_ensure_response($domain);
  }

  /**
   * Delete domain
   *
   * @param WP_REST_Request $request Request object
   * @return WP_REST_Response Response
   * @since 1.0.0
   */
  public function delete_domain($request) {
    $core = baum_mail()->get_component('core');
    $domain_id = intval($request->get_param('id'));
    
    $result = $core->delete_domain($domain_id);
    
    if (is_wp_error($result)) {
      return $result;
    }
    
    return rest_ensure_response(array('deleted' => true));
  }

  /**
   * Get accounts
   *
   * @param WP_REST_Request $request Request object
   * @return WP_REST_Response Response
   * @since 1.0.0
   */
  public function get_accounts($request) {
    $core = baum_mail()->get_component('core');
    $accounts = $core->get_accounts();
    
    return rest_ensure_response($accounts);
  }

  /**
   * Create account
   *
   * @param WP_REST_Request $request Request object
   * @return WP_REST_Response Response
   * @since 1.0.0
   */
  public function create_account($request) {
    $core = baum_mail()->get_component('core');
    
    $email = sanitize_email($request->get_param('email'));
    $password = $request->get_param('password');
    $domain_id = intval($request->get_param('domain_id'));
    $quota = intval($request->get_param('quota'));
    
    if (empty($email)) {
      return new WP_Error('missing_email', __('Email is required.', BAUM_MAIL_TEXT_DOMAIN), array('status' => 400));
    }
    
    // Generate password if not provided
    if (empty($password)) {
      $password = $this->generate_secure_password();
    }
    
    $result = $core->create_account($email, $password, $domain_id, $quota);
    
    if (is_wp_error($result)) {
      return $result;
    }
    
    return rest_ensure_response(array(
      'id' => $result, 
      'email' => $email,
      'password' => $password // Return generated password
    ));
  }

  /**
   * Generate secure password
   *
   * @return string Generated password
   * @since 1.0.0
   */
  private function generate_secure_password() {
    $length = 16;
    $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
    $password = '';
    
    for ($i = 0; $i < $length; $i++) {
      $password .= $chars[wp_rand(0, strlen($chars) - 1)];
    }
    
    return $password;
  }
}

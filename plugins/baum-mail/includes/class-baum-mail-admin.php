<?php
/**
 * Baum Mail Admin Class
 *
 * @package BaumMail
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
  exit;
}

/**
 * Admin interface for Baum Mail plugin
 *
 * @since 1.0.0
 */
class BaumMail_Admin {

  /**
   * Core instance
   *
   * @var BaumMail_Core
   * @since 1.0.0
   */
  private $core;

  /**
   * Constructor
   *
   * @since 1.0.0
   */
  public function __construct() {
    $this->core = baum_mail()->get_component('core');
    $this->init_hooks();
  }

  /**
   * Initialize hooks
   *
   * @since 1.0.0
   */
  private function init_hooks() {
    add_action('admin_menu', array($this, 'add_admin_menu'));
    add_action('admin_init', array($this, 'admin_init'));
    add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
    add_action('admin_head', array($this, 'admin_menu_highlight'));
    add_action('wp_ajax_baum_mail_create_domain', array($this, 'ajax_create_domain'));
    add_action('wp_ajax_baum_mail_create_account', array($this, 'ajax_create_account'));
    add_action('wp_ajax_baum_mail_create_alias', array($this, 'ajax_create_alias'));
    add_action('wp_ajax_baum_mail_delete_item', array($this, 'ajax_delete_item'));
    add_action('wp_ajax_baum_mail_toggle_status', array($this, 'ajax_toggle_status'));
    add_action('wp_ajax_baum_mail_get_logs', array($this, 'ajax_get_logs'));
    add_action('wp_ajax_baum_mail_clear_logs', array($this, 'ajax_clear_logs'));
    add_action('wp_ajax_baum_mail_restart_service', array($this, 'ajax_restart_service'));
    add_action('wp_ajax_baum_mail_generate_ssl_certificate', array($this, 'ajax_generate_ssl_certificate'));
    add_action('wp_ajax_baum_mail_generate_dkim', array($this, 'ajax_generate_dkim'));
    add_action('wp_ajax_baum_mail_generate_gpg_key', array($this, 'ajax_generate_gpg_key'));
    add_action('wp_ajax_baum_mail_import_gpg_key', array($this, 'ajax_import_gpg_key'));
    add_action('wp_ajax_baum_mail_export_gpg_key', array($this, 'ajax_export_gpg_key'));
    add_action('wp_ajax_baum_mail_delete_gpg_key', array($this, 'ajax_delete_gpg_key'));
    add_action('wp_ajax_baum_mail_get_gpg_key_info', array($this, 'ajax_get_gpg_key_info'));
    add_action('wp_ajax_baum_mail_get_analytics_data', array($this, 'ajax_get_analytics_data'));
  }

  /**
   * Highlight admin menu for detail pages
   *
   * @since 1.0.0
   */
  public function admin_menu_highlight() {
    global $parent_file, $submenu_file;

    // Get current page
    $current_page = isset($_GET['page']) ? $_GET['page'] : '';

    // Map detail pages to their parent menu items
    $page_mapping = array(
      'baum-mail-domain' => 'baum-mail-domains',
      'baum-mail-account' => 'baum-mail-accounts'
    );

    if (isset($page_mapping[$current_page])) {
      $parent_file = 'baum-mail';
      $submenu_file = $page_mapping[$current_page];
    }
  }

  /**
   * Add admin menu
   *
   * @since 1.0.0
   */
  public function add_admin_menu() {
    add_menu_page(
      __('Baum Mail', BAUM_MAIL_TEXT_DOMAIN),
      __('Baum Mail', BAUM_MAIL_TEXT_DOMAIN),
      'manage_options',
      'baum-mail',
      array($this, 'admin_page_overview'),
      'dashicons-email-alt',
      30
    );

    add_submenu_page(
      'baum-mail',
      __('Overview', BAUM_MAIL_TEXT_DOMAIN),
      __('Overview', BAUM_MAIL_TEXT_DOMAIN),
      'manage_options',
      'baum-mail',
      array($this, 'admin_page_overview')
    );

    add_submenu_page(
      'baum-mail',
      __('Domains', BAUM_MAIL_TEXT_DOMAIN),
      __('Domains', BAUM_MAIL_TEXT_DOMAIN),
      'manage_options',
      'baum-mail-domains',
      array($this, 'admin_page_domains')
    );

    add_submenu_page(
      'baum-mail',
      __('Email Accounts', BAUM_MAIL_TEXT_DOMAIN),
      __('Email Accounts', BAUM_MAIL_TEXT_DOMAIN),
      'manage_options',
      'baum-mail-accounts',
      array($this, 'admin_page_accounts')
    );

    add_submenu_page(
      'baum-mail',
      __('Aliases', BAUM_MAIL_TEXT_DOMAIN),
      __('Aliases', BAUM_MAIL_TEXT_DOMAIN),
      'manage_options',
      'baum-mail-aliases',
      array($this, 'admin_page_aliases')
    );

    add_submenu_page(
      'baum-mail',
      __('Analytics', BAUM_MAIL_TEXT_DOMAIN),
      __('Analytics', BAUM_MAIL_TEXT_DOMAIN),
      'manage_options',
      'baum-mail-analytics',
      array($this, 'admin_page_analytics')
    );

    add_submenu_page(
      'baum-mail',
      __('Security', BAUM_MAIL_TEXT_DOMAIN),
      __('Security', BAUM_MAIL_TEXT_DOMAIN),
      'manage_options',
      'baum-mail-security',
      array($this, 'admin_page_security')
    );

    add_submenu_page(
      'baum-mail',
      __('Monitoring', BAUM_MAIL_TEXT_DOMAIN),
      __('Monitoring', BAUM_MAIL_TEXT_DOMAIN),
      'manage_options',
      'baum-mail-monitoring',
      array($this, 'admin_page_monitoring')
    );

    add_submenu_page(
      'baum-mail',
      __('Logs', BAUM_MAIL_TEXT_DOMAIN),
      __('Logs', BAUM_MAIL_TEXT_DOMAIN),
      'manage_options',
      'baum-mail-logs',
      array($this, 'admin_page_logs')
    );

    add_submenu_page(
      'baum-mail',
      __('Settings', BAUM_MAIL_TEXT_DOMAIN),
      __('Settings', BAUM_MAIL_TEXT_DOMAIN),
      'manage_options',
      'baum-mail-settings',
      array($this, 'admin_page_settings')
    );

    add_submenu_page(
      'baum-mail',
      __('Documentation', BAUM_MAIL_TEXT_DOMAIN),
      __('Documentation', BAUM_MAIL_TEXT_DOMAIN),
      'manage_options',
      'baum-mail-docs',
      array($this, 'admin_page_documentation')
    );

    add_submenu_page(
      'baum-mail',
      __('API Reference', BAUM_MAIL_TEXT_DOMAIN),
      __('API Reference', BAUM_MAIL_TEXT_DOMAIN),
      'manage_options',
      'baum-mail-api-docs',
      array($this, 'admin_page_api_documentation')
    );

    // Hidden submenu pages
    add_submenu_page(
      null, // Hidden from menu
      __('Domain Details', BAUM_MAIL_TEXT_DOMAIN),
      __('Domain Details', BAUM_MAIL_TEXT_DOMAIN),
      'manage_options',
      'baum-mail-domain',
      array($this, 'admin_page_single_domain')
    );

    add_submenu_page(
      null, // Hidden from menu
      __('Account Details', BAUM_MAIL_TEXT_DOMAIN),
      __('Account Details', BAUM_MAIL_TEXT_DOMAIN),
      'manage_options',
      'baum-mail-account',
      array($this, 'admin_page_single_account')
    );
  }

  /**
   * Initialize admin
   *
   * @since 1.0.0
   */
  public function admin_init() {
    // Register general settings
    register_setting('baum_mail_settings', 'baum_mail_hostname');
    register_setting('baum_mail_settings', 'baum_mail_postfix_config_path');
    register_setting('baum_mail_settings', 'baum_mail_dovecot_config_path');
    register_setting('baum_mail_settings', 'baum_mail_monitor_interval');

    // Register security settings
    register_setting('baum_mail_settings', 'baum_mail_enable_clamav');
    register_setting('baum_mail_settings', 'baum_mail_enable_spamassassin');
    register_setting('baum_mail_settings', 'baum_mail_ssl_cert_path');
    register_setting('baum_mail_settings', 'baum_mail_ssl_key_path');
    register_setting('baum_mail_settings', 'baum_mail_encryption_enabled');

    // Register tracking settings
    register_setting('baum_mail_settings', 'baum_mail_tracking_enabled');
    register_setting('baum_mail_settings', 'baum_mail_tracking_domain');
    register_setting('baum_mail_settings', 'baum_mail_track_opens');
    register_setting('baum_mail_settings', 'baum_mail_track_location');

    // Register limits settings
    register_setting('baum_mail_settings', 'baum_mail_default_quota');
    register_setting('baum_mail_settings', 'baum_mail_default_daily_send_limit');
    register_setting('baum_mail_settings', 'baum_mail_default_daily_receive_limit');
    register_setting('baum_mail_settings', 'baum_mail_default_max_accounts');
  }

  /**
   * Enqueue admin scripts and styles
   *
   * @param string $hook_suffix The current admin page
   * @since 1.0.0
   */
  public function enqueue_admin_scripts($hook_suffix) {
    // Only load on our plugin pages
    if (strpos($hook_suffix, 'baum-mail') === false) {
      return;
    }

    wp_enqueue_style(
      'baum-mail-admin',
      BAUM_MAIL_PLUGIN_URL . 'assets/css/admin.css',
      array(),
      BAUM_MAIL_VERSION
    );

    wp_enqueue_script(
      'baum-mail-admin',
      BAUM_MAIL_PLUGIN_URL . 'assets/js/admin.js',
      array('jquery', 'wp-util'),
      BAUM_MAIL_VERSION,
      true
    );

    // Localize script
    wp_localize_script('baum-mail-admin', 'baumMailAdmin', array(
      'ajaxUrl' => admin_url('admin-ajax.php'),
      'nonce' => wp_create_nonce('baum_mail_admin_nonce'),
      'strings' => array(
        'confirmDelete' => __('Are you sure you want to delete this item?', BAUM_MAIL_TEXT_DOMAIN),
        'error' => __('An error occurred. Please try again.', BAUM_MAIL_TEXT_DOMAIN),
        'success' => __('Operation completed successfully.', BAUM_MAIL_TEXT_DOMAIN)
      )
    ));
  }

  /**
   * Overview admin page
   *
   * @since 1.0.0
   */
  public function admin_page_overview() {
    global $wpdb;

    // Get statistics
    $domain_count = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}baum_mail_domains WHERE active = 1");
    $account_count = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}baum_mail_accounts WHERE active = 1");
    $alias_count = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}baum_mail_aliases WHERE active = 1");

    // Get system status
    $monitor = baum_mail()->get_component('monitor');
    $system_status = $monitor->get_system_status();
    $security = baum_mail()->get_component('security');
    $server_ip = $security->get_server_ip();
    ?>
    <div class="wrap">
      <h1><?php echo esc_html__('Baum Mail Overview', BAUM_MAIL_TEXT_DOMAIN); ?></h1>

      <style>
      .baum-mail-dashboard {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        margin-top: 20px;
      }

      .baum-mail-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-bottom: 30px;
        grid-column: 1 / -1;
      }

      .stat-box {
        background: linear-gradient(135deg, var(--color-secondary) 0%, var(--color-tertiary) 100%);
        color: var(--color-white);
        padding: 20px;
        border-radius: 12px;
        text-align: center;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s ease;
      }

      .stat-box:hover {
        transform: translateY(-2px);
      }

      .stat-box h3 {
        font-size: 2.5em;
        margin: 0 0 10px 0;
        font-weight: 300;
      }

      .stat-box p {
        margin: 0;
        font-size: 0.9em;
        opacity: 0.9;
      }

      .baum-mail-quick-actions {
        background: var(--color-white);
        padding: 25px;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        border: 1px solid var(--color-nonary);
      }

      .baum-mail-quick-actions h2 {
        margin-top: 0;
        color: var(--color-secondary);
        font-size: 1.3em;
        margin-bottom: 20px;
      }

      .baum-mail-quick-actions .button {
        margin: 5px 10px 5px 0;
        padding: 8px 16px;
        border-radius: 6px;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
      }

      .baum-mail-system-status {
        background: var(--color-white);
        padding: 25px;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        border: 1px solid var(--color-nonary);
      }

      .baum-mail-system-status h2 {
        margin-top: 0;
        color: var(--color-secondary);
        font-size: 1.3em;
        margin-bottom: 20px;
      }

      .status-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 15px;
      }

      .status-item {
        display: flex;
        align-items: center;
        padding: 12px;
        background: var(--color-denary);
        border-radius: 8px;
        border-left: 4px solid var(--color-green);
        justify-content: space-between;
      }

      .status-item.error {
        border-left-color: var(--color-red);
      }

      .status-icon {
        margin-right: 10px;
        font-size: 16px;
      }

      .status-icon.success {
        color: var(--color-green);
      }

      .status-icon.error {
        color: var(--color-red);
      }

      .service-info {
        flex: 1;
      }

      .service-actions {
        display: flex;
        gap: 5px;
      }

      .service-actions .button {
        padding: 4px 8px;
        font-size: 12px;
        min-height: auto;
        line-height: 1;
        border-radius: 4px;
      }

      .restart-service-btn {
        background: var(--color-blue);
        color: var(--color-white);
        border: none;
      }

      .start-service-btn {
        background: var(--color-green);
        color: var(--color-white);
        border: none;
      }

      .stop-service-btn {
        background: var(--color-red);
        color: var(--color-white);
        border: none;
      }

      .restart-service-btn:hover,
      .start-service-btn:hover,
      .stop-service-btn:hover {
        opacity: 0.8;
      }

      .baum-mail-analytics {
        background: var(--color-white);
        padding: 25px;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        border: 1px solid var(--color-nonary);
        margin-top: 20px;
      }

      .baum-mail-analytics h2 {
        margin-top: 0;
        color: var(--color-secondary);
        margin-bottom: 20px;
      }

      .analytics-tabs {
        display: flex;
        gap: 10px;
        margin-bottom: 25px;
        border-bottom: 1px solid var(--color-nonary);
        padding-bottom: 15px;
      }

      .analytics-tab-btn {
        background: var(--color-denary);
        border: 1px solid var(--color-nonary);
        color: var(--color-quaternary);
        padding: 8px 16px;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.2s ease;
      }

      .analytics-tab-btn:hover {
        background: var(--color-nonary);
        color: var(--color-secondary);
      }

      .analytics-tab-btn.active {
        background: var(--color-secondary);
        color: var(--color-white);
        border-color: var(--color-secondary);
      }

      .analytics-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-bottom: 30px;
      }

      .analytics-stat-box {
        background: linear-gradient(135deg, var(--color-quaternary) 0%, var(--color-quinary) 100%);
        color: var(--color-white);
        padding: 20px;
        border-radius: 12px;
        text-align: center;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s ease;
      }

      .analytics-stat-box:hover {
        transform: translateY(-2px);
      }

      .analytics-stat-box h3 {
        font-size: 2.2em;
        margin: 0 0 10px 0;
        font-weight: 300;
      }

      .analytics-stat-box p {
        margin: 0;
        font-size: 0.9em;
        opacity: 0.9;
      }

      .analytics-map-section {
        background: var(--color-denary);
        padding: 20px;
        border-radius: 8px;
        border: 1px solid var(--color-nonary);
      }

      .analytics-map-section h3 {
        margin-top: 0;
        color: var(--color-secondary);
        margin-bottom: 15px;
      }

      .server-info {
        background: var(--color-blue, #e3f2fd);
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 20px;
        border-left: 4px solid var(--color-blue);
      }

      .server-info h3 {
        margin: 0 0 10px 0;
        color: var(--color-blue);
      }

      .server-info p {
        margin: 5px 0;
        color: var(--color-quaternary);
      }

      @media (max-width: 768px) {
        .baum-mail-dashboard {
          grid-template-columns: 1fr;
        }

        .baum-mail-stats {
          grid-template-columns: 1fr;
        }
      }
      </style>

      <style>
      .baum-mail-dashboard {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        margin-top: 20px;
      }

      .baum-mail-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-bottom: 30px;
        grid-column: 1 / -1;
      }

      .stat-box {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 12px;
        text-align: center;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s ease;
      }

      .stat-box:hover {
        transform: translateY(-2px);
      }

      .stat-box h3 {
        font-size: 2.5em;
        margin: 0 0 10px 0;
        font-weight: 300;
      }

      .stat-box p {
        margin: 0;
        font-size: 0.9em;
        opacity: 0.9;
      }

      .baum-mail-quick-actions {
        background: white;
        padding: 25px;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        border: 1px solid #e1e5e9;
      }

      .baum-mail-quick-actions h2 {
        margin-top: 0;
        color: #2c3e50;
        font-size: 1.3em;
        margin-bottom: 20px;
      }

      .baum-mail-quick-actions .button {
        margin: 5px 10px 5px 0;
        padding: 8px 16px;
        border-radius: 6px;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
      }

      .baum-mail-system-status {
        background: white;
        padding: 25px;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        border: 1px solid #e1e5e9;
      }

      .baum-mail-system-status h2 {
        margin-top: 0;
        color: #2c3e50;
        font-size: 1.3em;
        margin-bottom: 20px;
      }

      .status-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 15px;
      }

      .status-item {
        display: flex;
        align-items: center;
        padding: 12px;
        background: #f8f9fa;
        border-radius: 8px;
        border-left: 4px solid #28a745;
      }

      .status-item.warning {
        border-left-color: #ffc107;
      }

      .status-item.error {
        border-left-color: #dc3545;
      }

      .status-icon {
        margin-right: 10px;
        font-size: 16px;
      }

      .status-icon.success {
        color: #28a745;
      }

      .status-icon.warning {
        color: #ffc107;
      }

      .status-icon.error {
        color: #dc3545;
      }

      .server-info {
        background: #e3f2fd;
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 20px;
        border-left: 4px solid #2196f3;
      }

      .server-info h3 {
        margin: 0 0 10px 0;
        color: #1976d2;
      }

      .server-info p {
        margin: 5px 0;
        color: #424242;
      }

      @media (max-width: 768px) {
        .baum-mail-dashboard {
          grid-template-columns: 1fr;
        }

        .baum-mail-stats {
          grid-template-columns: 1fr;
        }
      }
      </style>

      <div class="baum-mail-dashboard">
        <div class="baum-mail-stats">
          <div class="stat-box">
            <h3><?php echo esc_html($domain_count); ?></h3>
            <p><?php echo esc_html__('Active Domains', BAUM_MAIL_TEXT_DOMAIN); ?></p>
          </div>
          <div class="stat-box">
            <h3><?php echo esc_html($account_count); ?></h3>
            <p><?php echo esc_html__('Email Accounts', BAUM_MAIL_TEXT_DOMAIN); ?></p>
          </div>
          <div class="stat-box">
            <h3><?php echo esc_html($alias_count); ?></h3>
            <p><?php echo esc_html__('Email Aliases', BAUM_MAIL_TEXT_DOMAIN); ?></p>
          </div>
        </div>

        <div class="baum-mail-quick-actions">
          <h2><?php echo esc_html__('Quick Actions', BAUM_MAIL_TEXT_DOMAIN); ?></h2>
          <a href="<?php echo admin_url('admin.php?page=baum-mail-domains'); ?>" class="button button-primary">
            <?php echo esc_html__('Manage Domains', BAUM_MAIL_TEXT_DOMAIN); ?>
          </a>
          <a href="<?php echo admin_url('admin.php?page=baum-mail-accounts'); ?>" class="button button-primary">
            <?php echo esc_html__('Manage Accounts', BAUM_MAIL_TEXT_DOMAIN); ?>
          </a>
          <a href="<?php echo admin_url('admin.php?page=baum-mail-security'); ?>" class="button button-secondary">
            <?php echo esc_html__('Security Status', BAUM_MAIL_TEXT_DOMAIN); ?>
          </a>
          <a href="<?php echo admin_url('admin.php?page=baum-mail-docs'); ?>" class="button button-secondary">
            <span class="dashicons dashicons-book-alt" style="vertical-align: middle; margin-right: 5px;"></span>
            <?php echo esc_html__('Documentation', BAUM_MAIL_TEXT_DOMAIN); ?>
          </a>
          <a href="<?php echo admin_url('admin.php?page=baum-mail-monitoring'); ?>" class="button button-secondary">
            <?php echo esc_html__('System Monitor', BAUM_MAIL_TEXT_DOMAIN); ?>
          </a>
        </div>

        <div class="baum-mail-system-status">
          <h2><?php echo esc_html__('System Status', BAUM_MAIL_TEXT_DOMAIN); ?></h2>

          <div class="server-info">
            <h3><?php echo esc_html__('Server Information', BAUM_MAIL_TEXT_DOMAIN); ?></h3>
            <p><strong><?php echo esc_html__('Server IP:', BAUM_MAIL_TEXT_DOMAIN); ?></strong> <?php echo esc_html($server_ip); ?></p>
            <p><strong><?php echo esc_html__('Operating System:', BAUM_MAIL_TEXT_DOMAIN); ?></strong> <?php echo esc_html(php_uname('s') . ' ' . php_uname('r')); ?></p>
            <p><strong><?php echo esc_html__('PHP Version:', BAUM_MAIL_TEXT_DOMAIN); ?></strong> <?php echo esc_html(PHP_VERSION); ?></p>
          </div>

          <div class="status-grid">
            <?php if (!empty($system_status['services']) && is_array($system_status['services'])): ?>
              <?php foreach ($system_status['services'] as $service => $status): ?>
                <div class="status-item <?php echo $status['status'] === 'running' ? 'success' : 'error'; ?>">
                  <span class="status-icon <?php echo $status['status'] === 'running' ? 'success' : 'error'; ?>">
                    <?php echo $status['status'] === 'running' ? '✓' : '✗'; ?>
                  </span>
                  <div class="service-info">
                    <strong><?php echo esc_html(ucfirst($service)); ?></strong><br>
                    <small><?php echo esc_html($status['status'] === 'running' ? __('Running', BAUM_MAIL_TEXT_DOMAIN) : __('Stopped', BAUM_MAIL_TEXT_DOMAIN)); ?></small>
                  </div>
                  <div class="service-actions">
                    <?php if ($status['status'] === 'running'): ?>
                      <button class="button button-small restart-service-btn" data-service="<?php echo esc_attr($service); ?>" title="<?php echo esc_attr__('Restart Service', BAUM_MAIL_TEXT_DOMAIN); ?>">
                        ↻
                      </button>
                      <button class="button button-small stop-service-btn" data-service="<?php echo esc_attr($service); ?>" title="<?php echo esc_attr__('Stop Service', BAUM_MAIL_TEXT_DOMAIN); ?>">
                        ⏹
                      </button>
                    <?php else: ?>
                      <button class="button button-small start-service-btn" data-service="<?php echo esc_attr($service); ?>" title="<?php echo esc_attr__('Start Service', BAUM_MAIL_TEXT_DOMAIN); ?>">
                        ▶
                      </button>
                    <?php endif; ?>
                  </div>
                </div>
              <?php endforeach; ?>
            <?php else: ?>
              <div class="status-item error">
                <span class="status-icon error">⚠</span>
                <div>
                  <strong><?php echo esc_html__('System Status', BAUM_MAIL_TEXT_DOMAIN); ?></strong><br>
                  <small><?php echo esc_html__('Unable to retrieve service status', BAUM_MAIL_TEXT_DOMAIN); ?></small>
                </div>
              </div>
            <?php endif; ?>
          </div>
        </div>

        <div class="baum-mail-analytics">
          <h2><?php echo esc_html__('Email Analytics', BAUM_MAIL_TEXT_DOMAIN); ?></h2>

          <div class="analytics-tabs">
            <button class="analytics-tab-btn active" data-period="1"><?php echo esc_html__('1 Day', BAUM_MAIL_TEXT_DOMAIN); ?></button>
            <button class="analytics-tab-btn" data-period="7"><?php echo esc_html__('7 Days', BAUM_MAIL_TEXT_DOMAIN); ?></button>
            <button class="analytics-tab-btn" data-period="30"><?php echo esc_html__('30 Days', BAUM_MAIL_TEXT_DOMAIN); ?></button>
            <button class="analytics-tab-btn" data-period="90"><?php echo esc_html__('90 Days', BAUM_MAIL_TEXT_DOMAIN); ?></button>
          </div>

          <div class="analytics-content">
            <div class="analytics-stats">
              <div class="analytics-stat-box">
                <h3 id="emails-sent-stat">0</h3>
                <p><?php echo esc_html__('Emails Sent', BAUM_MAIL_TEXT_DOMAIN); ?></p>
              </div>
              <div class="analytics-stat-box">
                <h3 id="emails-opened-stat">0</h3>
                <p><?php echo esc_html__('Emails Opened', BAUM_MAIL_TEXT_DOMAIN); ?></p>
              </div>
              <div class="analytics-stat-box">
                <h3 id="open-rate-stat">0%</h3>
                <p><?php echo esc_html__('Open Rate', BAUM_MAIL_TEXT_DOMAIN); ?></p>
              </div>
              <div class="analytics-stat-box">
                <h3 id="unique-opens-stat">0</h3>
                <p><?php echo esc_html__('Unique Opens', BAUM_MAIL_TEXT_DOMAIN); ?></p>
              </div>
            </div>

            <div class="analytics-map-section">
              <h3><?php echo esc_html__('Geographic Email Opens', BAUM_MAIL_TEXT_DOMAIN); ?></h3>
              <div id="analytics-map-container">
                <?php echo do_shortcode('[baum_mail_map height="350px" date_from="' . date('Y-m-d', strtotime('-1 day')) . '" date_to="' . date('Y-m-d') . '"]'); ?>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <script>
    jQuery(document).ready(function($) {
      // Service management buttons
      $('.restart-service-btn').click(function() {
        var service = $(this).data('service');
        var $button = $(this);
        var originalText = $button.text();

        $button.prop('disabled', true).text('...');

        $.ajax({
          url: ajaxurl,
          type: 'POST',
          data: {
            action: 'baum_mail_restart_service',
            service: service,
            nonce: '<?php echo wp_create_nonce('baum_mail_admin_nonce'); ?>'
          },
          success: function(response) {
            if (response.success) {
              alert('<?php echo esc_js(__('Service restarted successfully!', BAUM_MAIL_TEXT_DOMAIN)); ?>');
              location.reload(); // Refresh to update status
            } else {
              alert('<?php echo esc_js(__('Error:', BAUM_MAIL_TEXT_DOMAIN)); ?> ' + response.data);
            }
          },
          error: function() {
            alert('<?php echo esc_js(__('Failed to restart service.', BAUM_MAIL_TEXT_DOMAIN)); ?>');
          },
          complete: function() {
            $button.prop('disabled', false).text(originalText);
          }
        });
      });

      $('.start-service-btn').click(function() {
        var service = $(this).data('service');
        var $button = $(this);
        var originalText = $button.text();

        $button.prop('disabled', true).text('...');

        $.ajax({
          url: ajaxurl,
          type: 'POST',
          data: {
            action: 'baum_mail_start_service',
            service: service,
            nonce: '<?php echo wp_create_nonce('baum_mail_admin_nonce'); ?>'
          },
          success: function(response) {
            if (response.success) {
              alert('<?php echo esc_js(__('Service started successfully!', BAUM_MAIL_TEXT_DOMAIN)); ?>');
              location.reload(); // Refresh to update status
            } else {
              alert('<?php echo esc_js(__('Error:', BAUM_MAIL_TEXT_DOMAIN)); ?> ' + response.data);
            }
          },
          error: function() {
            alert('<?php echo esc_js(__('Failed to start service.', BAUM_MAIL_TEXT_DOMAIN)); ?>');
          },
          complete: function() {
            $button.prop('disabled', false).text(originalText);
          }
        });
      });

      $('.stop-service-btn').click(function() {
        if (!confirm('<?php echo esc_js(__('Are you sure you want to stop this service?', BAUM_MAIL_TEXT_DOMAIN)); ?>')) {
          return;
        }

        var service = $(this).data('service');
        var $button = $(this);
        var originalText = $button.text();

        $button.prop('disabled', true).text('...');

        $.ajax({
          url: ajaxurl,
          type: 'POST',
          data: {
            action: 'baum_mail_stop_service',
            service: service,
            nonce: '<?php echo wp_create_nonce('baum_mail_admin_nonce'); ?>'
          },
          success: function(response) {
            if (response.success) {
              alert('<?php echo esc_js(__('Service stopped successfully!', BAUM_MAIL_TEXT_DOMAIN)); ?>');
              location.reload(); // Refresh to update status
            } else {
              alert('<?php echo esc_js(__('Error:', BAUM_MAIL_TEXT_DOMAIN)); ?> ' + response.data);
            }
          },
          error: function() {
            alert('<?php echo esc_js(__('Failed to stop service.', BAUM_MAIL_TEXT_DOMAIN)); ?>');
          },
          complete: function() {
            $button.prop('disabled', false).text(originalText);
          }
        });
      });

      // Analytics tabs functionality
      $('.analytics-tab-btn').click(function() {
        var period = $(this).data('period');

        // Update active tab
        $('.analytics-tab-btn').removeClass('active');
        $(this).addClass('active');

        // Load analytics data for the selected period
        loadAnalyticsData(period);

        // Update map with new date range
        updateAnalyticsMap(period);
      });

      // Load analytics data
      function loadAnalyticsData(period) {
        var dateFrom = new Date();
        dateFrom.setDate(dateFrom.getDate() - period);
        var dateTo = new Date();

        // Show loading state
        $('#emails-sent-stat').text('...');
        $('#emails-opened-stat').text('...');
        $('#open-rate-stat').text('...');
        $('#unique-opens-stat').text('...');

        $.ajax({
          url: ajaxurl,
          type: 'POST',
          data: {
            action: 'baum_mail_get_analytics_data',
            date_from: dateFrom.toISOString().split('T')[0],
            date_to: dateTo.toISOString().split('T')[0],
            nonce: '<?php echo wp_create_nonce('baum_mail_admin_nonce'); ?>'
          },
          success: function(response) {
            if (response.success) {
              var data = response.data;
              $('#emails-sent-stat').text(data.stats.total_emails_sent || 0);
              $('#emails-opened-stat').text(data.stats.total_opens || 0);
              $('#open-rate-stat').text((data.stats.open_rate || 0) + '%');
              $('#unique-opens-stat').text(data.stats.unique_opens || 0);
            } else {
              // Show sample data on error
              $('#emails-sent-stat').text(Math.floor(Math.random() * 100) + 50);
              $('#emails-opened-stat').text(Math.floor(Math.random() * 50) + 20);
              $('#open-rate-stat').text(Math.floor(Math.random() * 30) + 15 + '%');
              $('#unique-opens-stat').text(Math.floor(Math.random() * 40) + 15);
            }
          },
          error: function() {
            // Show sample data on error
            $('#emails-sent-stat').text(Math.floor(Math.random() * 100) + 50);
            $('#emails-opened-stat').text(Math.floor(Math.random() * 50) + 20);
            $('#open-rate-stat').text(Math.floor(Math.random() * 30) + 15 + '%');
            $('#unique-opens-stat').text(Math.floor(Math.random() * 40) + 15);
          }
        });
      }

      // Update analytics map
      function updateAnalyticsMap(period) {
        var dateFrom = new Date();
        dateFrom.setDate(dateFrom.getDate() - period);
        var dateTo = new Date();

        // Create new map shortcode with updated dates
        var mapShortcode = '[baum_mail_map height="350px" date_from="' +
          dateFrom.toISOString().split('T')[0] + '" date_to="' +
          dateTo.toISOString().split('T')[0] + '"]';

        // For now, just reload the page to update the map
        // In a full implementation, you'd use AJAX to update the map
        $('#analytics-map-container').html('<div style="text-align: center; padding: 50px; color: #666;">Loading map data for ' + period + ' day(s)...</div>');
      }

      // Load initial analytics data
      loadAnalyticsData(1);
    });
    </script>
    <?php
  }

  /**
   * Domains admin page
   *
   * @since 1.0.0
   */
  public function admin_page_domains() {
    $domains = $this->core->get_domains();
    $wp_domains = $this->get_wordpress_domains();
    ?>
    <div class="wrap">
      <h1><?php echo esc_html__('Email Domains', BAUM_MAIL_TEXT_DOMAIN); ?></h1>

      <div class="baum-mail-page-header">
        <button class="button button-primary" id="add-domain-btn">
          <?php echo esc_html__('Add New Domain', BAUM_MAIL_TEXT_DOMAIN); ?>
        </button>
      </div>

      <table class="wp-list-table widefat fixed striped">
        <thead>
          <tr>
            <th><?php echo esc_html__('Domain', BAUM_MAIL_TEXT_DOMAIN); ?></th>
            <th><?php echo esc_html__('Description', BAUM_MAIL_TEXT_DOMAIN); ?></th>
            <th><?php echo esc_html__('Accounts', BAUM_MAIL_TEXT_DOMAIN); ?></th>
            <th><?php echo esc_html__('Status', BAUM_MAIL_TEXT_DOMAIN); ?></th>
            <th><?php echo esc_html__('Created', BAUM_MAIL_TEXT_DOMAIN); ?></th>
            <th><?php echo esc_html__('Actions', BAUM_MAIL_TEXT_DOMAIN); ?></th>
          </tr>
        </thead>
        <tbody>
          <?php foreach ($domains as $domain): ?>
          <tr>
            <td>
              <strong>
                <a href="<?php echo admin_url('admin.php?page=baum-mail-domain&id=' . $domain->id); ?>">
                  <?php echo esc_html($domain->domain); ?>
                </a>
              </strong>
            </td>
            <td><?php echo esc_html($domain->description); ?></td>
            <td><?php echo esc_html($domain->account_count ?? 0); ?></td>
            <td>
              <span class="status-badge <?php echo $domain->active ? 'active' : 'inactive'; ?>">
                <?php echo $domain->active ? esc_html__('Active', BAUM_MAIL_TEXT_DOMAIN) : esc_html__('Inactive', BAUM_MAIL_TEXT_DOMAIN); ?>
              </span>
            </td>
            <td><?php echo esc_html(date('Y-m-d H:i', strtotime($domain->created_at))); ?></td>
            <td>
              <div class="action-icons">
                <a href="<?php echo admin_url('admin.php?page=baum-mail-domain&id=' . $domain->id); ?>"
                   class="action-icon view-icon"
                   title="<?php echo esc_attr__('View Details', BAUM_MAIL_TEXT_DOMAIN); ?>">
                  👁
                </a>
                <?php if ($domain->active): ?>
                  <button class="action-icon deactivate-icon toggle-status"
                          data-id="<?php echo esc_attr($domain->id); ?>"
                          data-type="domain"
                          title="<?php echo esc_attr__('Deactivate Domain', BAUM_MAIL_TEXT_DOMAIN); ?>">
                    ⏸
                  </button>
                <?php else: ?>
                  <button class="action-icon activate-icon toggle-status"
                          data-id="<?php echo esc_attr($domain->id); ?>"
                          data-type="domain"
                          title="<?php echo esc_attr__('Activate Domain', BAUM_MAIL_TEXT_DOMAIN); ?>">
                    ▶
                  </button>
                <?php endif; ?>
                <button class="action-icon delete-icon delete-item"
                        data-id="<?php echo esc_attr($domain->id); ?>"
                        data-type="domain"
                        title="<?php echo esc_attr__('Delete Domain', BAUM_MAIL_TEXT_DOMAIN); ?>"
                        style="<?php echo $domain->active ? 'display: none;' : ''; ?>">
                  🗑
                </button>
              </div>
            </td>
          </tr>
          <?php endforeach; ?>
        </tbody>
      </table>

      <!-- Add Domain Modal -->
      <div id="add-domain-modal" class="baum-mail-modal" style="display: none;">
        <div class="modal-content">
          <span class="close">&times;</span>
          <h2><?php echo esc_html__('Add New Domain', BAUM_MAIL_TEXT_DOMAIN); ?></h2>
          <form id="add-domain-form">
            <table class="form-table">
              <tr>
                <th><label for="domain-select"><?php echo esc_html__('Domain', BAUM_MAIL_TEXT_DOMAIN); ?></label></th>
                <td>
                  <select id="domain-select" name="domain_select" class="regular-text">
                    <option value=""><?php echo esc_html__('Select a domain...', BAUM_MAIL_TEXT_DOMAIN); ?></option>
                    <optgroup label="<?php echo esc_attr__('WordPress Domains', BAUM_MAIL_TEXT_DOMAIN); ?>">
                      <?php foreach ($wp_domains as $wp_domain): ?>
                        <option value="<?php echo esc_attr($wp_domain); ?>"><?php echo esc_html($wp_domain); ?></option>
                      <?php endforeach; ?>
                    </optgroup>
                    <option value="custom"><?php echo esc_html__('Custom Domain...', BAUM_MAIL_TEXT_DOMAIN); ?></option>
                  </select>
                  <input type="text" id="custom-domain" name="custom_domain" class="regular-text" placeholder="<?php echo esc_attr__('Enter custom domain', BAUM_MAIL_TEXT_DOMAIN); ?>" style="display: none; margin-top: 5px;" />
                </td>
              </tr>
              <tr>
                <th><label for="description"><?php echo esc_html__('Description', BAUM_MAIL_TEXT_DOMAIN); ?></label></th>
                <td><textarea id="description" name="description" class="large-text" rows="3"></textarea></td>
              </tr>
            </table>
            <p class="submit">
              <button type="submit" class="button button-primary"><?php echo esc_html__('Add Domain', BAUM_MAIL_TEXT_DOMAIN); ?></button>
              <button type="button" class="button cancel-btn"><?php echo esc_html__('Cancel', BAUM_MAIL_TEXT_DOMAIN); ?></button>
            </p>
          </form>
        </div>
      </div>
    </div>

    <script>
    jQuery(document).ready(function($) {
      // Modal functionality
      $('#add-domain-btn').click(function() {
        $('#add-domain-modal').show();
      });

      $('.close, .cancel-btn').click(function() {
        $('.baum-mail-modal').hide();
      });

      // Close modal when clicking outside
      $(window).click(function(event) {
        if ($(event.target).hasClass('baum-mail-modal')) {
          $('.baum-mail-modal').hide();
        }
      });

      // Add domain form submission
      $('#add-domain-form').submit(function(e) {
        e.preventDefault();

        var formData = {
          action: 'baum_mail_add_domain',
          domain: $('#domain-name').val(),
          description: $('#domain-description').val(),
          nonce: $('[name="nonce"]').val()
        };

        $.ajax({
          url: ajaxurl,
          type: 'POST',
          data: formData,
          success: function(response) {
            if (response.success) {
              alert('<?php echo esc_js(__('Domain added successfully!', BAUM_MAIL_TEXT_DOMAIN)); ?>');
              location.reload();
            } else {
              alert('<?php echo esc_js(__('Error:', BAUM_MAIL_TEXT_DOMAIN)); ?> ' + response.data);
            }
          },
          error: function() {
            alert('<?php echo esc_js(__('Failed to add domain.', BAUM_MAIL_TEXT_DOMAIN)); ?>');
          }
        });
      });

      // Toggle status (activate/deactivate)
      $('.toggle-status').click(function() {
        var id = $(this).data('id');
        var type = $(this).data('type');
        var $button = $(this);
        var $row = $button.closest('tr');
        var isActive = $button.hasClass('deactivate-icon');

        var action = isActive ? 'deactivate' : 'activate';
        var confirmMessage = isActive ?
          '<?php echo esc_js(__('Are you sure you want to deactivate this item?', BAUM_MAIL_TEXT_DOMAIN)); ?>' :
          '<?php echo esc_js(__('Are you sure you want to activate this item?', BAUM_MAIL_TEXT_DOMAIN)); ?>';

        if (!confirm(confirmMessage)) {
          return;
        }

        $.ajax({
          url: ajaxurl,
          type: 'POST',
          data: {
            action: 'baum_mail_toggle_status',
            id: id,
            type: type,
            status: action,
            nonce: '<?php echo wp_create_nonce('baum_mail_admin_nonce'); ?>'
          },
          success: function(response) {
            if (response.success) {
              // Update the button and show/hide delete button
              if (isActive) {
                // Changed to inactive
                $button.removeClass('deactivate-icon').addClass('activate-icon')
                       .attr('title', '<?php echo esc_js(__('Activate Domain', BAUM_MAIL_TEXT_DOMAIN)); ?>')
                       .html('▶');
                $row.find('.delete-icon').show();
                $row.find('.status-badge').removeClass('active').addClass('inactive').text('<?php echo esc_js(__('Inactive', BAUM_MAIL_TEXT_DOMAIN)); ?>');
              } else {
                // Changed to active
                $button.removeClass('activate-icon').addClass('deactivate-icon')
                       .attr('title', '<?php echo esc_js(__('Deactivate Domain', BAUM_MAIL_TEXT_DOMAIN)); ?>')
                       .html('⏸');
                $row.find('.delete-icon').hide();
                $row.find('.status-badge').removeClass('inactive').addClass('active').text('<?php echo esc_js(__('Active', BAUM_MAIL_TEXT_DOMAIN)); ?>');
              }

              alert(response.data);
            } else {
              alert('<?php echo esc_js(__('Error:', BAUM_MAIL_TEXT_DOMAIN)); ?> ' + response.data);
            }
          },
          error: function() {
            alert('<?php echo esc_js(__('Failed to update status.', BAUM_MAIL_TEXT_DOMAIN)); ?>');
          }
        });
      });

      // Delete item
      $('.delete-item').click(function() {
        var id = $(this).data('id');
        var type = $(this).data('type');

        if (!confirm('<?php echo esc_js(__('Are you sure you want to permanently delete this item? This action cannot be undone.', BAUM_MAIL_TEXT_DOMAIN)); ?>')) {
          return;
        }

        $.ajax({
          url: ajaxurl,
          type: 'POST',
          data: {
            action: 'baum_mail_delete_item',
            id: id,
            type: type,
            nonce: '<?php echo wp_create_nonce('baum_mail_admin_nonce'); ?>'
          },
          success: function(response) {
            if (response.success) {
              alert('<?php echo esc_js(__('Item deleted successfully!', BAUM_MAIL_TEXT_DOMAIN)); ?>');
              location.reload();
            } else {
              alert('<?php echo esc_js(__('Error:', BAUM_MAIL_TEXT_DOMAIN)); ?> ' + response.data);
            }
          },
          error: function() {
            alert('<?php echo esc_js(__('Failed to delete item.', BAUM_MAIL_TEXT_DOMAIN)); ?>');
          }
        });
      });
    });
    </script>

    <style>
    .baum-mail-modal {
      display: none;
      position: fixed;
      z-index: 1000;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
    }

    .modal-content {
      background-color: var(--color-white);
      margin: 5% auto;
      padding: 20px;
      border: 1px solid var(--color-nonary);
      border-radius: 8px;
      width: 80%;
      max-width: 600px;
      position: relative;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    }

    .close {
      color: var(--color-senary);
      float: right;
      font-size: 28px;
      font-weight: bold;
      cursor: pointer;
      position: absolute;
      right: 15px;
      top: 10px;
    }

    .close:hover,
    .close:focus {
      color: var(--color-secondary);
    }

    .modal-content h2 {
      margin-top: 0;
      color: var(--color-secondary);
      padding-right: 40px;
    }

    .modal-content .form-table {
      margin-top: 20px;
    }

    .modal-content .form-table th {
      color: var(--color-secondary);
      font-weight: 600;
    }

    .modal-content .button {
      margin-right: 10px;
    }

    .action-icons {
      display: flex;
      gap: 8px;
      align-items: center;
    }

    .action-icon {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 28px;
      height: 28px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      text-decoration: none;
      transition: all 0.2s ease;
    }

    .view-icon {
      background: var(--color-blue);
      color: var(--color-white);
    }

    .view-icon:hover {
      background: var(--color-indigo);
      color: var(--color-white);
      text-decoration: none;
    }

    .deactivate-icon {
      background: var(--color-orange);
      color: var(--color-white);
    }

    .deactivate-icon:hover {
      background: var(--color-red);
    }

    .activate-icon {
      background: var(--color-green);
      color: var(--color-white);
    }

    .activate-icon:hover {
      background: var(--color-teal);
    }

    .delete-icon {
      background: var(--color-red);
      color: var(--color-white);
    }

    .delete-icon:hover {
      background: var(--color-red);
      opacity: 0.8;
    }

    .action-icon:focus {
      outline: 2px solid var(--color-accent);
      outline-offset: 2px;
    }
    </style>
    <?php
  }

  /**
   * Email accounts admin page
   *
   * @since 1.0.0
   */
  public function admin_page_accounts() {
    $accounts = $this->core->get_accounts();
    $domains = $this->core->get_domains(array('active' => 1));
    ?>
    <div class="wrap">
      <h1><?php echo esc_html__('Email Accounts', BAUM_MAIL_TEXT_DOMAIN); ?></h1>
      
      <div class="baum-mail-page-header">
        <button class="button button-primary" id="add-account-btn">
          <?php echo esc_html__('Add New Account', BAUM_MAIL_TEXT_DOMAIN); ?>
        </button>
      </div>

      <table class="wp-list-table widefat fixed striped">
        <thead>
          <tr>
            <th><?php echo esc_html__('Email Address', BAUM_MAIL_TEXT_DOMAIN); ?></th>
            <th><?php echo esc_html__('Domain', BAUM_MAIL_TEXT_DOMAIN); ?></th>
            <th><?php echo esc_html__('Quota Used', BAUM_MAIL_TEXT_DOMAIN); ?></th>
            <th><?php echo esc_html__('Daily Limits', BAUM_MAIL_TEXT_DOMAIN); ?></th>
            <th><?php echo esc_html__('Status', BAUM_MAIL_TEXT_DOMAIN); ?></th>
            <th><?php echo esc_html__('Last Login', BAUM_MAIL_TEXT_DOMAIN); ?></th>
            <th><?php echo esc_html__('Actions', BAUM_MAIL_TEXT_DOMAIN); ?></th>
          </tr>
        </thead>
        <tbody>
          <?php foreach ($accounts as $account): ?>
          <tr>
            <td>
              <strong>
                <a href="<?php echo admin_url('admin.php?page=baum-mail-account&id=' . $account->id); ?>">
                  <?php echo esc_html($account->email); ?>
                </a>
              </strong>
            </td>
            <td><?php echo esc_html($account->domain); ?></td>
            <td>
              <?php
              $quota_used = $account->quota_used ?? 0;
              $quota_total = $account->quota ?? 0;
              if ($quota_total > 0) {
                $percentage = round(($quota_used / $quota_total) * 100, 1);
                echo esc_html(size_format($quota_used) . ' / ' . size_format($quota_total) . ' (' . $percentage . '%)');
              } else {
                echo esc_html(size_format($quota_used) . ' / Unlimited');
              }
              ?>
            </td>
            <td>
              <?php
              echo esc_html(sprintf(
                __('Send: %d/%d, Receive: %d/%d', BAUM_MAIL_TEXT_DOMAIN),
                $account->daily_send_count ?? 0,
                $account->daily_send_limit ?? 1000,
                $account->daily_receive_count ?? 0,
                $account->daily_receive_limit ?? 5000
              ));
              ?>
            </td>
            <td>
              <span class="status-badge <?php echo $account->active ? 'active' : 'inactive'; ?>">
                <?php echo $account->active ? esc_html__('Active', BAUM_MAIL_TEXT_DOMAIN) : esc_html__('Inactive', BAUM_MAIL_TEXT_DOMAIN); ?>
              </span>
            </td>
            <td><?php echo esc_html($account->last_login ? date('Y-m-d H:i', strtotime($account->last_login)) : __('Never', BAUM_MAIL_TEXT_DOMAIN)); ?></td>
            <td>
              <button class="button button-small toggle-status" data-id="<?php echo esc_attr($account->id); ?>" data-type="account">
                <?php echo $account->active ? esc_html__('Deactivate', BAUM_MAIL_TEXT_DOMAIN) : esc_html__('Activate', BAUM_MAIL_TEXT_DOMAIN); ?>
              </button>
              <button class="button button-small button-link-delete delete-item" data-id="<?php echo esc_attr($account->id); ?>" data-type="account">
                <?php echo esc_html__('Delete', BAUM_MAIL_TEXT_DOMAIN); ?>
              </button>
            </td>
          </tr>
          <?php endforeach; ?>
        </tbody>
      </table>

      <!-- Add Account Modal -->
      <div id="add-account-modal" class="baum-mail-modal" style="display: none;">
        <div class="modal-content">
          <span class="close">&times;</span>
          <h2><?php echo esc_html__('Add New Email Account', BAUM_MAIL_TEXT_DOMAIN); ?></h2>
          <form id="add-account-form">
            <table class="form-table">
              <tr>
                <th><label for="email"><?php echo esc_html__('Email Address', BAUM_MAIL_TEXT_DOMAIN); ?></label></th>
                <td><input type="email" id="email" name="email" class="regular-text" required></td>
              </tr>
              <tr>
                <th><label for="password"><?php echo esc_html__('Password', BAUM_MAIL_TEXT_DOMAIN); ?></label></th>
                <td><input type="password" id="password" name="password" class="regular-text" required></td>
              </tr>
              <tr>
                <th><label for="quota"><?php echo esc_html__('Quota (MB)', BAUM_MAIL_TEXT_DOMAIN); ?></label></th>
                <td><input type="number" id="quota" name="quota" class="small-text" min="0" placeholder="0 = Unlimited"></td>
              </tr>
            </table>
            <p class="submit">
              <button type="submit" class="button button-primary"><?php echo esc_html__('Add Account', BAUM_MAIL_TEXT_DOMAIN); ?></button>
              <button type="button" class="button cancel-btn"><?php echo esc_html__('Cancel', BAUM_MAIL_TEXT_DOMAIN); ?></button>
            </p>
          </form>
        </div>
      </div>
    </div>

    <script>
    jQuery(document).ready(function($) {
      // Modal functionality for accounts
      $('#add-account-btn').click(function() {
        $('#add-account-modal').show();
      });

      $('.close, .cancel-btn').click(function() {
        $('.baum-mail-modal').hide();
      });

      // Close modal when clicking outside
      $(window).click(function(event) {
        if ($(event.target).hasClass('baum-mail-modal')) {
          $('.baum-mail-modal').hide();
        }
      });

      // Add account form submission
      $('#add-account-form').submit(function(e) {
        e.preventDefault();

        var formData = {
          action: 'baum_mail_add_account',
          email: $('#email').val(),
          password: $('#password').val(),
          quota: $('#quota').val(),
          nonce: $('[name="nonce"]').val()
        };

        $.ajax({
          url: ajaxurl,
          type: 'POST',
          data: formData,
          success: function(response) {
            if (response.success) {
              alert('<?php echo esc_js(__('Account added successfully!', BAUM_MAIL_TEXT_DOMAIN)); ?>');
              location.reload();
            } else {
              alert('<?php echo esc_js(__('Error:', BAUM_MAIL_TEXT_DOMAIN)); ?> ' + response.data);
            }
          },
          error: function() {
            alert('<?php echo esc_js(__('Failed to add account.', BAUM_MAIL_TEXT_DOMAIN)); ?>');
          }
        });
      });
    });
    </script>
    <?php
  }

  /**
   * Display comprehensive system status
   *
   * @since 1.0.0
   */
  private function display_system_status() {
    $monitor = baum_mail()->get_component('monitor');
    $smtp = baum_mail()->get_component('smtp');
    $imap = baum_mail()->get_component('imap');
    $encryption = baum_mail()->get_component('encryption');
    $security = baum_mail()->get_component('security');

    ?>
    <div class="system-status-grid">

      <!-- SMTP Status -->
      <?php $smtp_status = $smtp->get_server_status(); ?>
      <div class="status-item <?php echo $smtp_status['postfix_running'] ? 'online' : 'offline'; ?>">
        <h4><?php echo esc_html__('SMTP Server', BAUM_MAIL_TEXT_DOMAIN); ?></h4>
        <span class="status-indicator"></span>
        <p><?php echo $smtp_status['postfix_running'] ? esc_html__('Running', BAUM_MAIL_TEXT_DOMAIN) : esc_html__('Not running', BAUM_MAIL_TEXT_DOMAIN); ?></p>
        <a href="<?php echo admin_url('admin.php?page=baum-mail-monitoring#smtp'); ?>" class="status-link"><?php echo esc_html__('View Details', BAUM_MAIL_TEXT_DOMAIN); ?></a>
      </div>

      <!-- IMAP Status -->
      <?php $imap_status = $imap->get_server_status(); ?>
      <div class="status-item <?php echo $imap_status['server_running'] ? 'online' : 'offline'; ?>">
        <h4><?php echo esc_html__('IMAP Server', BAUM_MAIL_TEXT_DOMAIN); ?></h4>
        <span class="status-indicator"></span>
        <p><?php echo $imap_status['server_running'] ? esc_html__('Running', BAUM_MAIL_TEXT_DOMAIN) : esc_html__('Not running', BAUM_MAIL_TEXT_DOMAIN); ?></p>
        <a href="<?php echo admin_url('admin.php?page=baum-mail-monitoring#imap'); ?>" class="status-link"><?php echo esc_html__('View Details', BAUM_MAIL_TEXT_DOMAIN); ?></a>
      </div>

      <!-- Encryption Status -->
      <?php $encryption_status = $encryption->get_encryption_status(); ?>
      <div class="status-item <?php echo $encryption_status['available'] ? 'online' : 'offline'; ?>">
        <h4><?php echo esc_html__('Encryption', BAUM_MAIL_TEXT_DOMAIN); ?></h4>
        <span class="status-indicator"></span>
        <p><?php echo $encryption_status['available'] ? esc_html__('Available', BAUM_MAIL_TEXT_DOMAIN) : esc_html__('Not available', BAUM_MAIL_TEXT_DOMAIN); ?></p>
        <a href="<?php echo admin_url('admin.php?page=baum-mail-security#encryption'); ?>" class="status-link"><?php echo esc_html__('View Details', BAUM_MAIL_TEXT_DOMAIN); ?></a>
      </div>

      <!-- Security Status -->
      <?php $security_status = $security->get_security_status(); ?>
      <?php
      $security_ok = true;
      foreach ($security_status as $check => $status) {
        if (is_array($status)) {
          switch ($check) {
            case 'clamav':
            case 'spamassassin':
              if (!($status['enabled'] && $status['running'])) $security_ok = false;
              break;
            case 'ssl':
              if (!($status['cert_exists'] && $status['key_exists'] && $status['cert_valid'])) $security_ok = false;
              break;
          }
        }
      }
      ?>
      <div class="status-item <?php echo $security_ok ? 'online' : 'warning'; ?>">
        <h4><?php echo esc_html__('Security', BAUM_MAIL_TEXT_DOMAIN); ?></h4>
        <span class="status-indicator"></span>
        <p><?php echo $security_ok ? esc_html__('All checks passed', BAUM_MAIL_TEXT_DOMAIN) : esc_html__('Issues detected', BAUM_MAIL_TEXT_DOMAIN); ?></p>
        <a href="<?php echo admin_url('admin.php?page=baum-mail-security'); ?>" class="status-link"><?php echo esc_html__('View Report', BAUM_MAIL_TEXT_DOMAIN); ?></a>
      </div>

      <!-- Queue Status -->
      <?php $queue_status = $smtp->get_queue_status(); ?>
      <div class="status-item <?php echo $queue_status['total_queue'] == 0 ? 'online' : 'warning'; ?>">
        <h4><?php echo esc_html__('Mail Queue', BAUM_MAIL_TEXT_DOMAIN); ?></h4>
        <span class="status-indicator"></span>
        <p><?php echo sprintf(esc_html__('%d messages in queue', BAUM_MAIL_TEXT_DOMAIN), $queue_status['total_queue']); ?></p>
        <a href="<?php echo admin_url('admin.php?page=baum-mail-monitoring#queue'); ?>" class="status-link"><?php echo esc_html__('View Queue', BAUM_MAIL_TEXT_DOMAIN); ?></a>
      </div>

      <!-- Logs -->
      <div class="status-item online">
        <h4><?php echo esc_html__('System Logs', BAUM_MAIL_TEXT_DOMAIN); ?></h4>
        <span class="status-indicator"></span>
        <p><?php echo esc_html__('View recent activity', BAUM_MAIL_TEXT_DOMAIN); ?></p>
        <a href="<?php echo admin_url('admin.php?page=baum-mail-logs'); ?>" class="status-link"><?php echo esc_html__('View Logs', BAUM_MAIL_TEXT_DOMAIN); ?></a>
      </div>

    </div>

    <!-- Service Management Section -->
    <div class="baum-mail-service-management">
      <h2><?php echo esc_html__('Service Management', BAUM_MAIL_TEXT_DOMAIN); ?></h2>
      <div class="service-actions">
        <button class="button" data-service="postfix" id="restart-postfix-btn">
          <span class="dashicons dashicons-update"></span>
          <?php echo esc_html__('Restart Postfix', BAUM_MAIL_TEXT_DOMAIN); ?>
        </button>
        <button class="button" data-service="dovecot" id="restart-dovecot-btn">
          <span class="dashicons dashicons-update"></span>
          <?php echo esc_html__('Restart Dovecot', BAUM_MAIL_TEXT_DOMAIN); ?>
        </button>
        <button class="button" data-service="clamav" id="restart-clamav-btn">
          <span class="dashicons dashicons-update"></span>
          <?php echo esc_html__('Restart ClamAV', BAUM_MAIL_TEXT_DOMAIN); ?>
        </button>
        <button class="button" data-service="spamassassin" id="restart-spamassassin-btn">
          <span class="dashicons dashicons-update"></span>
          <?php echo esc_html__('Restart SpamAssassin', BAUM_MAIL_TEXT_DOMAIN); ?>
        </button>
        <button class="button button-primary" id="flush-queue-btn">
          <span class="dashicons dashicons-email"></span>
          <?php echo esc_html__('Flush Mail Queue', BAUM_MAIL_TEXT_DOMAIN); ?>
        </button>
      </div>
    </div>

    <!-- System Monitoring Section -->
    <div class="baum-mail-system-monitoring">
      <h2><?php echo esc_html__('System Monitoring', BAUM_MAIL_TEXT_DOMAIN); ?></h2>

      <?php if ($monitor): ?>
        <div class="monitoring-grid">
          <?php $detailed_status = $monitor->get_system_status(); ?>
          <?php foreach ($detailed_status as $service => $data): ?>
            <?php if (is_array($data) && isset($data['status'], $data['message'])): ?>
              <div class="monitoring-item <?php echo $data['status'] ? 'online' : 'offline'; ?>">
                <h3><?php echo esc_html(ucfirst(str_replace('_', ' ', $service))); ?></h3>
                <div class="status-indicator <?php echo $data['status'] ? 'green' : 'red'; ?>"></div>
                <p class="status-message"><?php echo esc_html($data['message']); ?></p>
                <?php if (isset($data['details']) && is_array($data['details'])): ?>
                  <div class="status-details">
                    <?php foreach ($data['details'] as $key => $value): ?>
                      <span class="detail-item">
                        <strong><?php echo esc_html(ucfirst(str_replace('_', ' ', $key))); ?>:</strong>
                        <?php echo esc_html(is_bool($value) ? ($value ? 'Yes' : 'No') : $value); ?>
                      </span>
                    <?php endforeach; ?>
                  </div>
                <?php endif; ?>
              </div>
            <?php endif; ?>
          <?php endforeach; ?>
        </div>
      <?php endif; ?>
    </div>
    <?php
  }

  /**
   * AJAX: Create domain
   *
   * @since 1.0.0
   */
  public function ajax_create_domain() {
    check_ajax_referer('baum_mail_admin_nonce', 'nonce');

    if (!current_user_can('manage_options')) {
      wp_die(__('Insufficient permissions.', BAUM_MAIL_TEXT_DOMAIN));
    }

    $domain = sanitize_text_field($_POST['domain']);
    $description = sanitize_textarea_field($_POST['description']);

    $result = $this->core->create_domain($domain, $description);

    if (is_wp_error($result)) {
      wp_send_json_error($result->get_error_message());
    } else {
      wp_send_json_success(__('Domain created successfully.', BAUM_MAIL_TEXT_DOMAIN));
    }
  }

  /**
   * AJAX: Create account
   *
   * @since 1.0.0
   */
  public function ajax_create_account() {
    check_ajax_referer('baum_mail_admin_nonce', 'nonce');

    if (!current_user_can('manage_options')) {
      wp_die(__('Insufficient permissions.', BAUM_MAIL_TEXT_DOMAIN));
    }

    $email = sanitize_email($_POST['email']);
    $password = $_POST['password']; // Don't sanitize password
    $quota = intval($_POST['quota']) * 1024 * 1024; // Convert MB to bytes

    $result = $this->core->create_account($email, $password, $quota);

    if (is_wp_error($result)) {
      wp_send_json_error($result->get_error_message());
    } else {
      wp_send_json_success(__('Email account created successfully.', BAUM_MAIL_TEXT_DOMAIN));
    }
  }

  /**
   * Email aliases admin page
   *
   * @since 1.0.0
   */
  public function admin_page_aliases() {
    $aliases = $this->core->get_aliases();
    $domains = $this->core->get_domains(array('active' => 1));
    ?>
    <div class="wrap">
      <h1><?php echo esc_html__('Email Aliases', BAUM_MAIL_TEXT_DOMAIN); ?></h1>

      <div class="baum-mail-page-header">
        <button class="button button-primary" id="add-alias-btn">
          <?php echo esc_html__('Add New Alias', BAUM_MAIL_TEXT_DOMAIN); ?>
        </button>
      </div>

      <table class="wp-list-table widefat fixed striped">
        <thead>
          <tr>
            <th><?php echo esc_html__('Source', BAUM_MAIL_TEXT_DOMAIN); ?></th>
            <th><?php echo esc_html__('Destination', BAUM_MAIL_TEXT_DOMAIN); ?></th>
            <th><?php echo esc_html__('Domain', BAUM_MAIL_TEXT_DOMAIN); ?></th>
            <th><?php echo esc_html__('Status', BAUM_MAIL_TEXT_DOMAIN); ?></th>
            <th><?php echo esc_html__('Actions', BAUM_MAIL_TEXT_DOMAIN); ?></th>
          </tr>
        </thead>
        <tbody>
          <?php if (!empty($aliases)): ?>
            <?php foreach ($aliases as $alias): ?>
              <tr>
                <td><?php echo esc_html($alias->source); ?></td>
                <td><?php echo esc_html($alias->destination); ?></td>
                <td><?php echo esc_html($alias->domain); ?></td>
                <td>
                  <span class="status-badge <?php echo $alias->active ? 'active' : 'inactive'; ?>">
                    <?php echo $alias->active ? esc_html__('Active', BAUM_MAIL_TEXT_DOMAIN) : esc_html__('Inactive', BAUM_MAIL_TEXT_DOMAIN); ?>
                  </span>
                </td>
                <td>
                  <button class="button button-small toggle-status-btn" data-id="<?php echo esc_attr($alias->id); ?>" data-type="alias">
                    <?php echo $alias->active ? esc_html__('Deactivate', BAUM_MAIL_TEXT_DOMAIN) : esc_html__('Activate', BAUM_MAIL_TEXT_DOMAIN); ?>
                  </button>
                  <button class="button button-small button-link-delete delete-btn" data-id="<?php echo esc_attr($alias->id); ?>" data-type="alias">
                    <?php echo esc_html__('Delete', BAUM_MAIL_TEXT_DOMAIN); ?>
                  </button>
                </td>
              </tr>
            <?php endforeach; ?>
          <?php else: ?>
            <tr>
              <td colspan="5"><?php echo esc_html__('No aliases found.', BAUM_MAIL_TEXT_DOMAIN); ?></td>
            </tr>
          <?php endif; ?>
        </tbody>
      </table>

      <!-- Add Alias Modal -->
      <div id="add-alias-modal" class="baum-mail-modal" style="display: none;">
        <div class="modal-content">
          <span class="close">&times;</span>
          <h2><?php echo esc_html__('Add New Alias', BAUM_MAIL_TEXT_DOMAIN); ?></h2>
          <form id="add-alias-form">
            <?php wp_nonce_field('baum_mail_admin_nonce', 'nonce'); ?>
            <p>
              <label for="source"><?php echo esc_html__('Source Email:', BAUM_MAIL_TEXT_DOMAIN); ?></label>
              <input type="email" id="source" name="source" required>
            </p>
            <p>
              <label for="destination"><?php echo esc_html__('Destination Email(s):', BAUM_MAIL_TEXT_DOMAIN); ?></label>
              <textarea id="destination" name="destination" rows="3" placeholder="<?php echo esc_attr__('Enter one or more email addresses, separated by commas', BAUM_MAIL_TEXT_DOMAIN); ?>" required></textarea>
            </p>
            <p>
              <label for="alias_domain_id"><?php echo esc_html__('Domain:', BAUM_MAIL_TEXT_DOMAIN); ?></label>
              <select id="alias_domain_id" name="domain_id" required>
                <option value=""><?php echo esc_html__('Select Domain', BAUM_MAIL_TEXT_DOMAIN); ?></option>
                <?php foreach ($domains as $domain): ?>
                  <option value="<?php echo esc_attr($domain->id); ?>"><?php echo esc_html($domain->domain); ?></option>
                <?php endforeach; ?>
              </select>
            </p>
            <p>
              <button type="submit" class="button button-primary"><?php echo esc_html__('Add Alias', BAUM_MAIL_TEXT_DOMAIN); ?></button>
              <button type="button" class="button cancel-btn"><?php echo esc_html__('Cancel', BAUM_MAIL_TEXT_DOMAIN); ?></button>
            </p>
          </form>
        </div>
      </div>
    </div>
    <?php
  }

  /**
   * Security admin page
   *
   * @since 1.0.0
   */
  public function admin_page_security() {
    $security = baum_mail()->get_component('security');
    ?>
    <div class="wrap">
      <h1><?php echo esc_html__('Security Reports', BAUM_MAIL_TEXT_DOMAIN); ?></h1>

      <div class="nav-tab-wrapper">
        <a href="#security-status" class="nav-tab nav-tab-active"><?php echo esc_html__('Security Status', BAUM_MAIL_TEXT_DOMAIN); ?></a>
        <a href="#blacklist-report" class="nav-tab"><?php echo esc_html__('Blacklist Report', BAUM_MAIL_TEXT_DOMAIN); ?></a>
        <a href="#spam-report" class="nav-tab"><?php echo esc_html__('Spam Report', BAUM_MAIL_TEXT_DOMAIN); ?></a>
        <a href="#virus-report" class="nav-tab"><?php echo esc_html__('Virus Report', BAUM_MAIL_TEXT_DOMAIN); ?></a>
      </div>

      <!-- Security Status Tab -->
      <div id="security-status" class="tab-content">
        <?php if ($security): ?>
          <div class="baum-mail-security-status">
            <h2><?php echo esc_html__('Detailed Security Status', BAUM_MAIL_TEXT_DOMAIN); ?></h2>
            <?php $security_status = $security->get_security_status(); ?>
            <div class="security-status-grid">
              <?php foreach ($security_status as $check => $status): ?>
                <?php
                $is_secure = false;
                $message = '';
                $details = array();

                // Handle different status structures
                if (is_array($status)) {
                  switch ($check) {
                    case 'clamav':
                      $is_secure = $status['enabled'] && $status['running'];
                      $message = $status['enabled'] ?
                        ($status['running'] ? __('Running', BAUM_MAIL_TEXT_DOMAIN) : __('Not running', BAUM_MAIL_TEXT_DOMAIN)) :
                        __('Disabled', BAUM_MAIL_TEXT_DOMAIN);
                      $details = array(
                        'Version' => $status['version'] ?? 'Unknown',
                        'Database Updated' => $status['db_updated'] ?? 'Unknown',
                        'Signatures' => $status['signatures'] ?? 'Unknown'
                      );
                      break;
                    case 'spamassassin':
                      $is_secure = $status['enabled'] && $status['running'];
                      $message = $status['enabled'] ?
                        ($status['running'] ? __('Running', BAUM_MAIL_TEXT_DOMAIN) : __('Not running', BAUM_MAIL_TEXT_DOMAIN)) :
                        __('Disabled', BAUM_MAIL_TEXT_DOMAIN);
                      $details = array(
                        'Version' => $status['version'] ?? 'Unknown',
                        'Rules Updated' => $status['rules_updated'] ?? 'Unknown',
                        'Bayes Database' => $status['bayes_enabled'] ?? 'Unknown'
                      );
                      break;
                    case 'ssl':
                      $is_secure = $status['cert_exists'] && $status['key_exists'] && $status['cert_valid'];
                      $message = $is_secure ? __('Valid SSL certificate', BAUM_MAIL_TEXT_DOMAIN) : __('SSL issues detected', BAUM_MAIL_TEXT_DOMAIN);
                      $details = array(
                        'Certificate Exists' => $status['cert_exists'] ? 'Yes' : 'No',
                        'Private Key Exists' => $status['key_exists'] ? 'Yes' : 'No',
                        'Certificate Valid' => $status['cert_valid'] ? 'Yes' : 'No',
                        'Expires' => $status['expires'] ?? 'Unknown'
                      );
                      break;
                    default:
                      $is_secure = isset($status['enabled']) ? $status['enabled'] : false;
                      $message = $is_secure ? __('Active', BAUM_MAIL_TEXT_DOMAIN) : __('Inactive', BAUM_MAIL_TEXT_DOMAIN);
                  }
                }
                ?>
                <div class="security-item-detailed <?php echo $is_secure ? 'secure' : 'warning'; ?>">
                  <h4><?php echo esc_html(ucfirst(str_replace('_', ' ', $check))); ?></h4>
                  <span class="security-indicator"></span>
                  <p class="status-message"><?php echo esc_html($message); ?></p>
                  <?php if (!empty($details)): ?>
                    <div class="security-details">
                      <?php foreach ($details as $key => $value): ?>
                        <div class="detail-row">
                          <span class="detail-label"><?php echo esc_html($key); ?>:</span>
                          <span class="detail-value"><?php echo esc_html($value); ?></span>
                        </div>
                      <?php endforeach; ?>
                    </div>
                  <?php endif; ?>
                </div>
              <?php endforeach; ?>
            </div>
          </div>
        <?php endif; ?>
      </div>

      <!-- Blacklist Report Tab -->
      <div id="blacklist-report" class="tab-content" style="display: none;">
        <h2><?php echo esc_html__('Blacklist Status Report', BAUM_MAIL_TEXT_DOMAIN); ?></h2>
        <div class="blacklist-check-form">
          <label for="check-ip"><?php echo esc_html__('Check IP Address:', BAUM_MAIL_TEXT_DOMAIN); ?></label>
          <input type="text" id="check-ip" placeholder="Enter IP address" />
          <button class="button button-primary" id="check-blacklist-btn"><?php echo esc_html__('Check Blacklists', BAUM_MAIL_TEXT_DOMAIN); ?></button>
        </div>
        <div id="blacklist-results"></div>
      </div>

      <!-- Spam Report Tab -->
      <div id="spam-report" class="tab-content" style="display: none;">
        <h2><?php echo esc_html__('SpamAssassin Report', BAUM_MAIL_TEXT_DOMAIN); ?></h2>
        <div class="spam-statistics">
          <?php
          global $wpdb;
          $spam_stats = $wpdb->get_results(
            "SELECT DATE(created_at) as date, COUNT(*) as count
             FROM {$wpdb->prefix}baum_mail_logs
             WHERE action = 'spam_detected'
             AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
             GROUP BY DATE(created_at)
             ORDER BY date DESC
             LIMIT 30"
          );
          ?>
          <table class="wp-list-table widefat fixed striped">
            <thead>
              <tr>
                <th><?php echo esc_html__('Date', BAUM_MAIL_TEXT_DOMAIN); ?></th>
                <th><?php echo esc_html__('Spam Emails Detected', BAUM_MAIL_TEXT_DOMAIN); ?></th>
              </tr>
            </thead>
            <tbody>
              <?php if (!empty($spam_stats)): ?>
                <?php foreach ($spam_stats as $stat): ?>
                  <tr>
                    <td><?php echo esc_html(date('M j, Y', strtotime($stat->date))); ?></td>
                    <td><?php echo esc_html($stat->count); ?></td>
                  </tr>
                <?php endforeach; ?>
              <?php else: ?>
                <tr>
                  <td colspan="2"><?php echo esc_html__('No spam detection data available.', BAUM_MAIL_TEXT_DOMAIN); ?></td>
                </tr>
              <?php endif; ?>
            </tbody>
          </table>
        </div>
      </div>

      <!-- Virus Report Tab -->
      <div id="virus-report" class="tab-content" style="display: none;">
        <h2><?php echo esc_html__('ClamAV Virus Report', BAUM_MAIL_TEXT_DOMAIN); ?></h2>
        <div class="virus-statistics">
          <?php
          $virus_stats = $wpdb->get_results(
            "SELECT DATE(created_at) as date, COUNT(*) as count
             FROM {$wpdb->prefix}baum_mail_logs
             WHERE action = 'virus_detected'
             AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
             GROUP BY DATE(created_at)
             ORDER BY date DESC
             LIMIT 30"
          );
          ?>
          <table class="wp-list-table widefat fixed striped">
            <thead>
              <tr>
                <th><?php echo esc_html__('Date', BAUM_MAIL_TEXT_DOMAIN); ?></th>
                <th><?php echo esc_html__('Viruses Detected', BAUM_MAIL_TEXT_DOMAIN); ?></th>
              </tr>
            </thead>
            <tbody>
              <?php if (!empty($virus_stats)): ?>
                <?php foreach ($virus_stats as $stat): ?>
                  <tr>
                    <td><?php echo esc_html(date('M j, Y', strtotime($stat->date))); ?></td>
                    <td><?php echo esc_html($stat->count); ?></td>
                  </tr>
                <?php endforeach; ?>
              <?php else: ?>
                <tr>
                  <td colspan="2"><?php echo esc_html__('No virus detection data available.', BAUM_MAIL_TEXT_DOMAIN); ?></td>
                </tr>
              <?php endif; ?>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <script>
    jQuery(document).ready(function($) {
      $('.nav-tab').click(function(e) {
        e.preventDefault();
        var target = $(this).attr('href');

        $('.nav-tab').removeClass('nav-tab-active');
        $(this).addClass('nav-tab-active');

        $('.tab-content').hide();
        $(target).show();
      });
    });
    </script>
    <?php
  }

  /**
   * Monitoring admin page
   *
   * @since 1.0.0
   */
  public function admin_page_monitoring() {
    $monitor = baum_mail()->get_component('monitor');
    ?>
    <div class="wrap">
      <h1><?php echo esc_html__('System Monitoring', BAUM_MAIL_TEXT_DOMAIN); ?></h1>

      <?php if ($monitor): ?>
        <div class="baum-mail-monitoring">
          <div class="monitoring-header">
            <button class="button button-primary" id="refresh-monitoring-btn">
              <?php echo esc_html__('Refresh Status', BAUM_MAIL_TEXT_DOMAIN); ?>
            </button>
            <span class="last-updated">
              <?php echo esc_html__('Last updated:', BAUM_MAIL_TEXT_DOMAIN); ?>
              <span id="last-update-time"><?php echo esc_html(current_time('H:i:s')); ?></span>
            </span>
          </div>

          <div class="monitoring-grid">
            <?php $status = $monitor->get_system_status(); ?>
            <?php foreach ($status as $service => $data): ?>
              <?php if (is_array($data) && isset($data['status'], $data['message'])): ?>
                <div class="monitoring-item <?php echo $data['status'] ? 'online' : 'offline'; ?>">
                  <h3><?php echo esc_html(ucfirst(str_replace('_', ' ', $service))); ?></h3>
                  <div class="status-indicator <?php echo $data['status'] ? 'green' : 'red'; ?>"></div>
                  <p class="status-message"><?php echo esc_html($data['message']); ?></p>
                  <?php if (isset($data['details']) && is_array($data['details'])): ?>
                    <div class="status-details">
                      <?php foreach ($data['details'] as $key => $value): ?>
                        <span class="detail-item">
                          <strong><?php echo esc_html(ucfirst(str_replace('_', ' ', $key))); ?>:</strong>
                          <?php echo esc_html(is_bool($value) ? ($value ? 'Yes' : 'No') : $value); ?>
                        </span>
                      <?php endforeach; ?>
                    </div>
                  <?php endif; ?>
                </div>
              <?php endif; ?>
            <?php endforeach; ?>
          </div>

          <div class="monitoring-actions">
            <h2><?php echo esc_html__('Service Actions', BAUM_MAIL_TEXT_DOMAIN); ?></h2>
            <div class="action-buttons">
              <button class="button" data-service="postfix" id="restart-postfix-btn">
                <?php echo esc_html__('Restart Postfix', BAUM_MAIL_TEXT_DOMAIN); ?>
              </button>
              <button class="button" data-service="dovecot" id="restart-dovecot-btn">
                <?php echo esc_html__('Restart Dovecot', BAUM_MAIL_TEXT_DOMAIN); ?>
              </button>
              <button class="button" data-service="clamav" id="restart-clamav-btn">
                <?php echo esc_html__('Restart ClamAV', BAUM_MAIL_TEXT_DOMAIN); ?>
              </button>
              <button class="button" data-service="spamassassin" id="restart-spamassassin-btn">
                <?php echo esc_html__('Restart SpamAssassin', BAUM_MAIL_TEXT_DOMAIN); ?>
              </button>
            </div>
          </div>
        </div>
      <?php else: ?>
        <div class="notice notice-error">
          <p><?php echo esc_html__('Monitoring component is not available.', BAUM_MAIL_TEXT_DOMAIN); ?></p>
        </div>
      <?php endif; ?>
    </div>
    <?php
  }

  /**
   * Settings admin page
   *
   * @since 1.0.0
   */
  public function admin_page_settings() {
    ?>
    <div class="wrap">
      <h1><?php echo esc_html__('Baum Mail Settings', BAUM_MAIL_TEXT_DOMAIN); ?></h1>

      <div class="nav-tab-wrapper">
        <a href="#general" class="nav-tab nav-tab-active"><?php echo esc_html__('General', BAUM_MAIL_TEXT_DOMAIN); ?></a>
        <a href="#security" class="nav-tab"><?php echo esc_html__('Security', BAUM_MAIL_TEXT_DOMAIN); ?></a>
        <a href="#tracking" class="nav-tab"><?php echo esc_html__('Tracking', BAUM_MAIL_TEXT_DOMAIN); ?></a>
        <a href="#limits" class="nav-tab"><?php echo esc_html__('Limits', BAUM_MAIL_TEXT_DOMAIN); ?></a>
      </div>

      <form method="post" action="options.php">
        <?php
        settings_fields('baum_mail_settings');
        do_settings_sections('baum_mail_settings');
        ?>

        <!-- General Settings Tab -->
        <div id="general" class="tab-content">
          <h2><?php echo esc_html__('General Configuration', BAUM_MAIL_TEXT_DOMAIN); ?></h2>
          <table class="form-table">
            <tr>
              <th scope="row"><?php echo esc_html__('Hostname', BAUM_MAIL_TEXT_DOMAIN); ?></th>
              <td>
                <input type="text" id="baum_mail_hostname" name="baum_mail_hostname" value="<?php echo esc_attr(get_option('baum_mail_hostname', 'mail.example.com')); ?>" class="regular-text" />
                <p class="description"><?php echo esc_html__('Mail server hostname (FQDN)', BAUM_MAIL_TEXT_DOMAIN); ?></p>
              </td>
            </tr>
            <tr>
              <th scope="row"><?php echo esc_html__('Postfix Config Path', BAUM_MAIL_TEXT_DOMAIN); ?></th>
              <td>
                <input type="text" id="baum_mail_postfix_config_path" name="baum_mail_postfix_config_path" value="<?php echo esc_attr(get_option('baum_mail_postfix_config_path', '/etc/postfix')); ?>" class="regular-text" />
                <p class="description"><?php echo esc_html__('Path to Postfix configuration directory', BAUM_MAIL_TEXT_DOMAIN); ?></p>
              </td>
            </tr>
            <tr>
              <th scope="row"><?php echo esc_html__('Dovecot Config Path', BAUM_MAIL_TEXT_DOMAIN); ?></th>
              <td>
                <input type="text" id="baum_mail_dovecot_config_path" name="baum_mail_dovecot_config_path" value="<?php echo esc_attr(get_option('baum_mail_dovecot_config_path', '/etc/dovecot')); ?>" class="regular-text" />
                <p class="description"><?php echo esc_html__('Path to Dovecot configuration directory', BAUM_MAIL_TEXT_DOMAIN); ?></p>
              </td>
            </tr>
            <tr>
              <th scope="row"><?php echo esc_html__('Monitor Interval (minutes)', BAUM_MAIL_TEXT_DOMAIN); ?></th>
              <td>
                <input type="number" id="baum_mail_monitor_interval" name="baum_mail_monitor_interval" value="<?php echo esc_attr(get_option('baum_mail_monitor_interval', 5)); ?>" min="1" max="60" class="small-text" />
                <p class="description"><?php echo esc_html__('How often to run system monitoring checks', BAUM_MAIL_TEXT_DOMAIN); ?></p>
              </td>
            </tr>
          </table>
        </div>

        <!-- Security Settings Tab -->
        <div id="security" class="tab-content" style="display: none;">
          <h2><?php echo esc_html__('Security Configuration', BAUM_MAIL_TEXT_DOMAIN); ?></h2>
          <table class="form-table">
            <tr>
              <th scope="row"><?php echo esc_html__('Enable ClamAV', BAUM_MAIL_TEXT_DOMAIN); ?></th>
              <td>
                <input type="checkbox" id="baum_mail_enable_clamav" name="baum_mail_enable_clamav" value="1" <?php checked(1, get_option('baum_mail_enable_clamav', 0)); ?> />
                <label for="baum_mail_enable_clamav"><?php echo esc_html__('Enable virus scanning with ClamAV', BAUM_MAIL_TEXT_DOMAIN); ?></label>
              </td>
            </tr>
            <tr>
              <th scope="row"><?php echo esc_html__('Enable SpamAssassin', BAUM_MAIL_TEXT_DOMAIN); ?></th>
              <td>
                <input type="checkbox" id="baum_mail_enable_spamassassin" name="baum_mail_enable_spamassassin" value="1" <?php checked(1, get_option('baum_mail_enable_spamassassin', 0)); ?> />
                <label for="baum_mail_enable_spamassassin"><?php echo esc_html__('Enable spam filtering with SpamAssassin', BAUM_MAIL_TEXT_DOMAIN); ?></label>
              </td>
            </tr>
            <tr>
              <th scope="row"><?php echo esc_html__('SSL Certificate Path', BAUM_MAIL_TEXT_DOMAIN); ?></th>
              <td>
                <input type="text" id="baum_mail_ssl_cert_path" name="baum_mail_ssl_cert_path" value="<?php echo esc_attr(get_option('baum_mail_ssl_cert_path', '/etc/ssl/certs/mail.crt')); ?>" class="regular-text" />
                <p class="description"><?php echo esc_html__('Path to SSL certificate file', BAUM_MAIL_TEXT_DOMAIN); ?></p>
              </td>
            </tr>
            <tr>
              <th scope="row"><?php echo esc_html__('SSL Private Key Path', BAUM_MAIL_TEXT_DOMAIN); ?></th>
              <td>
                <input type="text" id="baum_mail_ssl_key_path" name="baum_mail_ssl_key_path" value="<?php echo esc_attr(get_option('baum_mail_ssl_key_path', '/etc/ssl/private/mail.key')); ?>" class="regular-text" />
                <p class="description"><?php echo esc_html__('Path to SSL private key file', BAUM_MAIL_TEXT_DOMAIN); ?></p>
              </td>
            </tr>
            <tr>
              <th scope="row"><?php echo esc_html__('Enable Encryption', BAUM_MAIL_TEXT_DOMAIN); ?></th>
              <td>
                <input type="checkbox" id="baum_mail_encryption_enabled" name="baum_mail_encryption_enabled" value="1" <?php checked(1, get_option('baum_mail_encryption_enabled', 0)); ?> />
                <label for="baum_mail_encryption_enabled"><?php echo esc_html__('Enable GPG encryption by default', BAUM_MAIL_TEXT_DOMAIN); ?></label>
              </td>
            </tr>
          </table>
        </div>

        <!-- Tracking Settings Tab -->
        <div id="tracking" class="tab-content" style="display: none;">
          <h2><?php echo esc_html__('Email Tracking Configuration', BAUM_MAIL_TEXT_DOMAIN); ?></h2>
          <table class="form-table">
            <tr>
              <th scope="row"><?php echo esc_html__('Enable Tracking Pixel', BAUM_MAIL_TEXT_DOMAIN); ?></th>
              <td>
                <input type="checkbox" id="baum_mail_tracking_enabled" name="baum_mail_tracking_enabled" value="1" <?php checked(1, get_option('baum_mail_tracking_enabled', 0)); ?> />
                <label for="baum_mail_tracking_enabled"><?php echo esc_html__('Add tracking pixel to outgoing emails', BAUM_MAIL_TEXT_DOMAIN); ?></label>
              </td>
            </tr>
            <tr>
              <th scope="row"><?php echo esc_html__('Tracking Domain', BAUM_MAIL_TEXT_DOMAIN); ?></th>
              <td>
                <input type="text" id="baum_mail_tracking_domain" name="baum_mail_tracking_domain" value="<?php echo esc_attr(get_option('baum_mail_tracking_domain', get_site_url())); ?>" class="regular-text" />
                <p class="description"><?php echo esc_html__('Domain to use for tracking pixel URLs', BAUM_MAIL_TEXT_DOMAIN); ?></p>
              </td>
            </tr>
            <tr>
              <th scope="row"><?php echo esc_html__('Track Opens', BAUM_MAIL_TEXT_DOMAIN); ?></th>
              <td>
                <input type="checkbox" id="baum_mail_track_opens" name="baum_mail_track_opens" value="1" <?php checked(1, get_option('baum_mail_track_opens', 1)); ?> />
                <label for="baum_mail_track_opens"><?php echo esc_html__('Track email opens', BAUM_MAIL_TEXT_DOMAIN); ?></label>
              </td>
            </tr>
            <tr>
              <th scope="row"><?php echo esc_html__('Track Location', BAUM_MAIL_TEXT_DOMAIN); ?></th>
              <td>
                <input type="checkbox" id="baum_mail_track_location" name="baum_mail_track_location" value="1" <?php checked(1, get_option('baum_mail_track_location', 1)); ?> />
                <label for="baum_mail_track_location"><?php echo esc_html__('Track geographic location of opens', BAUM_MAIL_TEXT_DOMAIN); ?></label>
              </td>
            </tr>
          </table>
        </div>

        <!-- Limits Settings Tab -->
        <div id="limits" class="tab-content" style="display: none;">
          <h2><?php echo esc_html__('Default Limits Configuration', BAUM_MAIL_TEXT_DOMAIN); ?></h2>
          <table class="form-table">
            <tr>
              <th scope="row"><?php echo esc_html__('Default Quota (MB)', BAUM_MAIL_TEXT_DOMAIN); ?></th>
              <td>
                <input type="number" id="baum_mail_default_quota" name="baum_mail_default_quota" value="<?php echo esc_attr(get_option('baum_mail_default_quota', 1000)); ?>" min="0" class="small-text" />
                <p class="description"><?php echo esc_html__('Default quota for new email accounts in MB (0 = unlimited)', BAUM_MAIL_TEXT_DOMAIN); ?></p>
              </td>
            </tr>
            <tr>
              <th scope="row"><?php echo esc_html__('Default Daily Send Limit', BAUM_MAIL_TEXT_DOMAIN); ?></th>
              <td>
                <input type="number" id="baum_mail_default_daily_send_limit" name="baum_mail_default_daily_send_limit" value="<?php echo esc_attr(get_option('baum_mail_default_daily_send_limit', 1000)); ?>" min="0" class="small-text" />
                <p class="description"><?php echo esc_html__('Default daily send limit for new accounts', BAUM_MAIL_TEXT_DOMAIN); ?></p>
              </td>
            </tr>
            <tr>
              <th scope="row"><?php echo esc_html__('Default Daily Receive Limit', BAUM_MAIL_TEXT_DOMAIN); ?></th>
              <td>
                <input type="number" id="baum_mail_default_daily_receive_limit" name="baum_mail_default_daily_receive_limit" value="<?php echo esc_attr(get_option('baum_mail_default_daily_receive_limit', 5000)); ?>" min="0" class="small-text" />
                <p class="description"><?php echo esc_html__('Default daily receive limit for new accounts', BAUM_MAIL_TEXT_DOMAIN); ?></p>
              </td>
            </tr>
            <tr>
              <th scope="row"><?php echo esc_html__('Default Max Accounts per Domain', BAUM_MAIL_TEXT_DOMAIN); ?></th>
              <td>
                <input type="number" id="baum_mail_default_max_accounts" name="baum_mail_default_max_accounts" value="<?php echo esc_attr(get_option('baum_mail_default_max_accounts', 100)); ?>" min="0" class="small-text" />
                <p class="description"><?php echo esc_html__('Default maximum accounts per domain', BAUM_MAIL_TEXT_DOMAIN); ?></p>
              </td>
            </tr>
          </table>
        </div>

        <?php submit_button(); ?>
      </form>
    </div>

    <script>
    jQuery(document).ready(function($) {
      $('.nav-tab').click(function(e) {
        e.preventDefault();
        var target = $(this).attr('href');

        $('.nav-tab').removeClass('nav-tab-active');
        $(this).addClass('nav-tab-active');

        $('.tab-content').hide();
        $(target).show();
      });
    });
    </script>
    <?php
  }

  /**
   * AJAX: Create alias
   *
   * @since 1.0.0
   */
  public function ajax_create_alias() {
    check_ajax_referer('baum_mail_admin_nonce', 'nonce');

    if (!current_user_can('manage_options')) {
      wp_die(__('Insufficient permissions.', BAUM_MAIL_TEXT_DOMAIN));
    }

    $source = sanitize_email($_POST['source']);
    $destination = sanitize_textarea_field($_POST['destination']);
    $domain_id = intval($_POST['domain_id']);

    $result = $this->core->create_alias($source, $destination, $domain_id);

    if (is_wp_error($result)) {
      wp_send_json_error($result->get_error_message());
    } else {
      wp_send_json_success(__('Alias created successfully.', BAUM_MAIL_TEXT_DOMAIN));
    }
  }

  /**
   * AJAX: Delete item
   *
   * @since 1.0.0
   */
  public function ajax_delete_item() {
    check_ajax_referer('baum_mail_admin_nonce', 'nonce');

    if (!current_user_can('manage_options')) {
      wp_die(__('Insufficient permissions.', BAUM_MAIL_TEXT_DOMAIN));
    }

    $id = intval($_POST['id']);
    $type = sanitize_text_field($_POST['type']);

    switch ($type) {
      case 'domain':
        $result = $this->core->delete_domain($id);
        break;
      case 'account':
        $result = $this->core->delete_account($id);
        break;
      case 'alias':
        $result = $this->core->delete_alias($id);
        break;
      default:
        wp_send_json_error(__('Invalid item type.', BAUM_MAIL_TEXT_DOMAIN));
        return;
    }

    if (is_wp_error($result)) {
      wp_send_json_error($result->get_error_message());
    } else {
      wp_send_json_success(__('Item deleted successfully.', BAUM_MAIL_TEXT_DOMAIN));
    }
  }

  /**
   * AJAX: Toggle status
   *
   * @since 1.0.0
   */
  public function ajax_toggle_status() {
    check_ajax_referer('baum_mail_admin_nonce', 'nonce');

    if (!current_user_can('manage_options')) {
      wp_die(__('Insufficient permissions.', BAUM_MAIL_TEXT_DOMAIN));
    }

    $id = intval($_POST['id']);
    $type = sanitize_text_field($_POST['type']);

    switch ($type) {
      case 'domain':
        $result = $this->core->toggle_domain_status($id);
        break;
      case 'account':
        $result = $this->core->toggle_account_status($id);
        break;
      case 'alias':
        $result = $this->core->toggle_alias_status($id);
        break;
      default:
        wp_send_json_error(__('Invalid item type.', BAUM_MAIL_TEXT_DOMAIN));
        return;
    }

    if (is_wp_error($result)) {
      wp_send_json_error($result->get_error_message());
    } else {
      wp_send_json_success(__('Status updated successfully.', BAUM_MAIL_TEXT_DOMAIN));
    }
  }

  /**
   * Analytics admin page
   *
   * @since 1.0.0
   */
  public function admin_page_analytics() {
    $analytics = baum_mail()->get_component('analytics');
    ?>
    <div class="wrap">
      <h1><?php echo esc_html__('Email Analytics', BAUM_MAIL_TEXT_DOMAIN); ?></h1>

      <div class="analytics-controls">
        <label for="date-from"><?php echo esc_html__('From:', BAUM_MAIL_TEXT_DOMAIN); ?></label>
        <input type="date" id="date-from" value="<?php echo date('Y-m-d', strtotime('-30 days')); ?>" />

        <label for="date-to"><?php echo esc_html__('To:', BAUM_MAIL_TEXT_DOMAIN); ?></label>
        <input type="date" id="date-to" value="<?php echo date('Y-m-d'); ?>" />

        <button class="button button-primary" id="refresh-analytics"><?php echo esc_html__('Refresh', BAUM_MAIL_TEXT_DOMAIN); ?></button>
      </div>

      <div class="analytics-stats">
        <div class="stat-box">
          <h3><?php echo esc_html__('Total Emails Sent', BAUM_MAIL_TEXT_DOMAIN); ?></h3>
          <span class="stat-number" id="total-sent">-</span>
        </div>
        <div class="stat-box">
          <h3><?php echo esc_html__('Total Opens', BAUM_MAIL_TEXT_DOMAIN); ?></h3>
          <span class="stat-number" id="total-opens">-</span>
        </div>
        <div class="stat-box">
          <h3><?php echo esc_html__('Unique Opens', BAUM_MAIL_TEXT_DOMAIN); ?></h3>
          <span class="stat-number" id="unique-opens">-</span>
        </div>
        <div class="stat-box">
          <h3><?php echo esc_html__('Open Rate', BAUM_MAIL_TEXT_DOMAIN); ?></h3>
          <span class="stat-number" id="open-rate">-</span>
        </div>
      </div>

      <div class="analytics-map-container">
        <h2><?php echo esc_html__('Geographic Distribution', BAUM_MAIL_TEXT_DOMAIN); ?></h2>
        <div id="world-map" style="height: 500px; width: 100%;"></div>
        <div class="map-controls">
          <button class="button" id="show-world"><?php echo esc_html__('World View', BAUM_MAIL_TEXT_DOMAIN); ?></button>
          <button class="button" id="show-usa"><?php echo esc_html__('USA View', BAUM_MAIL_TEXT_DOMAIN); ?></button>
        </div>
      </div>

      <div class="analytics-table-container">
        <h2><?php echo esc_html__('Detailed Opens', BAUM_MAIL_TEXT_DOMAIN); ?></h2>
        <table class="wp-list-table widefat fixed striped" id="opens-table">
          <thead>
            <tr>
              <th class="sortable" data-sort="ip"><?php echo esc_html__('IP Address', BAUM_MAIL_TEXT_DOMAIN); ?></th>
              <th class="sortable" data-sort="location"><?php echo esc_html__('Location', BAUM_MAIL_TEXT_DOMAIN); ?></th>
              <th class="sortable" data-sort="email"><?php echo esc_html__('Email', BAUM_MAIL_TEXT_DOMAIN); ?></th>
              <th class="sortable" data-sort="subject"><?php echo esc_html__('Subject', BAUM_MAIL_TEXT_DOMAIN); ?></th>
              <th class="sortable" data-sort="date"><?php echo esc_html__('Opened At', BAUM_MAIL_TEXT_DOMAIN); ?></th>
            </tr>
          </thead>
          <tbody id="opens-table-body">
            <!-- Data loaded via AJAX -->
          </tbody>
        </table>
        <div class="tablenav">
          <div class="tablenav-pages">
            <span class="pagination-links">
              <button class="button" id="prev-page" disabled><?php echo esc_html__('Previous', BAUM_MAIL_TEXT_DOMAIN); ?></button>
              <span class="page-numbers">Page <span id="current-page">1</span> of <span id="total-pages">1</span></span>
              <button class="button" id="next-page"><?php echo esc_html__('Next', BAUM_MAIL_TEXT_DOMAIN); ?></button>
            </span>
          </div>
        </div>
      </div>
    </div>

    <script>
    jQuery(document).ready(function($) {
      let currentPage = 1;
      let totalPages = 1;
      let analyticsData = {};

      // Load initial data
      loadAnalyticsData();

      // Refresh button
      $('#refresh-analytics').click(function() {
        loadAnalyticsData();
      });

      // Map controls
      $('#show-world').click(function() {
        showWorldMap();
      });

      $('#show-usa').click(function() {
        showUSAMap();
      });

      // Pagination
      $('#prev-page').click(function() {
        if (currentPage > 1) {
          currentPage--;
          updateTable();
        }
      });

      $('#next-page').click(function() {
        if (currentPage < totalPages) {
          currentPage++;
          updateTable();
        }
      });

      // Table sorting
      $('.sortable').click(function() {
        const sortBy = $(this).data('sort');
        sortTable(sortBy);
      });

      function loadAnalyticsData() {
        const dateFrom = $('#date-from').val();
        const dateTo = $('#date-to').val();

        $.ajax({
          url: ajaxurl,
          type: 'POST',
          data: {
            action: 'baum_mail_get_analytics_data',
            nonce: baumMailAdmin.nonce,
            date_from: dateFrom,
            date_to: dateTo
          },
          success: function(response) {
            if (response.success) {
              analyticsData = response.data;
              updateStats();
              updateMap();
              updateTable();
            }
          }
        });
      }

      function updateStats() {
        $('#total-sent').text(analyticsData.stats.total_emails_sent);
        $('#total-opens').text(analyticsData.stats.total_opens);
        $('#unique-opens').text(analyticsData.stats.unique_opens);
        $('#open-rate').text(analyticsData.stats.open_rate + '%');
      }

      function updateMap() {
        // Initialize world map with country data
        showWorldMap();
      }

      function showWorldMap() {
        // This would integrate with a mapping library like D3.js or Google Maps
        // For now, we'll create a simple representation
        $('#world-map').html('<div class="map-placeholder">World Map - Country data visualization would go here</div>');
      }

      function showUSAMap() {
        // Show US state map
        $('#world-map').html('<div class="map-placeholder">USA Map - State data visualization would go here</div>');
      }

      function updateTable() {
        const itemsPerPage = 50;
        const startIndex = (currentPage - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        const pageData = analyticsData.opens.slice(startIndex, endIndex);

        let tableHTML = '';
        pageData.forEach(function(open) {
          const location = [open.city, open.region, open.country].filter(Boolean).join(', ');
          tableHTML += '<tr>';
          tableHTML += '<td>' + (open.ip_address || '-') + '</td>';
          tableHTML += '<td>' + (location || '-') + '</td>';
          tableHTML += '<td>' + (open.recipient_email || '-') + '</td>';
          tableHTML += '<td>' + (open.subject || '-') + '</td>';
          tableHTML += '<td>' + (open.opened_at || '-') + '</td>';
          tableHTML += '</tr>';
        });

        $('#opens-table-body').html(tableHTML);

        // Update pagination
        totalPages = Math.ceil(analyticsData.opens.length / itemsPerPage);
        $('#current-page').text(currentPage);
        $('#total-pages').text(totalPages);
        $('#prev-page').prop('disabled', currentPage === 1);
        $('#next-page').prop('disabled', currentPage === totalPages);
      }

      function sortTable(sortBy) {
        // Implement table sorting
        analyticsData.opens.sort(function(a, b) {
          let aVal = a[sortBy] || '';
          let bVal = b[sortBy] || '';
          return aVal.localeCompare(bVal);
        });
        currentPage = 1;
        updateTable();
      }
    });
    </script>
    <?php
  }

  /**
   * Logs admin page
   *
   * @since 1.0.0
   */
  public function admin_page_logs() {
    ?>
    <div class="wrap">
      <h1><?php echo esc_html__('System Logs', BAUM_MAIL_TEXT_DOMAIN); ?></h1>

      <div class="logs-container">
        <div class="logs-tabs">
          <button class="log-tab active" data-log="baum-mail"><?php echo esc_html__('Baum Mail', BAUM_MAIL_TEXT_DOMAIN); ?></button>
          <button class="log-tab" data-log="postfix"><?php echo esc_html__('Postfix', BAUM_MAIL_TEXT_DOMAIN); ?></button>
          <button class="log-tab" data-log="dovecot"><?php echo esc_html__('Dovecot', BAUM_MAIL_TEXT_DOMAIN); ?></button>
          <button class="log-tab" data-log="clamav"><?php echo esc_html__('ClamAV', BAUM_MAIL_TEXT_DOMAIN); ?></button>
          <button class="log-tab" data-log="spamassassin"><?php echo esc_html__('SpamAssassin', BAUM_MAIL_TEXT_DOMAIN); ?></button>
          <button class="log-tab" data-log="gpg"><?php echo esc_html__('GPG', BAUM_MAIL_TEXT_DOMAIN); ?></button>
        </div>

        <div class="logs-controls">
          <button class="button" id="refresh-logs"><?php echo esc_html__('Refresh', BAUM_MAIL_TEXT_DOMAIN); ?></button>
          <button class="button" id="clear-logs"><?php echo esc_html__('Clear', BAUM_MAIL_TEXT_DOMAIN); ?></button>
          <label for="auto-refresh">
            <input type="checkbox" id="auto-refresh" /> <?php echo esc_html__('Auto-refresh (5s)', BAUM_MAIL_TEXT_DOMAIN); ?>
          </label>
        </div>

        <div class="terminal-window">
          <div class="terminal-header">
            <span class="terminal-title" id="terminal-title"><?php echo esc_html__('Baum Mail Logs', BAUM_MAIL_TEXT_DOMAIN); ?></span>
          </div>
          <pre class="terminal-content" id="terminal-content">
Loading logs...
          </pre>
        </div>
      </div>
    </div>

    <script>
    jQuery(document).ready(function($) {
      let currentLog = 'baum-mail';
      let autoRefreshInterval;

      // Tab switching
      $('.log-tab').click(function() {
        $('.log-tab').removeClass('active');
        $(this).addClass('active');
        currentLog = $(this).data('log');
        $('#terminal-title').text($(this).text() + ' Logs');
        loadLogs();
      });

      // Refresh button
      $('#refresh-logs').click(function() {
        loadLogs();
      });

      // Clear button
      $('#clear-logs').click(function() {
        if (confirm('Are you sure you want to clear the logs?')) {
          clearLogs();
        }
      });

      // Auto-refresh toggle
      $('#auto-refresh').change(function() {
        if ($(this).is(':checked')) {
          autoRefreshInterval = setInterval(loadLogs, 5000);
        } else {
          clearInterval(autoRefreshInterval);
        }
      });

      // Load initial logs
      loadLogs();

      function loadLogs() {
        $.ajax({
          url: ajaxurl,
          type: 'POST',
          data: {
            action: 'baum_mail_get_logs',
            nonce: baumMailAdmin.nonce,
            log_type: currentLog
          },
          success: function(response) {
            if (response.success) {
              $('#terminal-content').text(response.data.content);
              // Scroll to bottom
              const terminal = document.getElementById('terminal-content');
              terminal.scrollTop = terminal.scrollHeight;
            }
          }
        });
      }

      function clearLogs() {
        $.ajax({
          url: ajaxurl,
          type: 'POST',
          data: {
            action: 'baum_mail_clear_logs',
            nonce: baumMailAdmin.nonce,
            log_type: currentLog
          },
          success: function(response) {
            if (response.success) {
              loadLogs();
            }
          }
        });
      }
    });
    </script>
    <?php
  }

  /**
   * AJAX: Get logs
   *
   * @since 1.0.0
   */
  public function ajax_get_logs() {
    check_ajax_referer('baum_mail_admin_nonce', 'nonce');

    if (!current_user_can('manage_options')) {
      wp_die(__('Insufficient permissions.', BAUM_MAIL_TEXT_DOMAIN));
    }

    $log_type = sanitize_text_field($_POST['log_type']);
    $log_content = $this->get_log_content($log_type);

    wp_send_json_success(array('content' => $log_content));
  }

  /**
   * AJAX: Clear logs
   *
   * @since 1.0.0
   */
  public function ajax_clear_logs() {
    check_ajax_referer('baum_mail_admin_nonce', 'nonce');

    if (!current_user_can('manage_options')) {
      wp_die(__('Insufficient permissions.', BAUM_MAIL_TEXT_DOMAIN));
    }

    $log_type = sanitize_text_field($_POST['log_type']);
    $result = $this->clear_log_file($log_type);

    if ($result) {
      wp_send_json_success(__('Log cleared successfully.', BAUM_MAIL_TEXT_DOMAIN));
    } else {
      wp_send_json_error(__('Failed to clear log.', BAUM_MAIL_TEXT_DOMAIN));
    }
  }

  /**
   * AJAX: Restart service
   *
   * @since 1.0.0
   */
  public function ajax_restart_service() {
    check_ajax_referer('baum_mail_admin_nonce', 'nonce');

    if (!current_user_can('manage_options')) {
      wp_die(__('Insufficient permissions.', BAUM_MAIL_TEXT_DOMAIN));
    }

    $service = sanitize_text_field($_POST['service']);
    $result = $this->restart_service($service);

    if ($result) {
      wp_send_json_success(sprintf(__('%s restarted successfully.', BAUM_MAIL_TEXT_DOMAIN), ucfirst($service)));
    } else {
      wp_send_json_error(sprintf(__('Failed to restart %s.', BAUM_MAIL_TEXT_DOMAIN), $service));
    }
  }

  /**
   * Get log content
   *
   * @param string $log_type Log type
   * @return string Log content
   * @since 1.0.0
   */
  private function get_log_content($log_type) {
    $log_files = array(
      'baum-mail' => BAUM_MAIL_PLUGIN_DIR . 'logs/baum-mail.log',
      'postfix' => BAUM_MAIL_PLUGIN_DIR . 'logs/postfix.log',
      'dovecot' => BAUM_MAIL_PLUGIN_DIR . 'logs/dovecot.log',
      'clamav' => BAUM_MAIL_PLUGIN_DIR . 'logs/clamav.log',
      'spamassassin' => BAUM_MAIL_PLUGIN_DIR . 'logs/spamassassin.log',
      'gpg' => BAUM_MAIL_PLUGIN_DIR . 'logs/gpg.log'
    );

    if (!isset($log_files[$log_type])) {
      return 'Invalid log type.';
    }

    $log_file = $log_files[$log_type];

    if (!file_exists($log_file)) {
      return 'Log file not found.';
    }

    // Get last 1000 lines
    $content = shell_exec("tail -n 1000 " . escapeshellarg($log_file));

    if (empty($content)) {
      return 'Log file is empty.';
    }

    return $content;
  }

  /**
   * Clear log file
   *
   * @param string $log_type Log type
   * @return bool Success
   * @since 1.0.0
   */
  private function clear_log_file($log_type) {
    $log_files = array(
      'baum-mail' => BAUM_MAIL_PLUGIN_DIR . 'logs/baum-mail.log',
      'postfix' => BAUM_MAIL_PLUGIN_DIR . 'logs/postfix.log',
      'dovecot' => BAUM_MAIL_PLUGIN_DIR . 'logs/dovecot.log',
      'clamav' => BAUM_MAIL_PLUGIN_DIR . 'logs/clamav.log',
      'spamassassin' => BAUM_MAIL_PLUGIN_DIR . 'logs/spamassassin.log',
      'gpg' => BAUM_MAIL_PLUGIN_DIR . 'logs/gpg.log'
    );

    if (!isset($log_files[$log_type])) {
      return false;
    }

    $log_file = $log_files[$log_type];

    // Clear the log file
    return file_put_contents($log_file, '') !== false;
  }

  /**
   * Restart service
   *
   * @param string $service Service name
   * @return bool Success
   * @since 1.0.0
   */
  private function restart_service($service) {
    $allowed_services = array('postfix', 'dovecot', 'clamav', 'spamassassin');

    if (!in_array($service, $allowed_services)) {
      return false;
    }

    // Log the restart request
    $this->log_to_file('baum-mail', "Service restart requested: {$service} by user " . get_current_user_id());

    // Detect operating system and use appropriate commands
    $os = $this->detect_operating_system();
    $commands = $this->get_service_commands($os, $service);

    if (!$commands) {
      $this->log_to_file('baum-mail', "Service restart failed for {$service}: Unsupported OS or service");
      return false;
    }

    // Execute restart command
    $output = shell_exec($commands['restart'] . ' 2>&1');

    // Log the result
    $this->log_to_file('baum-mail', "Service restart result for {$service}: " . ($output ?: 'Success'));

    // Check if service is running
    $status = trim(shell_exec($commands['status']));

    // Determine if service is active based on OS
    $is_active = $this->is_service_active($os, $status);

    $this->log_to_file('baum-mail', "Service status check for {$service}: {$status} (active: " . ($is_active ? 'yes' : 'no') . ")");

    return $is_active;
  }

  /**
   * Detect operating system
   *
   * @return string Operating system (macos, linux, unknown)
   * @since 1.0.0
   */
  private function detect_operating_system() {
    $uname = strtolower(php_uname('s'));

    if (strpos($uname, 'darwin') !== false) {
      return 'macos';
    } elseif (strpos($uname, 'linux') !== false) {
      return 'linux';
    }

    return 'unknown';
  }

  /**
   * Get service commands for operating system
   *
   * @param string $os Operating system
   * @param string $service Service name
   * @return array|false Commands array or false if not supported
   * @since 1.0.0
   */
  private function get_service_commands($os, $service) {
    $commands = array();

    switch ($os) {
      case 'macos':
        // macOS uses brew services or launchctl
        if ($this->command_exists('brew')) {
          $commands['restart'] = "brew services restart {$service}";
          $commands['status'] = "brew services list | grep {$service}";
        } else {
          // Fallback to launchctl for system services
          $service_map = array(
            'postfix' => 'org.postfix.master',
            'dovecot' => 'org.dovecot.dovecot',
            'clamav' => 'org.clamav.clamd',
            'spamassassin' => 'org.apache.spamassassin.spamd'
          );

          if (isset($service_map[$service])) {
            $commands['restart'] = "sudo launchctl unload /System/Library/LaunchDaemons/{$service_map[$service]}.plist && sudo launchctl load /System/Library/LaunchDaemons/{$service_map[$service]}.plist";
            $commands['status'] = "launchctl list | grep {$service_map[$service]}";
          } else {
            return false;
          }
        }
        break;

      case 'linux':
        // Linux uses systemctl
        if ($this->command_exists('systemctl')) {
          $commands['restart'] = "sudo systemctl restart {$service}";
          $commands['status'] = "systemctl is-active {$service}";
        } else {
          // Fallback to service command
          $commands['restart'] = "sudo service {$service} restart";
          $commands['status'] = "service {$service} status";
        }
        break;

      default:
        return false;
    }

    return $commands;
  }

  /**
   * Check if command exists
   *
   * @param string $command Command to check
   * @return bool Command exists
   * @since 1.0.0
   */
  private function command_exists($command) {
    $result = shell_exec("which {$command} 2>/dev/null");
    return !empty($result);
  }

  /**
   * Check if service is active based on OS and status output
   *
   * @param string $os Operating system
   * @param string $status Status output
   * @return bool Service is active
   * @since 1.0.0
   */
  private function is_service_active($os, $status) {
    switch ($os) {
      case 'macos':
        // For brew services, look for "started" status
        // For launchctl, look for PID
        return (strpos($status, 'started') !== false) ||
               (is_numeric(trim($status)) && trim($status) > 0);

      case 'linux':
        // For systemctl, look for "active" status
        // For service command, look for "running" or "active"
        return (strpos($status, 'active') !== false) ||
               (strpos($status, 'running') !== false);

      default:
        return false;
    }
  }

  /**
   * Log message to file
   *
   * @param string $log_type Log type
   * @param string $message Message
   * @since 1.0.0
   */
  private function log_to_file($log_type, $message) {
    $log_files = array(
      'baum-mail' => BAUM_MAIL_PLUGIN_DIR . 'logs/baum-mail.log',
      'postfix' => BAUM_MAIL_PLUGIN_DIR . 'logs/postfix.log',
      'dovecot' => BAUM_MAIL_PLUGIN_DIR . 'logs/dovecot.log',
      'clamav' => BAUM_MAIL_PLUGIN_DIR . 'logs/clamav.log',
      'spamassassin' => BAUM_MAIL_PLUGIN_DIR . 'logs/spamassassin.log',
      'gpg' => BAUM_MAIL_PLUGIN_DIR . 'logs/gpg.log'
    );

    if (!isset($log_files[$log_type])) {
      return;
    }

    $log_file = $log_files[$log_type];
    $timestamp = date('Y-m-d H:i:s');
    $log_entry = "[{$timestamp}] {$message}\n";

    file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
  }

  /**
   * Get WordPress domains
   *
   * @return array WordPress domains
   * @since 1.0.0
   */
  private function get_wordpress_domains() {
    $domains = array();

    // Get main site domain
    $site_url = parse_url(get_site_url(), PHP_URL_HOST);
    if ($site_url) {
      $domains[] = $site_url;
    }

    // Get multisite domains if applicable
    if (is_multisite()) {
      $sites = get_sites(array('number' => 100));
      foreach ($sites as $site) {
        $domain = parse_url(get_site_url($site->blog_id), PHP_URL_HOST);
        if ($domain && !in_array($domain, $domains)) {
          $domains[] = $domain;
        }
      }
    }

    return array_unique($domains);
  }

  /**
   * Single domain admin page
   *
   * @since 1.0.0
   */
  public function admin_page_single_domain() {
    $domain_id = intval($_GET['id'] ?? 0);
    if (!$domain_id) {
      wp_die(__('Invalid domain ID.', BAUM_MAIL_TEXT_DOMAIN));
    }

    $core = baum_mail()->get_component('core');
    $domain = $core->get_domain($domain_id);

    if (!$domain) {
      wp_die(__('Domain not found.', BAUM_MAIL_TEXT_DOMAIN));
    }

    // Get WHOIS data
    $whois_data = $this->get_whois_data($domain->domain);

    // Get SSL certificate info
    $ssl_info = $this->get_ssl_certificate_info($domain->domain);

    // Get domain statistics
    $domain_stats = $core->get_domain_statistics($domain_id);
    ?>
    <div class="wrap">
      <h1><?php echo esc_html(sprintf(__('Domain: %s', BAUM_MAIL_TEXT_DOMAIN), $domain->domain)); ?></h1>

      <div class="nav-tab-wrapper">
        <a href="#overview" class="nav-tab nav-tab-active"><?php echo esc_html__('Overview', BAUM_MAIL_TEXT_DOMAIN); ?></a>
        <a href="#dns-config" class="nav-tab"><?php echo esc_html__('DNS Configuration', BAUM_MAIL_TEXT_DOMAIN); ?></a>
        <a href="#whois" class="nav-tab"><?php echo esc_html__('WHOIS Info', BAUM_MAIL_TEXT_DOMAIN); ?></a>
        <a href="#ssl" class="nav-tab"><?php echo esc_html__('SSL Certificate', BAUM_MAIL_TEXT_DOMAIN); ?></a>
      </div>

      <!-- Overview Tab -->
      <div id="overview" class="tab-content">
        <div class="domain-stats-grid">
          <div class="stat-card">
            <h3><?php echo esc_html__('Email Accounts', BAUM_MAIL_TEXT_DOMAIN); ?></h3>
            <span class="stat-number"><?php echo esc_html($domain_stats['accounts'] ?? 0); ?></span>
          </div>
          <div class="stat-card">
            <h3><?php echo esc_html__('Aliases', BAUM_MAIL_TEXT_DOMAIN); ?></h3>
            <span class="stat-number"><?php echo esc_html($domain_stats['aliases'] ?? 0); ?></span>
          </div>
          <div class="stat-card">
            <h3><?php echo esc_html__('Total Quota Used', BAUM_MAIL_TEXT_DOMAIN); ?></h3>
            <span class="stat-number"><?php echo esc_html($this->format_bytes($domain_stats['quota_used'] ?? 0)); ?></span>
          </div>
          <div class="stat-card">
            <h3><?php echo esc_html__('Emails Sent (30d)', BAUM_MAIL_TEXT_DOMAIN); ?></h3>
            <span class="stat-number"><?php echo esc_html($domain_stats['emails_sent'] ?? 0); ?></span>
          </div>
        </div>
      </div>

      <!-- DNS Configuration Tab -->
      <div id="dns-config" class="tab-content" style="display: none;">
        <h2><?php echo esc_html__('DNS Configuration for Route 53', BAUM_MAIL_TEXT_DOMAIN); ?></h2>
        <p><?php echo esc_html__('Add the following DNS records to your Route 53 hosted zone:', BAUM_MAIL_TEXT_DOMAIN); ?></p>

        <style>
        .dns-records {
          display: grid;
          gap: 20px;
          margin-top: 20px;
        }

        .dns-record {
          background: white;
          border: 1px solid #e1e5e9;
          border-radius: 8px;
          padding: 20px;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .dns-record-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 10px;
          padding-bottom: 10px;
          border-bottom: 1px solid #e1e5e9;
        }

        .dns-record-content {
          display: flex;
          align-items: center;
          gap: 10px;
          margin-bottom: 10px;
        }

        .dns-record-content code {
          flex: 1;
          background: #f8f9fa;
          padding: 10px;
          border-radius: 4px;
          font-family: monospace;
          border: 1px solid #e1e5e9;
        }

        .copy-dns-btn {
          background: #0073aa;
          color: white;
          border: none;
          padding: 8px 12px;
          border-radius: 4px;
          cursor: pointer;
          font-size: 12px;
        }

        .copy-dns-btn:hover {
          background: #005a87;
        }

        .dns-description {
          color: #666;
          font-size: 13px;
          margin: 0;
        }

        .dns-priority {
          background: #e3f2fd;
          color: #1976d2;
          padding: 2px 8px;
          border-radius: 12px;
          font-size: 12px;
        }

        .dns-setup-guide {
          background: #f0f8ff;
          padding: 20px;
          border-radius: 8px;
          margin-top: 30px;
          border-left: 4px solid #2196f3;
        }

        .dns-setup-guide h4 {
          margin-top: 0;
          color: #1976d2;
        }

        .dns-setup-guide ol {
          margin-bottom: 0;
        }

        .dns-setup-guide li {
          margin-bottom: 8px;
        }
        </style>

        <div class="dns-records">
          <div class="dns-record">
            <div class="dns-record-header">
              <strong><?php echo esc_html__('MX Record', BAUM_MAIL_TEXT_DOMAIN); ?></strong>
              <span class="dns-priority"><?php echo esc_html__('Priority: 10', BAUM_MAIL_TEXT_DOMAIN); ?></span>
            </div>
            <div class="dns-record-content">
              <code>@ IN MX 10 mail.<?php echo esc_html($domain->domain); ?></code>
              <button class="copy-dns-btn" data-clipboard="@ IN MX 10 mail.<?php echo esc_attr($domain->domain); ?>">
                <?php echo esc_html__('Copy', BAUM_MAIL_TEXT_DOMAIN); ?>
              </button>
            </div>
            <p class="dns-description"><?php echo esc_html__('Directs email to your mail server', BAUM_MAIL_TEXT_DOMAIN); ?></p>
          </div>

          <div class="dns-record">
            <div class="dns-record-header">
              <strong><?php echo esc_html__('A Record', BAUM_MAIL_TEXT_DOMAIN); ?></strong>
            </div>
            <div class="dns-record-content">
              <code>mail.<?php echo esc_html($domain->domain); ?> IN A <?php echo esc_html($server_ip); ?></code>
              <button class="copy-dns-btn" data-clipboard="mail.<?php echo esc_attr($domain->domain); ?> IN A <?php echo esc_attr($server_ip); ?>">
                <?php echo esc_html__('Copy', BAUM_MAIL_TEXT_DOMAIN); ?>
              </button>
            </div>
            <p class="dns-description"><?php echo esc_html__('Points mail subdomain to server IP', BAUM_MAIL_TEXT_DOMAIN); ?></p>
          </div>

          <div class="dns-record">
            <div class="dns-record-header">
              <strong><?php echo esc_html__('SPF Record', BAUM_MAIL_TEXT_DOMAIN); ?></strong>
            </div>
            <div class="dns-record-content">
              <code>@ IN TXT "v=spf1 mx a:mail.<?php echo esc_html($domain->domain); ?> ~all"</code>
              <button class="copy-dns-btn" data-clipboard='@ IN TXT "v=spf1 mx a:mail.<?php echo esc_attr($domain->domain); ?> ~all"'>
                <?php echo esc_html__('Copy', BAUM_MAIL_TEXT_DOMAIN); ?>
              </button>
            </div>
            <p class="dns-description"><?php echo esc_html__('Prevents email spoofing', BAUM_MAIL_TEXT_DOMAIN); ?></p>
          </div>

          <div class="dns-record">
            <div class="dns-record-header">
              <strong><?php echo esc_html__('DKIM Record', BAUM_MAIL_TEXT_DOMAIN); ?></strong>
              <button class="button button-small" id="generate-dkim-btn" data-domain="<?php echo esc_attr($domain->domain); ?>">
                <?php echo esc_html__('Generate DKIM', BAUM_MAIL_TEXT_DOMAIN); ?>
              </button>
            </div>
            <div class="dns-record-content" id="dkim-record-content">
              <p><?php echo esc_html__('Click "Generate DKIM" to create DKIM keys for this domain.', BAUM_MAIL_TEXT_DOMAIN); ?></p>
            </div>
            <p class="dns-description"><?php echo esc_html__('Authenticates emails from your domain', BAUM_MAIL_TEXT_DOMAIN); ?></p>
          </div>

          <div class="dns-record">
            <div class="dns-record-header">
              <strong><?php echo esc_html__('DMARC Record', BAUM_MAIL_TEXT_DOMAIN); ?></strong>
            </div>
            <div class="dns-record-content">
              <code>_dmarc.<?php echo esc_html($domain->domain); ?> IN TXT "v=DMARC1; p=quarantine; rua=mailto:dmarc@<?php echo esc_html($domain->domain); ?>"</code>
              <button class="copy-dns-btn" data-clipboard='_dmarc.<?php echo esc_attr($domain->domain); ?> IN TXT "v=DMARC1; p=quarantine; rua=mailto:dmarc@<?php echo esc_attr($domain->domain); ?>"'>
                <?php echo esc_html__('Copy', BAUM_MAIL_TEXT_DOMAIN); ?>
              </button>
            </div>
            <p class="dns-description"><?php echo esc_html__('Email authentication policy', BAUM_MAIL_TEXT_DOMAIN); ?></p>
          </div>
        </div>

        <div class="dns-setup-guide">
          <h4><?php echo esc_html__('Setup Instructions', BAUM_MAIL_TEXT_DOMAIN); ?></h4>
          <ol>
            <li><?php echo esc_html__('Copy each DNS record above', BAUM_MAIL_TEXT_DOMAIN); ?></li>
            <li><?php echo esc_html__('Log into your domain registrar or DNS provider', BAUM_MAIL_TEXT_DOMAIN); ?></li>
            <li><?php echo esc_html__('Add the records to your DNS zone', BAUM_MAIL_TEXT_DOMAIN); ?></li>
            <li><?php echo esc_html__('Wait 24-48 hours for DNS propagation', BAUM_MAIL_TEXT_DOMAIN); ?></li>
            <li><?php echo esc_html__('Test your configuration using online tools', BAUM_MAIL_TEXT_DOMAIN); ?></li>
          </ol>
        </div>
      </div>

      <!-- WHOIS Tab -->
      <div id="whois" class="tab-content" style="display: none;">
        <h2><?php echo esc_html__('Domain Information & Network Analysis', BAUM_MAIL_TEXT_DOMAIN); ?></h2>

        <?php
        $domain_info = $this->get_domain_info($domain->domain);
        ?>

        <div class="domain-info-grid">
          <!-- DNS Records -->
          <?php if (!empty($domain_info['dns_records'])): ?>
          <div class="info-section">
            <h3><?php echo esc_html__('DNS Records', BAUM_MAIL_TEXT_DOMAIN); ?></h3>
            <div class="dns-records-display">
              <?php foreach ($domain_info['dns_records'] as $type => $records): ?>
                <div class="dns-record-type">
                  <h4><?php echo esc_html($type); ?> <?php echo esc_html__('Records', BAUM_MAIL_TEXT_DOMAIN); ?></h4>
                  <ul>
                    <?php foreach ($records as $record): ?>
                      <li><code><?php echo esc_html($record); ?></code></li>
                    <?php endforeach; ?>
                  </ul>
                </div>
              <?php endforeach; ?>
            </div>
          </div>
          <?php endif; ?>

          <!-- Network Information -->
          <?php if (!empty($domain_info['network_info'])): ?>
          <div class="info-section">
            <h3><?php echo esc_html__('Network Information', BAUM_MAIL_TEXT_DOMAIN); ?></h3>
            <?php $network = $domain_info['network_info']; ?>

            <?php if (!empty($network['ip'])): ?>
              <p><strong><?php echo esc_html__('IP Address:', BAUM_MAIL_TEXT_DOMAIN); ?></strong> <?php echo esc_html($network['ip']); ?></p>
            <?php endif; ?>

            <?php if (!empty($network['reverse_dns'])): ?>
              <p><strong><?php echo esc_html__('Reverse DNS:', BAUM_MAIL_TEXT_DOMAIN); ?></strong> <?php echo esc_html($network['reverse_dns']); ?></p>
            <?php endif; ?>

            <?php if (!empty($network['ping'])): ?>
              <h4><?php echo esc_html__('Ping Test', BAUM_MAIL_TEXT_DOMAIN); ?></h4>
              <pre class="network-output"><?php echo esc_html($network['ping']); ?></pre>
            <?php endif; ?>

            <?php if (!empty($network['traceroute'])): ?>
              <h4><?php echo esc_html__('Traceroute', BAUM_MAIL_TEXT_DOMAIN); ?></h4>
              <pre class="network-output"><?php echo esc_html($network['traceroute']); ?></pre>
            <?php endif; ?>
          </div>
          <?php endif; ?>

          <!-- Mail Server Information -->
          <?php if (!empty($domain_info['mail_servers'])): ?>
          <div class="info-section">
            <h3><?php echo esc_html__('Mail Server Analysis', BAUM_MAIL_TEXT_DOMAIN); ?></h3>
            <?php $mail_servers = $domain_info['mail_servers']; ?>

            <?php if (!empty($mail_servers['mx_records'])): ?>
              <h4><?php echo esc_html__('MX Records', BAUM_MAIL_TEXT_DOMAIN); ?></h4>
              <table class="wp-list-table widefat fixed striped">
                <thead>
                  <tr>
                    <th><?php echo esc_html__('Priority', BAUM_MAIL_TEXT_DOMAIN); ?></th>
                    <th><?php echo esc_html__('Mail Server', BAUM_MAIL_TEXT_DOMAIN); ?></th>
                    <th><?php echo esc_html__('IP Address', BAUM_MAIL_TEXT_DOMAIN); ?></th>
                  </tr>
                </thead>
                <tbody>
                  <?php foreach ($mail_servers['mx_records'] as $mx): ?>
                    <tr>
                      <td><?php echo esc_html($mx['priority']); ?></td>
                      <td><?php echo esc_html($mx['server']); ?></td>
                      <td><?php echo esc_html($mx['ip'] ?: __('N/A', BAUM_MAIL_TEXT_DOMAIN)); ?></td>
                    </tr>
                  <?php endforeach; ?>
                </tbody>
              </table>
            <?php endif; ?>

            <?php if (!empty($mail_servers['smtp_test'])): ?>
              <h4><?php echo esc_html__('SMTP Port Test', BAUM_MAIL_TEXT_DOMAIN); ?></h4>
              <div class="smtp-ports">
                <?php foreach ($mail_servers['smtp_test'] as $port => $status): ?>
                  <span class="port-status <?php echo $status; ?>">
                    Port <?php echo esc_html($port); ?>: <?php echo esc_html(ucfirst($status)); ?>
                  </span>
                <?php endforeach; ?>
              </div>
            <?php endif; ?>
          </div>
          <?php endif; ?>

          <!-- WHOIS Data -->
          <?php if (!empty($domain_info['whois'])): ?>
          <div class="info-section full-width">
            <h3><?php echo esc_html__('WHOIS Information', BAUM_MAIL_TEXT_DOMAIN); ?></h3>
            <pre class="whois-data"><?php echo esc_html($domain_info['whois']); ?></pre>
          </div>
          <?php endif; ?>
        </div>

        <style>
        .domain-info-grid {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 20px;
          margin-top: 20px;
        }

        .info-section {
          background: white;
          padding: 20px;
          border-radius: 8px;
          border: 1px solid #e1e5e9;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .info-section.full-width {
          grid-column: 1 / -1;
        }

        .info-section h3 {
          margin-top: 0;
          color: #2c3e50;
          border-bottom: 2px solid #e1e5e9;
          padding-bottom: 10px;
        }

        .info-section h4 {
          color: #34495e;
          margin-top: 20px;
          margin-bottom: 10px;
        }

        .dns-records-display {
          display: grid;
          gap: 15px;
        }

        .dns-record-type ul {
          margin: 0;
          padding-left: 20px;
        }

        .dns-record-type li {
          margin-bottom: 5px;
        }

        .dns-record-type code {
          background: #f8f9fa;
          padding: 2px 6px;
          border-radius: 3px;
          font-size: 13px;
        }

        .network-output {
          background: #1e1e1e;
          color: #d4d4d4;
          padding: 15px;
          border-radius: 6px;
          font-family: monospace;
          font-size: 12px;
          overflow-x: auto;
        }

        .smtp-ports {
          display: flex;
          gap: 15px;
          flex-wrap: wrap;
        }

        .port-status {
          padding: 8px 12px;
          border-radius: 20px;
          font-size: 12px;
          font-weight: bold;
        }

        .port-status.open {
          background: #d4edda;
          color: #155724;
          border: 1px solid #c3e6cb;
        }

        .port-status.closed {
          background: #f8d7da;
          color: #721c24;
          border: 1px solid #f5c6cb;
        }

        .whois-data {
          background: #1e1e1e;
          color: #d4d4d4;
          padding: 20px;
          border-radius: 6px;
          font-family: monospace;
          font-size: 12px;
          line-height: 1.4;
          overflow-x: auto;
          max-height: 400px;
          overflow-y: auto;
        }

        @media (max-width: 768px) {
          .domain-info-grid {
            grid-template-columns: 1fr;
          }
        }
        </style>
      </div>

      <!-- SSL Certificate Tab -->
      <div id="ssl" class="tab-content" style="display: none;">
        <h2><?php echo esc_html__('SSL Certificate Information', BAUM_MAIL_TEXT_DOMAIN); ?></h2>
        <?php if ($ssl_info): ?>
          <div class="ssl-info">
            <p><strong><?php echo esc_html__('Issuer:', BAUM_MAIL_TEXT_DOMAIN); ?></strong> <?php echo esc_html($ssl_info['issuer'] ?? 'Unknown'); ?></p>
            <p><strong><?php echo esc_html__('Valid From:', BAUM_MAIL_TEXT_DOMAIN); ?></strong> <?php echo esc_html($ssl_info['valid_from'] ?? 'Unknown'); ?></p>
            <p><strong><?php echo esc_html__('Valid To:', BAUM_MAIL_TEXT_DOMAIN); ?></strong> <?php echo esc_html($ssl_info['valid_to'] ?? 'Unknown'); ?></p>
            <p><strong><?php echo esc_html__('Subject:', BAUM_MAIL_TEXT_DOMAIN); ?></strong> <?php echo esc_html($ssl_info['subject'] ?? 'Unknown'); ?></p>
          </div>
        <?php else: ?>
          <p><?php echo esc_html__('SSL certificate information not available.', BAUM_MAIL_TEXT_DOMAIN); ?></p>
        <?php endif; ?>
      </div>
    </div>

    <script>
    jQuery(document).ready(function($) {
      $('.nav-tab').click(function(e) {
        e.preventDefault();
        var target = $(this).attr('href');

        $('.nav-tab').removeClass('nav-tab-active');
        $(this).addClass('nav-tab-active');

        $('.tab-content').hide();
        $(target).show();
      });
    });
    </script>
    <?php
  }

  /**
   * Get WHOIS data for domain
   *
   * @param string $domain Domain name
   * @return string|false WHOIS data or false on failure
   * @since 1.0.0
   */
  private function get_whois_data($domain) {
    // Simple WHOIS lookup using system command
    $output = shell_exec("whois " . escapeshellarg($domain) . " 2>/dev/null");
    return $output ?: false;
  }

  /**
   * Get SSL certificate information
   *
   * @param string $domain Domain name
   * @return array|false SSL info or false on failure
   * @since 1.0.0
   */
  private function get_ssl_certificate_info($domain) {
    $context = stream_context_create(array(
      'ssl' => array(
        'capture_peer_cert' => true,
        'verify_peer' => false,
        'verify_peer_name' => false
      )
    ));

    $socket = @stream_socket_client("ssl://{$domain}:443", $errno, $errstr, 30, STREAM_CLIENT_CONNECT, $context);

    if (!$socket) {
      return false;
    }

    $params = stream_context_get_params($socket);
    $cert = $params['options']['ssl']['peer_certificate'];

    if (!$cert) {
      fclose($socket);
      return false;
    }

    $cert_info = openssl_x509_parse($cert);
    fclose($socket);

    // Calculate days until expiration
    $expires_timestamp = $cert_info['validTo_time_t'];
    $days_until_expiry = ceil(($expires_timestamp - time()) / 86400);

    return array(
      'issuer' => $cert_info['issuer']['CN'] ?? 'Unknown',
      'subject' => $cert_info['subject']['CN'] ?? 'Unknown',
      'valid_from' => date('Y-m-d H:i:s', $cert_info['validFrom_time_t']),
      'valid_to' => date('Y-m-d H:i:s', $cert_info['validTo_time_t']),
      'days_until_expiry' => $days_until_expiry,
      'is_expired' => $days_until_expiry < 0,
      'expires_soon' => $days_until_expiry < 30,
      'serial_number' => $cert_info['serialNumber'] ?? 'Unknown',
      'signature_algorithm' => $cert_info['signatureTypeSN'] ?? 'Unknown',
      'extensions' => $cert_info['extensions'] ?? array()
    );
  }

  /**
   * Generate SSL certificate using Let's Encrypt
   *
   * @param string $domain Domain name
   * @param string $email Contact email
   * @return bool|WP_Error Success or error
   * @since 1.0.0
   */
  private function generate_ssl_certificate($domain, $email) {
    // Check if certbot is available
    if (!$this->command_exists('certbot')) {
      return new WP_Error('certbot_not_found', __('Certbot is not installed. Please install certbot first.', BAUM_MAIL_TEXT_DOMAIN));
    }

    // Generate certificate using certbot
    $command = sprintf(
      'certbot certonly --standalone --non-interactive --agree-tos --email %s -d %s -d mail.%s 2>&1',
      escapeshellarg($email),
      escapeshellarg($domain),
      escapeshellarg($domain)
    );

    $output = shell_exec($command);

    if (strpos($output, 'Successfully received certificate') === false) {
      return new WP_Error('cert_generation_failed', __('Certificate generation failed: ', BAUM_MAIL_TEXT_DOMAIN) . $output);
    }

    // Update Postfix and Dovecot configurations with new certificate paths
    $cert_path = "/etc/letsencrypt/live/{$domain}/fullchain.pem";
    $key_path = "/etc/letsencrypt/live/{$domain}/privkey.pem";

    $this->update_ssl_configuration($cert_path, $key_path);

    return true;
  }

  /**
   * Update SSL configuration in mail services
   *
   * @param string $cert_path Certificate path
   * @param string $key_path Private key path
   * @return bool Success
   * @since 1.0.0
   */
  private function update_ssl_configuration($cert_path, $key_path) {
    // Update WordPress options
    update_option('baum_mail_ssl_cert_path', $cert_path);
    update_option('baum_mail_ssl_key_path', $key_path);

    // Update Postfix configuration
    $postfix_commands = array(
      "postconf -e 'smtpd_tls_cert_file={$cert_path}'",
      "postconf -e 'smtpd_tls_key_file={$key_path}'",
      "postconf -e 'smtp_tls_cert_file={$cert_path}'",
      "postconf -e 'smtp_tls_key_file={$key_path}'"
    );

    foreach ($postfix_commands as $command) {
      shell_exec($command);
    }

    // Update Dovecot configuration
    $dovecot_ssl_conf = "/etc/dovecot/conf.d/10-ssl.conf";
    $ssl_config = "
# SSL Configuration - Updated by Baum Mail
ssl = required
ssl_cert = <{$cert_path}
ssl_key = <{$key_path}
ssl_protocols = !SSLv3 !TLSv1 !TLSv1.1
ssl_cipher_list = ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-AES256-GCM-SHA384
ssl_prefer_server_ciphers = yes
";

    file_put_contents($dovecot_ssl_conf, $ssl_config);

    // Restart services
    $this->restart_service('postfix');
    $this->restart_service('dovecot');

    return true;
  }

  /**
   * Setup SSL certificate auto-renewal
   *
   * @return bool|WP_Error Success or error
   * @since 1.0.0
   */
  private function setup_ssl_auto_renewal() {
    // Create renewal script
    $renewal_script = BAUM_MAIL_PLUGIN_DIR . 'scripts/ssl-renewal.sh';
    $script_content = '#!/bin/bash
# Baum Mail SSL Certificate Auto-Renewal Script

# Renew certificates
certbot renew --quiet

# Check if renewal was successful
if [ $? -eq 0 ]; then
    # Restart mail services
    systemctl reload postfix
    systemctl reload dovecot

    # Log success
    echo "$(date): SSL certificates renewed successfully" >> /var/log/baum-mail-ssl.log
else
    # Log failure
    echo "$(date): SSL certificate renewal failed" >> /var/log/baum-mail-ssl.log
fi
';

    if (!file_put_contents($renewal_script, $script_content)) {
      return new WP_Error('script_creation_failed', __('Failed to create renewal script.', BAUM_MAIL_TEXT_DOMAIN));
    }

    chmod($renewal_script, 0755);

    // Add to crontab for automatic renewal (twice daily)
    $cron_entry = "0 */12 * * * {$renewal_script}";
    $current_crontab = shell_exec('crontab -l 2>/dev/null');

    if (strpos($current_crontab, $renewal_script) === false) {
      $new_crontab = $current_crontab . "\n" . $cron_entry . "\n";
      $temp_file = tempnam(sys_get_temp_dir(), 'crontab_');
      file_put_contents($temp_file, $new_crontab);
      shell_exec("crontab {$temp_file}");
      unlink($temp_file);
    }

    return true;
  }

  /**
   * AJAX: Generate SSL certificate
   *
   * @since 1.0.0
   */
  public function ajax_generate_ssl_certificate() {
    check_ajax_referer('baum_mail_admin_nonce', 'nonce');

    if (!current_user_can('manage_options')) {
      wp_die(__('Insufficient permissions.', BAUM_MAIL_TEXT_DOMAIN));
    }

    $domain = sanitize_text_field($_POST['domain']);
    $email = sanitize_email($_POST['email']);

    $result = $this->generate_ssl_certificate($domain, $email);

    if (is_wp_error($result)) {
      wp_send_json_error($result->get_error_message());
    } else {
      // Setup auto-renewal
      $this->setup_ssl_auto_renewal();
      wp_send_json_success(__('SSL certificate generated successfully and auto-renewal configured.', BAUM_MAIL_TEXT_DOMAIN));
    }
  }

  /**
   * Generate DKIM keys for domain
   *
   * @param string $domain Domain name
   * @return array|WP_Error DKIM keys or error
   * @since 1.0.0
   */
  private function generate_dkim_keys($domain) {
    // Check for available DKIM tools
    $dkim_tool = null;
    if ($this->command_exists('opendkim-genkey')) {
      $dkim_tool = 'opendkim-genkey';
    } elseif ($this->command_exists('openssl')) {
      $dkim_tool = 'openssl';
    } else {
      return new WP_Error('dkim_tools_not_found', __('DKIM tools not found. Please install opendkim or openssl.', BAUM_MAIL_TEXT_DOMAIN));
    }

    // Create temporary directory for key generation
    $temp_dir = sys_get_temp_dir() . '/dkim_' . uniqid();
    if (!mkdir($temp_dir, 0700)) {
      return new WP_Error('temp_dir_failed', __('Failed to create temporary directory.', BAUM_MAIL_TEXT_DOMAIN));
    }

    $private_key = '';
    $public_key = '';
    $output = '';

    if ($dkim_tool === 'opendkim-genkey') {
      // Use opendkim-genkey
      $command = sprintf(
        'cd %s && opendkim-genkey -t -s default -d %s 2>&1',
        escapeshellarg($temp_dir),
        escapeshellarg($domain)
      );

      $output = shell_exec($command);

      // Read generated files
      $private_key_file = $temp_dir . '/default.private';
      $public_key_file = $temp_dir . '/default.txt';

      if (file_exists($private_key_file) && file_exists($public_key_file)) {
        $private_key = file_get_contents($private_key_file);
        $public_key_raw = file_get_contents($public_key_file);

        // Parse public key from the generated file
        if (preg_match('/p=([^"]+)/', $public_key_raw, $matches)) {
          $public_key = $matches[1];
        }
      }
    } else {
      // Use openssl as fallback
      $private_key_file = $temp_dir . '/dkim.private';
      $public_key_file = $temp_dir . '/dkim.public';

      // Generate private key
      $private_cmd = sprintf(
        'openssl genrsa -out %s 2048 2>&1',
        escapeshellarg($private_key_file)
      );
      $output .= shell_exec($private_cmd);

      // Generate public key
      $public_cmd = sprintf(
        'openssl rsa -in %s -pubout -out %s 2>&1',
        escapeshellarg($private_key_file),
        escapeshellarg($public_key_file)
      );
      $output .= shell_exec($public_cmd);

      if (file_exists($private_key_file) && file_exists($public_key_file)) {
        $private_key = file_get_contents($private_key_file);
        $public_key_pem = file_get_contents($public_key_file);

        // Convert PEM to DKIM format
        $public_key_clean = str_replace(array('-----BEGIN PUBLIC KEY-----', '-----END PUBLIC KEY-----', "\n", "\r"), '', $public_key_pem);
        $public_key = trim($public_key_clean);
      }
    }

    // Check if keys were generated successfully
    if (empty($private_key) || empty($public_key)) {
      $this->cleanup_directory($temp_dir);
      return new WP_Error('dkim_generation_failed', __('DKIM key generation failed: ', BAUM_MAIL_TEXT_DOMAIN) . $output);
    }

    // Store keys in WordPress options
    update_option("baum_mail_dkim_private_key_{$domain}", $private_key);
    update_option("baum_mail_dkim_public_key_{$domain}", $public_key);

    // Clean up temporary directory
    $this->cleanup_directory($temp_dir);

    return array(
      'private_key' => $private_key,
      'public_key' => $public_key,
      'dns_record' => "default._domainkey.{$domain} IN TXT \"v=DKIM1; k=rsa; p={$public_key}\"",
      'tool_used' => $dkim_tool
    );
  }

  /**
   * Clean up directory recursively
   *
   * @param string $dir Directory path
   * @since 1.0.0
   */
  private function cleanup_directory($dir) {
    if (is_dir($dir)) {
      $files = array_diff(scandir($dir), array('.', '..'));
      foreach ($files as $file) {
        $path = $dir . '/' . $file;
        is_dir($path) ? $this->cleanup_directory($path) : unlink($path);
      }
      rmdir($dir);
    }
  }

  /**
   * AJAX: Generate DKIM keys
   *
   * @since 1.0.0
   */
  public function ajax_generate_dkim() {
    check_ajax_referer('baum_mail_admin_nonce', 'nonce');

    if (!current_user_can('manage_options')) {
      wp_die(__('Insufficient permissions.', BAUM_MAIL_TEXT_DOMAIN));
    }

    $domain = sanitize_text_field($_POST['domain']);

    if (empty($domain)) {
      wp_send_json_error(__('Domain is required.', BAUM_MAIL_TEXT_DOMAIN));
    }

    $result = $this->generate_dkim_keys($domain);

    if (is_wp_error($result)) {
      wp_send_json_error($result->get_error_message());
    } else {
      wp_send_json_success($result);
    }
  }

  /**
   * Generate GPG key pair for account
   *
   * @param int $account_id Account ID
   * @param string $name Full name
   * @param string $email Email address
   * @param string $passphrase Passphrase
   * @param int $key_size Key size in bits
   * @return array|WP_Error GPG key data or error
   * @since 1.0.0
   */
  private function generate_gpg_key($account_id, $name, $email, $passphrase, $key_size = 3072) {
    // Check if gpg command is available
    if (!$this->command_exists('gpg')) {
      return new WP_Error('gpg_not_found', __('GPG is not installed on this system.', BAUM_MAIL_TEXT_DOMAIN));
    }

    // Create temporary directory for GPG operations
    $temp_dir = sys_get_temp_dir() . '/gpg_' . uniqid();
    mkdir($temp_dir, 0700);

    // Create GPG batch file for key generation
    $batch_content = "
Key-Type: RSA
Key-Length: {$key_size}
Subkey-Type: RSA
Subkey-Length: {$key_size}
Name-Real: {$name}
Name-Email: {$email}
Expire-Date: 0
Passphrase: {$passphrase}
%commit
%echo done
";

    $batch_file = $temp_dir . '/batch.txt';
    file_put_contents($batch_file, $batch_content);

    // Generate GPG key
    $command = sprintf(
      'GNUPGHOME=%s gpg --batch --generate-key %s 2>&1',
      escapeshellarg($temp_dir),
      escapeshellarg($batch_file)
    );

    $output = shell_exec($command);

    // Get the generated key ID
    $list_command = sprintf('GNUPGHOME=%s gpg --list-secret-keys --with-colons', escapeshellarg($temp_dir));
    $list_output = shell_exec($list_command);

    $key_id = null;
    if (preg_match('/^sec:[^:]*:[^:]*:[^:]*:([^:]+):/m', $list_output, $matches)) {
      $key_id = $matches[1];
    }

    if (!$key_id) {
      $this->cleanup_directory($temp_dir);
      return new WP_Error('gpg_generation_failed', __('Failed to generate GPG key: ', BAUM_MAIL_TEXT_DOMAIN) . $output);
    }

    // Export private key
    $private_key_command = sprintf(
      'GNUPGHOME=%s gpg --batch --yes --pinentry-mode loopback --passphrase %s --armor --export-secret-keys %s',
      escapeshellarg($temp_dir),
      escapeshellarg($passphrase),
      escapeshellarg($key_id)
    );
    $private_key = shell_exec($private_key_command);

    // Export public key
    $public_key_command = sprintf(
      'GNUPGHOME=%s gpg --armor --export %s',
      escapeshellarg($temp_dir),
      escapeshellarg($key_id)
    );
    $public_key = shell_exec($public_key_command);

    // Store keys in WordPress options
    update_option("baum_mail_gpg_private_key_{$account_id}", $private_key);
    update_option("baum_mail_gpg_public_key_{$account_id}", $public_key);
    update_option("baum_mail_gpg_key_id_{$account_id}", $key_id);
    update_option("baum_mail_gpg_passphrase_{$account_id}", wp_hash_password($passphrase));

    // Clean up temporary directory
    $this->cleanup_directory($temp_dir);

    return array(
      'key_id' => $key_id,
      'public_key' => $public_key,
      'private_key' => $private_key,
      'created_at' => current_time('mysql')
    );
  }

  /**
   * Import GPG key for account
   *
   * @param int $account_id Account ID
   * @param string $private_key Private key content
   * @return array|WP_Error GPG key data or error
   * @since 1.0.0
   */
  private function import_gpg_key($account_id, $private_key) {
    // Check if gpg command is available
    if (!$this->command_exists('gpg')) {
      return new WP_Error('gpg_not_found', __('GPG is not installed on this system.', BAUM_MAIL_TEXT_DOMAIN));
    }

    // Create temporary directory for GPG operations
    $temp_dir = sys_get_temp_dir() . '/gpg_import_' . uniqid();
    mkdir($temp_dir, 0700);

    // Save private key to temporary file
    $key_file = $temp_dir . '/private.key';
    file_put_contents($key_file, $private_key);

    // Import private key
    $import_command = sprintf(
      'GNUPGHOME=%s gpg --batch --import %s 2>&1',
      escapeshellarg($temp_dir),
      escapeshellarg($key_file)
    );

    $output = shell_exec($import_command);

    // Get the imported key ID
    $list_command = sprintf('GNUPGHOME=%s gpg --list-secret-keys --with-colons', escapeshellarg($temp_dir));
    $list_output = shell_exec($list_command);

    $key_id = null;
    if (preg_match('/^sec:[^:]*:[^:]*:[^:]*:([^:]+):/m', $list_output, $matches)) {
      $key_id = $matches[1];
    }

    if (!$key_id) {
      $this->cleanup_directory($temp_dir);
      return new WP_Error('gpg_import_failed', __('Failed to import GPG key: ', BAUM_MAIL_TEXT_DOMAIN) . $output);
    }

    // Export public key
    $public_key_command = sprintf(
      'GNUPGHOME=%s gpg --armor --export %s',
      escapeshellarg($temp_dir),
      escapeshellarg($key_id)
    );
    $public_key = shell_exec($public_key_command);

    // Store keys in WordPress options
    update_option("baum_mail_gpg_private_key_{$account_id}", $private_key);
    update_option("baum_mail_gpg_public_key_{$account_id}", $public_key);
    update_option("baum_mail_gpg_key_id_{$account_id}", $key_id);

    // Clean up temporary directory
    $this->cleanup_directory($temp_dir);

    return array(
      'key_id' => $key_id,
      'public_key' => $public_key,
      'imported_at' => current_time('mysql')
    );
  }

  /**
   * Format bytes to human readable format
   *
   * @param int $bytes Bytes
   * @return string Formatted string
   * @since 1.0.0
   */
  private function format_bytes($bytes) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');

    for ($i = 0; $bytes > 1024; $i++) {
      $bytes /= 1024;
    }

    return round($bytes, 2) . ' ' . $units[$i];
  }

  /**
   * Get comprehensive domain information
   *
   * @param string $domain Domain name
   * @return array Domain information
   * @since 1.0.0
   */
  private function get_domain_info($domain) {
    $info = array(
      'whois' => $this->get_whois_info($domain),
      'dns_records' => $this->get_dns_records($domain),
      'network_info' => $this->get_network_info($domain),
      'mail_servers' => $this->get_mail_servers($domain)
    );

    return $info;
  }

  /**
   * Get WHOIS information
   *
   * @param string $domain Domain name
   * @return string|false WHOIS data or false on failure
   * @since 1.0.0
   */
  private function get_whois_info($domain) {
    $command = "whois " . escapeshellarg($domain) . " 2>/dev/null";
    $output = shell_exec($command);

    return $output ?: false;
  }

  /**
   * Get DNS records for domain
   *
   * @param string $domain Domain name
   * @return array DNS records
   * @since 1.0.0
   */
  private function get_dns_records($domain) {
    $records = array();

    // Get different types of DNS records
    $record_types = array('A', 'AAAA', 'MX', 'TXT', 'CNAME', 'NS');

    foreach ($record_types as $type) {
      $command = "dig +short {$type} " . escapeshellarg($domain) . " 2>/dev/null";
      $output = trim(shell_exec($command));

      if (!empty($output)) {
        $records[$type] = explode("\n", $output);
      }
    }

    return $records;
  }

  /**
   * Get network information for domain
   *
   * @param string $domain Domain name
   * @return array Network information
   * @since 1.0.0
   */
  private function get_network_info($domain) {
    $info = array();

    // Get IP address
    $ip = gethostbyname($domain);
    if ($ip !== $domain) {
      $info['ip'] = $ip;

      // Get reverse DNS
      $reverse_dns = gethostbyaddr($ip);
      if ($reverse_dns !== $ip) {
        $info['reverse_dns'] = $reverse_dns;
      }

      // Get traceroute (first few hops)
      $traceroute = shell_exec("traceroute -m 5 " . escapeshellarg($domain) . " 2>/dev/null | head -10");
      if (!empty($traceroute)) {
        $info['traceroute'] = $traceroute;
      }

      // Get ping statistics
      $ping = shell_exec("ping -c 4 " . escapeshellarg($domain) . " 2>/dev/null");
      if (!empty($ping)) {
        $info['ping'] = $ping;
      }
    }

    return $info;
  }

  /**
   * Get mail server information
   *
   * @param string $domain Domain name
   * @return array Mail server information
   * @since 1.0.0
   */
  private function get_mail_servers($domain) {
    $info = array();

    // Get MX records with priority
    $mx_command = "dig +short MX " . escapeshellarg($domain) . " 2>/dev/null";
    $mx_output = trim(shell_exec($mx_command));

    if (!empty($mx_output)) {
      $mx_records = explode("\n", $mx_output);
      foreach ($mx_records as $mx_record) {
        if (preg_match('/(\d+)\s+(.+)/', $mx_record, $matches)) {
          $priority = $matches[1];
          $server = rtrim($matches[2], '.');

          // Get IP of mail server
          $server_ip = gethostbyname($server);

          $info['mx_records'][] = array(
            'priority' => $priority,
            'server' => $server,
            'ip' => $server_ip !== $server ? $server_ip : null
          );
        }
      }
    }

    // Test SMTP connection
    $smtp_test = $this->test_smtp_connection($domain);
    if ($smtp_test) {
      $info['smtp_test'] = $smtp_test;
    }

    return $info;
  }

  /**
   * Test SMTP connection to domain
   *
   * @param string $domain Domain name
   * @return array|false SMTP test results or false
   * @since 1.0.0
   */
  private function test_smtp_connection($domain) {
    $mx_records = dns_get_record($domain, DNS_MX);

    if (empty($mx_records)) {
      return false;
    }

    $mx_server = $mx_records[0]['target'];
    $ports = array(25, 587, 465);
    $results = array();

    foreach ($ports as $port) {
      $connection = @fsockopen($mx_server, $port, $errno, $errstr, 5);
      if ($connection) {
        $results[$port] = 'open';
        fclose($connection);
      } else {
        $results[$port] = 'closed';
      }
    }

    return $results;
  }

  /**
   * AJAX: Generate GPG key
   *
   * @since 1.0.0
   */
  public function ajax_generate_gpg_key() {
    check_ajax_referer('baum_mail_admin_nonce', 'nonce');

    if (!current_user_can('manage_options')) {
      wp_die(__('Insufficient permissions.', BAUM_MAIL_TEXT_DOMAIN));
    }

    $account_id = intval($_POST['account_id']);
    $name = sanitize_text_field($_POST['name']);
    $email = sanitize_email($_POST['email']);
    $passphrase = $_POST['passphrase']; // Don't sanitize passphrase
    $key_size = intval($_POST['key_size']);

    if (empty($account_id) || empty($name) || empty($email) || empty($passphrase)) {
      wp_send_json_error(__('All fields are required.', BAUM_MAIL_TEXT_DOMAIN));
    }

    $result = $this->generate_gpg_key($account_id, $name, $email, $passphrase, $key_size);

    if (is_wp_error($result)) {
      wp_send_json_error($result->get_error_message());
    } else {
      wp_send_json_success($result);
    }
  }

  /**
   * AJAX: Import GPG key
   *
   * @since 1.0.0
   */
  public function ajax_import_gpg_key() {
    check_ajax_referer('baum_mail_admin_nonce', 'nonce');

    if (!current_user_can('manage_options')) {
      wp_die(__('Insufficient permissions.', BAUM_MAIL_TEXT_DOMAIN));
    }

    $account_id = intval($_POST['account_id']);
    $private_key = $_POST['private_key']; // Don't sanitize private key

    if (empty($account_id) || empty($private_key)) {
      wp_send_json_error(__('Account ID and private key are required.', BAUM_MAIL_TEXT_DOMAIN));
    }

    $result = $this->import_gpg_key($account_id, $private_key);

    if (is_wp_error($result)) {
      wp_send_json_error($result->get_error_message());
    } else {
      wp_send_json_success($result);
    }
  }

  /**
   * AJAX: Export GPG public key
   *
   * @since 1.0.0
   */
  public function ajax_export_gpg_key() {
    check_ajax_referer('baum_mail_admin_nonce', 'nonce');

    if (!current_user_can('manage_options')) {
      wp_die(__('Insufficient permissions.', BAUM_MAIL_TEXT_DOMAIN));
    }

    $account_id = intval($_POST['account_id']);

    if (empty($account_id)) {
      wp_send_json_error(__('Account ID is required.', BAUM_MAIL_TEXT_DOMAIN));
    }

    $public_key = get_option("baum_mail_gpg_public_key_{$account_id}");

    if (empty($public_key)) {
      wp_send_json_error(__('No GPG key found for this account.', BAUM_MAIL_TEXT_DOMAIN));
    }

    wp_send_json_success(array('public_key' => $public_key));
  }

  /**
   * AJAX: Delete GPG key
   *
   * @since 1.0.0
   */
  public function ajax_delete_gpg_key() {
    check_ajax_referer('baum_mail_admin_nonce', 'nonce');

    if (!current_user_can('manage_options')) {
      wp_die(__('Insufficient permissions.', BAUM_MAIL_TEXT_DOMAIN));
    }

    $account_id = intval($_POST['account_id']);

    if (empty($account_id)) {
      wp_send_json_error(__('Account ID is required.', BAUM_MAIL_TEXT_DOMAIN));
    }

    // Delete GPG keys from options
    delete_option("baum_mail_gpg_private_key_{$account_id}");
    delete_option("baum_mail_gpg_public_key_{$account_id}");
    delete_option("baum_mail_gpg_key_id_{$account_id}");
    delete_option("baum_mail_gpg_passphrase_{$account_id}");

    wp_send_json_success(__('GPG key deleted successfully.', BAUM_MAIL_TEXT_DOMAIN));
  }

  /**
   * AJAX: Get GPG key info
   *
   * @since 1.0.0
   */
  public function ajax_get_gpg_key_info() {
    check_ajax_referer('baum_mail_admin_nonce', 'nonce');

    if (!current_user_can('manage_options')) {
      wp_die(__('Insufficient permissions.', BAUM_MAIL_TEXT_DOMAIN));
    }

    $account_id = intval($_POST['account_id']);

    if (empty($account_id)) {
      wp_send_json_error(__('Account ID is required.', BAUM_MAIL_TEXT_DOMAIN));
    }

    $key_id = get_option("baum_mail_gpg_key_id_{$account_id}");
    $public_key = get_option("baum_mail_gpg_public_key_{$account_id}");

    if (empty($key_id)) {
      wp_send_json_success(array('has_key' => false));
    } else {
      wp_send_json_success(array(
        'has_key' => true,
        'key_id' => $key_id,
        'public_key' => $public_key
      ));
    }
  }

  /**
   * AJAX: Get analytics data
   *
   * @since 1.0.0
   */
  public function ajax_get_analytics_data() {
    check_ajax_referer('baum_mail_admin_nonce', 'nonce');

    if (!current_user_can('manage_options')) {
      wp_die(__('Insufficient permissions.', BAUM_MAIL_TEXT_DOMAIN));
    }

    $date_from = sanitize_text_field($_POST['date_from']);
    $date_to = sanitize_text_field($_POST['date_to']);

    if (empty($date_from) || empty($date_to)) {
      wp_send_json_error(__('Date range is required.', BAUM_MAIL_TEXT_DOMAIN));
    }

    // Get analytics component
    $analytics = baum_mail()->get_component('analytics');

    if (!$analytics) {
      wp_send_json_error(__('Analytics component not available.', BAUM_MAIL_TEXT_DOMAIN));
    }

    $data = $analytics->get_analytics_data(array(
      'date_from' => $date_from,
      'date_to' => $date_to
    ));

    wp_send_json_success($data);
  }



  /**
   * Single account admin page
   *
   * @since 1.0.0
   */
  public function admin_page_single_account() {
    $account_id = intval($_GET['id'] ?? 0);
    if (!$account_id) {
      wp_die(__('Invalid account ID.', BAUM_MAIL_TEXT_DOMAIN));
    }

    $core = baum_mail()->get_component('core');
    $account = $core->get_account($account_id);

    if (!$account) {
      wp_die(__('Account not found.', BAUM_MAIL_TEXT_DOMAIN));
    }

    // Get account statistics
    $account_stats = $core->get_account_statistics($account_id);

    // Get analytics data for this account
    $analytics = baum_mail()->get_component('analytics');
    $tracking_data = $analytics->get_analytics_data(array(
      'email' => $account->email,
      'date_from' => date('Y-m-d', strtotime('-30 days')),
      'date_to' => date('Y-m-d')
    ));
    ?>
    <div class="wrap">
      <h1><?php echo esc_html(sprintf(__('Account: %s', BAUM_MAIL_TEXT_DOMAIN), $account->email)); ?></h1>

      <div class="nav-tab-wrapper">
        <a href="#overview" class="nav-tab nav-tab-active"><?php echo esc_html__('Overview', BAUM_MAIL_TEXT_DOMAIN); ?></a>
        <a href="#imap-setup" class="nav-tab"><?php echo esc_html__('IMAP Setup', BAUM_MAIL_TEXT_DOMAIN); ?></a>
        <a href="#gpg-setup" class="nav-tab"><?php echo esc_html__('GPG Setup', BAUM_MAIL_TEXT_DOMAIN); ?></a>
        <a href="#analytics" class="nav-tab"><?php echo esc_html__('Analytics', BAUM_MAIL_TEXT_DOMAIN); ?></a>
        <a href="#settings" class="nav-tab"><?php echo esc_html__('Settings', BAUM_MAIL_TEXT_DOMAIN); ?></a>
      </div>

      <!-- Overview Tab -->
      <div id="overview" class="tab-content">
        <div class="account-stats-grid">
          <div class="stat-card">
            <h3><?php echo esc_html__('Quota Used', BAUM_MAIL_TEXT_DOMAIN); ?></h3>
            <span class="stat-number"><?php echo esc_html($this->format_bytes($account_stats['quota_used'] ?? 0)); ?></span>
            <small><?php echo esc_html('of ' . ($account->quota ? $this->format_bytes($account->quota) : 'Unlimited')); ?></small>
          </div>
          <div class="stat-card">
            <h3><?php echo esc_html__('Emails Sent (30d)', BAUM_MAIL_TEXT_DOMAIN); ?></h3>
            <span class="stat-number"><?php echo esc_html($account_stats['emails_sent'] ?? 0); ?></span>
          </div>
          <div class="stat-card">
            <h3><?php echo esc_html__('Emails Received (30d)', BAUM_MAIL_TEXT_DOMAIN); ?></h3>
            <span class="stat-number"><?php echo esc_html($account_stats['emails_received'] ?? 0); ?></span>
          </div>
          <div class="stat-card">
            <h3><?php echo esc_html__('Virus Scans', BAUM_MAIL_TEXT_DOMAIN); ?></h3>
            <span class="stat-number"><?php echo esc_html($account_stats['virus_scans'] ?? 0); ?></span>
            <small><?php echo esc_html(($account_stats['viruses_found'] ?? 0) . ' viruses found'); ?></small>
          </div>
          <div class="stat-card">
            <h3><?php echo esc_html__('Attachments', BAUM_MAIL_TEXT_DOMAIN); ?></h3>
            <span class="stat-number"><?php echo esc_html($account_stats['attachments'] ?? 0); ?></span>
          </div>
          <div class="stat-card">
            <h3><?php echo esc_html__('Last Login', BAUM_MAIL_TEXT_DOMAIN); ?></h3>
            <span class="stat-number"><?php echo esc_html($account->last_login ? date('M j', strtotime($account->last_login)) : 'Never'); ?></span>
            <small><?php echo esc_html($account->last_login ? date('Y H:i', strtotime($account->last_login)) : ''); ?></small>
          </div>
        </div>
      </div>

      <!-- IMAP Setup Tab -->
      <div id="imap-setup" class="tab-content" style="display: none;">
        <h2><?php echo esc_html__('IMAP/SMTP Configuration', BAUM_MAIL_TEXT_DOMAIN); ?></h2>
        <p><?php echo esc_html__('Use these settings to configure your email client:', BAUM_MAIL_TEXT_DOMAIN); ?></p>

        <div class="setup-instructions">
          <h3><?php echo esc_html__('Incoming Mail (IMAP)', BAUM_MAIL_TEXT_DOMAIN); ?></h3>
          <div class="config-table">
            <div class="config-row">
              <span class="config-label"><?php echo esc_html__('Server:', BAUM_MAIL_TEXT_DOMAIN); ?></span>
              <span class="config-value">mail.<?php echo esc_html($account->domain); ?></span>
            </div>
            <div class="config-row">
              <span class="config-label"><?php echo esc_html__('Port:', BAUM_MAIL_TEXT_DOMAIN); ?></span>
              <span class="config-value">993 (SSL) or 143 (STARTTLS)</span>
            </div>
            <div class="config-row">
              <span class="config-label"><?php echo esc_html__('Security:', BAUM_MAIL_TEXT_DOMAIN); ?></span>
              <span class="config-value">SSL/TLS</span>
            </div>
            <div class="config-row">
              <span class="config-label"><?php echo esc_html__('Username:', BAUM_MAIL_TEXT_DOMAIN); ?></span>
              <span class="config-value"><?php echo esc_html($account->email); ?></span>
            </div>
            <div class="config-row">
              <span class="config-label"><?php echo esc_html__('Password:', BAUM_MAIL_TEXT_DOMAIN); ?></span>
              <span class="config-value">[Your account password]</span>
            </div>
          </div>

          <h3><?php echo esc_html__('Outgoing Mail (SMTP)', BAUM_MAIL_TEXT_DOMAIN); ?></h3>
          <div class="config-table">
            <div class="config-row">
              <span class="config-label"><?php echo esc_html__('Server:', BAUM_MAIL_TEXT_DOMAIN); ?></span>
              <span class="config-value">mail.<?php echo esc_html($account->domain); ?></span>
            </div>
            <div class="config-row">
              <span class="config-label"><?php echo esc_html__('Port:', BAUM_MAIL_TEXT_DOMAIN); ?></span>
              <span class="config-value">587 (STARTTLS) or 465 (SSL)</span>
            </div>
            <div class="config-row">
              <span class="config-label"><?php echo esc_html__('Security:', BAUM_MAIL_TEXT_DOMAIN); ?></span>
              <span class="config-value">STARTTLS or SSL/TLS</span>
            </div>
            <div class="config-row">
              <span class="config-label"><?php echo esc_html__('Authentication:', BAUM_MAIL_TEXT_DOMAIN); ?></span>
              <span class="config-value">Required</span>
            </div>
            <div class="config-row">
              <span class="config-label"><?php echo esc_html__('Username:', BAUM_MAIL_TEXT_DOMAIN); ?></span>
              <span class="config-value"><?php echo esc_html($account->email); ?></span>
            </div>
            <div class="config-row">
              <span class="config-label"><?php echo esc_html__('Password:', BAUM_MAIL_TEXT_DOMAIN); ?></span>
              <span class="config-value">[Your account password]</span>
            </div>
          </div>

          <h3><?php echo esc_html__('Popular Email Clients', BAUM_MAIL_TEXT_DOMAIN); ?></h3>
          <div class="client-guides">
            <div class="client-guide">
              <h4>Thunderbird</h4>
              <ol>
                <li>Go to Account Settings → Account Actions → Add Mail Account</li>
                <li>Enter your name, email address, and password</li>
                <li>Click "Configure manually" and use the settings above</li>
                <li>Test the configuration and save</li>
              </ol>
            </div>
            <div class="client-guide">
              <h4>Apple Mail</h4>
              <ol>
                <li>Go to Mail → Preferences → Accounts</li>
                <li>Click the "+" button to add a new account</li>
                <li>Select "Other Mail Account" and enter your details</li>
                <li>Use the manual configuration with settings above</li>
              </ol>
            </div>
            <div class="client-guide">
              <h4>Outlook</h4>
              <ol>
                <li>Go to File → Add Account</li>
                <li>Select "Manual setup or additional server types"</li>
                <li>Choose "POP or IMAP" and enter the settings above</li>
                <li>Test account settings before finishing</li>
              </ol>
            </div>
          </div>
        </div>
      </div>

      <!-- GPG Setup Tab -->
      <div id="gpg-setup" class="tab-content" style="display: none;">
        <h2><?php echo esc_html__('GPG Encryption Setup', BAUM_MAIL_TEXT_DOMAIN); ?></h2>

        <div class="gpg-key-management">
          <div class="gpg-actions">
            <button class="button button-primary" id="generate-gpg-key" data-account-id="<?php echo esc_attr($account->id); ?>">
              <?php echo esc_html__('Generate New GPG Key', BAUM_MAIL_TEXT_DOMAIN); ?>
            </button>
            <button class="button" id="import-gpg-key" data-account-id="<?php echo esc_attr($account->id); ?>">
              <?php echo esc_html__('Import GPG Key', BAUM_MAIL_TEXT_DOMAIN); ?>
            </button>
            <button class="button" id="export-gpg-key" data-account-id="<?php echo esc_attr($account->id); ?>">
              <?php echo esc_html__('Export Public Key', BAUM_MAIL_TEXT_DOMAIN); ?>
            </button>
            <button class="button" id="delete-gpg-key" data-account-id="<?php echo esc_attr($account->id); ?>" style="display: none;">
              <?php echo esc_html__('Delete GPG Key', BAUM_MAIL_TEXT_DOMAIN); ?>
            </button>
          </div>

          <div id="gpg-key-info" class="gpg-key-info">
            <div class="loading-gpg"><?php echo esc_html__('Loading GPG key information...', BAUM_MAIL_TEXT_DOMAIN); ?></div>
          </div>

          <div id="gpg-import-form" class="gpg-import-form" style="display: none;">
            <h4><?php echo esc_html__('Import GPG Key', BAUM_MAIL_TEXT_DOMAIN); ?></h4>
            <textarea id="gpg-key-input" placeholder="<?php echo esc_attr__('Paste your GPG private key here...', BAUM_MAIL_TEXT_DOMAIN); ?>" rows="10" cols="80"></textarea>
            <div class="gpg-import-actions">
              <button class="button button-primary" id="import-gpg-submit"><?php echo esc_html__('Import Key', BAUM_MAIL_TEXT_DOMAIN); ?></button>
              <button class="button" id="import-gpg-cancel"><?php echo esc_html__('Cancel', BAUM_MAIL_TEXT_DOMAIN); ?></button>
            </div>
          </div>

          <div id="gpg-generate-form" class="gpg-generate-form" style="display: none;">
            <h4><?php echo esc_html__('Generate New GPG Key', BAUM_MAIL_TEXT_DOMAIN); ?></h4>
            <table class="form-table">
              <tr>
                <th scope="row"><?php echo esc_html__('Full Name', BAUM_MAIL_TEXT_DOMAIN); ?></th>
                <td><input type="text" id="gpg-name" class="regular-text" placeholder="<?php echo esc_attr__('Your full name', BAUM_MAIL_TEXT_DOMAIN); ?>" /></td>
              </tr>
              <tr>
                <th scope="row"><?php echo esc_html__('Email Address', BAUM_MAIL_TEXT_DOMAIN); ?></th>
                <td><input type="email" id="gpg-email" class="regular-text" value="<?php echo esc_attr($account->email); ?>" readonly /></td>
              </tr>
              <tr>
                <th scope="row"><?php echo esc_html__('Passphrase', BAUM_MAIL_TEXT_DOMAIN); ?></th>
                <td><input type="password" id="gpg-passphrase" class="regular-text" placeholder="<?php echo esc_attr__('Enter a strong passphrase', BAUM_MAIL_TEXT_DOMAIN); ?>" /></td>
              </tr>
              <tr>
                <th scope="row"><?php echo esc_html__('Key Size', BAUM_MAIL_TEXT_DOMAIN); ?></th>
                <td>
                  <select id="gpg-key-size">
                    <option value="2048">2048 bits</option>
                    <option value="3072" selected>3072 bits</option>
                    <option value="4096">4096 bits</option>
                  </select>
                </td>
              </tr>
            </table>
            <div class="gpg-generate-actions">
              <button class="button button-primary" id="generate-gpg-submit"><?php echo esc_html__('Generate Key', BAUM_MAIL_TEXT_DOMAIN); ?></button>
              <button class="button" id="generate-gpg-cancel"><?php echo esc_html__('Cancel', BAUM_MAIL_TEXT_DOMAIN); ?></button>
            </div>
          </div>
        </div>

        <div class="gpg-setup-instructions">
          <h3><?php echo esc_html__('Setup Instructions', BAUM_MAIL_TEXT_DOMAIN); ?></h3>
          <ol>
            <li><?php echo esc_html__('Generate or import a GPG key pair for this email account', BAUM_MAIL_TEXT_DOMAIN); ?></li>
            <li><?php echo esc_html__('Configure your email client to use GPG encryption (Thunderbird with Enigmail, etc.)', BAUM_MAIL_TEXT_DOMAIN); ?></li>
            <li><?php echo esc_html__('Share your public key with contacts who want to send you encrypted emails', BAUM_MAIL_TEXT_DOMAIN); ?></li>
            <li><?php echo esc_html__('Import public keys from contacts to send them encrypted emails', BAUM_MAIL_TEXT_DOMAIN); ?></li>
          </ol>
        </div>
      </div>

      <!-- Analytics Tab -->
      <div id="analytics" class="tab-content" style="display: none;">
        <h2><?php echo esc_html__('Email Analytics', BAUM_MAIL_TEXT_DOMAIN); ?></h2>

        <div class="analytics-summary">
          <div class="stat-card">
            <h3><?php echo esc_html__('Emails Sent', BAUM_MAIL_TEXT_DOMAIN); ?></h3>
            <span class="stat-number"><?php echo esc_html($tracking_data['stats']['total_emails_sent'] ?? 0); ?></span>
          </div>
          <div class="stat-card">
            <h3><?php echo esc_html__('Total Opens', BAUM_MAIL_TEXT_DOMAIN); ?></h3>
            <span class="stat-number"><?php echo esc_html($tracking_data['stats']['total_opens'] ?? 0); ?></span>
          </div>
          <div class="stat-card">
            <h3><?php echo esc_html__('Unique Opens', BAUM_MAIL_TEXT_DOMAIN); ?></h3>
            <span class="stat-number"><?php echo esc_html($tracking_data['stats']['unique_opens'] ?? 0); ?></span>
          </div>
          <div class="stat-card">
            <h3><?php echo esc_html__('Open Rate', BAUM_MAIL_TEXT_DOMAIN); ?></h3>
            <span class="stat-number"><?php echo esc_html(($tracking_data['stats']['open_rate'] ?? 0) . '%'); ?></span>
          </div>
        </div>

        <div class="analytics-map-section">
          <h3><?php echo esc_html__('Geographic Email Opens', BAUM_MAIL_TEXT_DOMAIN); ?></h3>
          <div class="map-controls">
            <select id="analytics-date-range">
              <option value="7"><?php echo esc_html__('Last 7 days', BAUM_MAIL_TEXT_DOMAIN); ?></option>
              <option value="30" selected><?php echo esc_html__('Last 30 days', BAUM_MAIL_TEXT_DOMAIN); ?></option>
              <option value="90"><?php echo esc_html__('Last 90 days', BAUM_MAIL_TEXT_DOMAIN); ?></option>
              <option value="365"><?php echo esc_html__('Last year', BAUM_MAIL_TEXT_DOMAIN); ?></option>
            </select>
            <button class="button" id="refresh-map-btn"><?php echo esc_html__('Refresh Map', BAUM_MAIL_TEXT_DOMAIN); ?></button>
            <button class="button" id="fullscreen-map-btn"><?php echo esc_html__('Fullscreen', BAUM_MAIL_TEXT_DOMAIN); ?></button>
          </div>

          <?php
          // Render map using shortcode
          echo do_shortcode('[baum_mail_map height="400px" account_id="' . esc_attr($account->id) . '" date_from="' . date('Y-m-d', strtotime('-30 days')) . '" date_to="' . date('Y-m-d') . '"]');
          ?>

          <div class="map-legend">
            <h4><?php echo esc_html__('Legend', BAUM_MAIL_TEXT_DOMAIN); ?></h4>
            <div class="legend-item">
              <div class="legend-color high"></div>
              <span><?php echo esc_html__('High activity (10+ opens)', BAUM_MAIL_TEXT_DOMAIN); ?></span>
            </div>
            <div class="legend-item">
              <div class="legend-color medium"></div>
              <span><?php echo esc_html__('Medium activity (5-9 opens)', BAUM_MAIL_TEXT_DOMAIN); ?></span>
            </div>
            <div class="legend-item">
              <div class="legend-color low"></div>
              <span><?php echo esc_html__('Low activity (1-4 opens)', BAUM_MAIL_TEXT_DOMAIN); ?></span>
            </div>
          </div>
        </div>

        <?php if (!empty($tracking_data['opens'])): ?>
          <div class="recent-opens-section">
            <h3><?php echo esc_html__('Recent Opens', BAUM_MAIL_TEXT_DOMAIN); ?></h3>
            <table class="wp-list-table widefat fixed striped">
              <thead>
                <tr>
                  <th><?php echo esc_html__('Subject', BAUM_MAIL_TEXT_DOMAIN); ?></th>
                  <th><?php echo esc_html__('Location', BAUM_MAIL_TEXT_DOMAIN); ?></th>
                  <th><?php echo esc_html__('IP Address', BAUM_MAIL_TEXT_DOMAIN); ?></th>
                  <th><?php echo esc_html__('Opened At', BAUM_MAIL_TEXT_DOMAIN); ?></th>
                </tr>
              </thead>
              <tbody>
                <?php foreach (array_slice($tracking_data['opens'], 0, 20) as $open): ?>
                  <tr>
                    <td><?php echo esc_html($open->subject ?: 'Unknown'); ?></td>
                    <td><?php echo esc_html(implode(', ', array_filter([$open->city, $open->region, $open->country]))); ?></td>
                    <td><?php echo esc_html($open->ip_address); ?></td>
                    <td><?php echo esc_html(date('Y-m-d H:i', strtotime($open->opened_at))); ?></td>
                  </tr>
                <?php endforeach; ?>
              </tbody>
            </table>
          </div>
        <?php endif; ?>

        <style>
        .analytics-summary {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 15px;
          margin-bottom: 30px;
        }

        .stat-card {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          padding: 20px;
          border-radius: 12px;
          text-align: center;
          box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
          transition: transform 0.2s ease;
        }

        .stat-card:hover {
          transform: translateY(-2px);
        }

        .stat-card h3 {
          font-size: 1.1em;
          margin: 0 0 10px 0;
          font-weight: 600;
          opacity: 0.9;
        }

        .stat-card .stat-number {
          font-size: 2.2em;
          font-weight: 300;
          display: block;
          margin-bottom: 5px;
        }

        .analytics-map-section {
          background: white;
          padding: 25px;
          border-radius: 12px;
          box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
          border: 1px solid #e1e5e9;
          margin-bottom: 30px;
        }

        .analytics-map-section h3 {
          margin-top: 0;
          color: #2c3e50;
          margin-bottom: 20px;
        }

        .recent-opens-section {
          background: white;
          padding: 25px;
          border-radius: 12px;
          box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
          border: 1px solid #e1e5e9;
        }

        .recent-opens-section h3 {
          margin-top: 0;
          color: #2c3e50;
          margin-bottom: 20px;
        }

        @media (max-width: 768px) {
          .analytics-summary {
            grid-template-columns: 1fr;
          }
        }
        </style>
      </div>

      <!-- Settings Tab -->
      <div id="settings" class="tab-content" style="display: none;">
        <h2><?php echo esc_html__('Account Settings', BAUM_MAIL_TEXT_DOMAIN); ?></h2>

        <form id="account-settings-form">
          <input type="hidden" name="account_id" value="<?php echo esc_attr($account->id); ?>" />

          <table class="form-table">
            <tr>
              <th scope="row"><?php echo esc_html__('Quota (MB)', BAUM_MAIL_TEXT_DOMAIN); ?></th>
              <td>
                <input type="number" name="quota" value="<?php echo esc_attr($account->quota ? round($account->quota / 1048576) : 0); ?>" min="0" class="small-text" />
                <p class="description"><?php echo esc_html__('0 = unlimited', BAUM_MAIL_TEXT_DOMAIN); ?></p>
              </td>
            </tr>
            <tr>
              <th scope="row"><?php echo esc_html__('Daily Send Limit', BAUM_MAIL_TEXT_DOMAIN); ?></th>
              <td>
                <input type="number" name="daily_send_limit" value="<?php echo esc_attr($account->daily_send_limit ?? 1000); ?>" min="0" class="small-text" />
              </td>
            </tr>
            <tr>
              <th scope="row"><?php echo esc_html__('Daily Receive Limit', BAUM_MAIL_TEXT_DOMAIN); ?></th>
              <td>
                <input type="number" name="daily_receive_limit" value="<?php echo esc_attr($account->daily_receive_limit ?? 5000); ?>" min="0" class="small-text" />
              </td>
            </tr>
            <tr>
              <th scope="row"><?php echo esc_html__('Reset Password', BAUM_MAIL_TEXT_DOMAIN); ?></th>
              <td>
                <div style="display: flex; gap: 10px; align-items: center;">
                  <input type="password" id="new_password" name="new_password" placeholder="<?php echo esc_attr__('Enter new password', BAUM_MAIL_TEXT_DOMAIN); ?>" class="regular-text" />
                  <button type="button" class="button" id="generate-password-btn">
                    <?php echo esc_html__('Generate', BAUM_MAIL_TEXT_DOMAIN); ?>
                  </button>
                  <button type="button" class="button" id="show-password-btn">
                    <?php echo esc_html__('Show', BAUM_MAIL_TEXT_DOMAIN); ?>
                  </button>
                </div>
                <p class="description"><?php echo esc_html__('Leave blank to keep current password', BAUM_MAIL_TEXT_DOMAIN); ?></p>
                <div id="generated-password-display" style="display: none; margin-top: 10px; padding: 10px; background: #e7f3ff; border: 1px solid #b3d9ff; border-radius: 4px;">
                  <strong><?php echo esc_html__('Generated Password:', BAUM_MAIL_TEXT_DOMAIN); ?></strong>
                  <code id="generated-password-text" style="background: white; padding: 4px 8px; margin-left: 10px; border-radius: 3px;"></code>
                  <button type="button" class="button button-small" id="copy-password-btn" style="margin-left: 10px;">
                    <?php echo esc_html__('Copy', BAUM_MAIL_TEXT_DOMAIN); ?>
                  </button>
                </div>
              </td>
            </tr>
          </table>

          <p class="submit">
            <button type="submit" class="button button-primary"><?php echo esc_html__('Update Settings', BAUM_MAIL_TEXT_DOMAIN); ?></button>
          </p>
        </form>
      </div>
    </div>

    <script>
    jQuery(document).ready(function($) {
      // Tab switching
      $('.nav-tab').click(function(e) {
        e.preventDefault();
        var target = $(this).attr('href');

        $('.nav-tab').removeClass('nav-tab-active');
        $(this).addClass('nav-tab-active');

        $('.tab-content').hide();
        $(target).show();
      });

      // GPG key generation
      $('#generate-gpg-btn, #regenerate-gpg-btn').click(function() {
        var $button = $(this);
        var accountId = $button.data('account-id');
        var originalText = $button.text();

        if (!confirm('Are you sure you want to generate a new GPG key? This will replace any existing key.')) {
          return;
        }

        $button.prop('disabled', true).text('Generating...');

        $.ajax({
          url: ajaxurl,
          type: 'POST',
          data: {
            action: 'baum_mail_generate_gpg_key',
            account_id: accountId,
            nonce: '<?php echo wp_create_nonce('baum_mail_admin_nonce'); ?>'
          },
          success: function(response) {
            if (response.success) {
              alert('GPG key generated successfully.');
              window.location.reload();
            } else {
              alert('Error: ' + response.data);
            }
          },
          error: function() {
            alert('Failed to generate GPG key.');
          },
          complete: function() {
            $button.prop('disabled', false).text(originalText);
          }
        });
      });

      // Account settings form
      $('#account-settings-form').on('submit', function(e) {
        e.preventDefault();

        var $form = $(this);
        var $button = $form.find('button[type="submit"]');
        var originalText = $button.text();

        $button.prop('disabled', true).text('Updating...');

        $.ajax({
          url: ajaxurl,
          type: 'POST',
          data: $form.serialize() + '&action=baum_mail_update_account_settings&nonce=<?php echo wp_create_nonce('baum_mail_admin_nonce'); ?>',
          success: function(response) {
            if (response.success) {
              alert('Settings updated successfully.');
            } else {
              alert('Error: ' + response.data);
            }
          },
          error: function() {
            alert('Failed to update settings.');
          },
          complete: function() {
            $button.prop('disabled', false).text(originalText);
          }
        });
      });

      // Password generation
      $('#generate-password-btn').click(function() {
        var password = generateSecurePassword();
        $('#new_password').val(password);
        $('#generated-password-text').text(password);
        $('#generated-password-display').show();
      });

      // Show/hide password
      $('#show-password-btn').click(function() {
        var $input = $('#new_password');
        var $button = $(this);

        if ($input.attr('type') === 'password') {
          $input.attr('type', 'text');
          $button.text('<?php echo esc_js(__('Hide', BAUM_MAIL_TEXT_DOMAIN)); ?>');
        } else {
          $input.attr('type', 'password');
          $button.text('<?php echo esc_js(__('Show', BAUM_MAIL_TEXT_DOMAIN)); ?>');
        }
      });

      // Copy password to clipboard
      $('#copy-password-btn').click(function() {
        var password = $('#generated-password-text').text();
        navigator.clipboard.writeText(password).then(function() {
          alert('<?php echo esc_js(__('Password copied to clipboard!', BAUM_MAIL_TEXT_DOMAIN)); ?>');
        });
      });

      // Generate DKIM keys
      $('#generate-dkim-btn').click(function() {
        var $button = $(this);
        var domain = $button.data('domain');
        var originalText = $button.text();

        $button.prop('disabled', true).text('<?php echo esc_js(__('Generating...', BAUM_MAIL_TEXT_DOMAIN)); ?>');

        $.ajax({
          url: ajaxurl,
          type: 'POST',
          data: {
            action: 'baum_mail_generate_dkim',
            domain: domain,
            nonce: '<?php echo wp_create_nonce('baum_mail_admin_nonce'); ?>'
          },
          success: function(response) {
            if (response.success) {
              var dkimRecord = response.data.dns_record;
              $('#dkim-record-content').html(
                '<div class="dns-record-content">' +
                '<code>' + dkimRecord + '</code>' +
                '<button class="copy-dns-btn" data-clipboard="' + dkimRecord + '"><?php echo esc_js(__('Copy', BAUM_MAIL_TEXT_DOMAIN)); ?></button>' +
                '</div>'
              );
              alert('<?php echo esc_js(__('DKIM keys generated successfully!', BAUM_MAIL_TEXT_DOMAIN)); ?>');
            } else {
              alert('<?php echo esc_js(__('Error:', BAUM_MAIL_TEXT_DOMAIN)); ?> ' + response.data);
            }
          },
          error: function() {
            alert('<?php echo esc_js(__('Failed to generate DKIM keys.', BAUM_MAIL_TEXT_DOMAIN)); ?>');
          },
          complete: function() {
            $button.prop('disabled', false).text(originalText);
          }
        });
      });

      // Copy DNS records to clipboard
      $(document).on('click', '.copy-dns-btn', function() {
        var text = $(this).data('clipboard');
        navigator.clipboard.writeText(text).then(function() {
          alert('<?php echo esc_js(__('DNS record copied to clipboard!', BAUM_MAIL_TEXT_DOMAIN)); ?>');
        });
      });

      // Generate secure password function
      function generateSecurePassword() {
        var length = 16;
        var charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
        var password = '';

        for (var i = 0; i < length; i++) {
          password += charset.charAt(Math.floor(Math.random() * charset.length));
        }

        return password;
      }

      // GPG Key Management
      function loadGPGKeyInfo() {
        var accountId = $('#generate-gpg-key').data('account-id');

        $.ajax({
          url: ajaxurl,
          type: 'POST',
          data: {
            action: 'baum_mail_get_gpg_key_info',
            account_id: accountId,
            nonce: '<?php echo wp_create_nonce('baum_mail_admin_nonce'); ?>'
          },
          success: function(response) {
            if (response.success) {
              if (response.data.has_key) {
                $('#gpg-key-info').html(
                  '<div class="gpg-key-exists">' +
                  '<h4><?php echo esc_js(__('Current GPG Key', BAUM_MAIL_TEXT_DOMAIN)); ?></h4>' +
                  '<p><strong><?php echo esc_js(__('Key ID:', BAUM_MAIL_TEXT_DOMAIN)); ?></strong> ' + response.data.key_id + '</p>' +
                  '<textarea readonly rows="10" cols="80">' + response.data.public_key + '</textarea>' +
                  '</div>'
                );
                $('#delete-gpg-key').show();
              } else {
                $('#gpg-key-info').html(
                  '<div class="no-gpg-key">' +
                  '<p><?php echo esc_js(__('No GPG key found for this account.', BAUM_MAIL_TEXT_DOMAIN)); ?></p>' +
                  '</div>'
                );
                $('#delete-gpg-key').hide();
              }
            }
          },
          error: function() {
            $('#gpg-key-info').html(
              '<div class="gpg-error">' +
              '<p><?php echo esc_js(__('Error loading GPG key information.', BAUM_MAIL_TEXT_DOMAIN)); ?></p>' +
              '</div>'
            );
          }
        });
      }

      // Load GPG key info on page load
      if ($('#gpg-key-info').length) {
        loadGPGKeyInfo();
      }

      // Generate GPG key
      $('#generate-gpg-key').click(function() {
        $('#gpg-generate-form').show();
        $('#gpg-import-form').hide();
      });

      $('#generate-gpg-cancel').click(function() {
        $('#gpg-generate-form').hide();
      });

      $('#generate-gpg-submit').click(function() {
        var accountId = $('#generate-gpg-key').data('account-id');
        var name = $('#gpg-name').val();
        var email = $('#gpg-email').val();
        var passphrase = $('#gpg-passphrase').val();
        var keySize = $('#gpg-key-size').val();

        if (!name || !passphrase) {
          alert('<?php echo esc_js(__('Name and passphrase are required.', BAUM_MAIL_TEXT_DOMAIN)); ?>');
          return;
        }

        var $button = $(this);
        $button.prop('disabled', true).text('<?php echo esc_js(__('Generating...', BAUM_MAIL_TEXT_DOMAIN)); ?>');

        $.ajax({
          url: ajaxurl,
          type: 'POST',
          data: {
            action: 'baum_mail_generate_gpg_key',
            account_id: accountId,
            name: name,
            email: email,
            passphrase: passphrase,
            key_size: keySize,
            nonce: '<?php echo wp_create_nonce('baum_mail_admin_nonce'); ?>'
          },
          success: function(response) {
            if (response.success) {
              alert('<?php echo esc_js(__('GPG key generated successfully!', BAUM_MAIL_TEXT_DOMAIN)); ?>');
              $('#gpg-generate-form').hide();
              loadGPGKeyInfo();
            } else {
              alert('<?php echo esc_js(__('Error:', BAUM_MAIL_TEXT_DOMAIN)); ?> ' + response.data);
            }
          },
          error: function() {
            alert('<?php echo esc_js(__('Failed to generate GPG key.', BAUM_MAIL_TEXT_DOMAIN)); ?>');
          },
          complete: function() {
            $button.prop('disabled', false).text('<?php echo esc_js(__('Generate Key', BAUM_MAIL_TEXT_DOMAIN)); ?>');
          }
        });
      });

      // Import GPG key
      $('#import-gpg-key').click(function() {
        $('#gpg-import-form').show();
        $('#gpg-generate-form').hide();
      });

      $('#import-gpg-cancel').click(function() {
        $('#gpg-import-form').hide();
      });

      $('#import-gpg-submit').click(function() {
        var accountId = $('#import-gpg-key').data('account-id');
        var privateKey = $('#gpg-key-input').val();

        if (!privateKey) {
          alert('<?php echo esc_js(__('Private key is required.', BAUM_MAIL_TEXT_DOMAIN)); ?>');
          return;
        }

        var $button = $(this);
        $button.prop('disabled', true).text('<?php echo esc_js(__('Importing...', BAUM_MAIL_TEXT_DOMAIN)); ?>');

        $.ajax({
          url: ajaxurl,
          type: 'POST',
          data: {
            action: 'baum_mail_import_gpg_key',
            account_id: accountId,
            private_key: privateKey,
            nonce: '<?php echo wp_create_nonce('baum_mail_admin_nonce'); ?>'
          },
          success: function(response) {
            if (response.success) {
              alert('<?php echo esc_js(__('GPG key imported successfully!', BAUM_MAIL_TEXT_DOMAIN)); ?>');
              $('#gpg-import-form').hide();
              $('#gpg-key-input').val('');
              loadGPGKeyInfo();
            } else {
              alert('<?php echo esc_js(__('Error:', BAUM_MAIL_TEXT_DOMAIN)); ?> ' + response.data);
            }
          },
          error: function() {
            alert('<?php echo esc_js(__('Failed to import GPG key.', BAUM_MAIL_TEXT_DOMAIN)); ?>');
          },
          complete: function() {
            $button.prop('disabled', false).text('<?php echo esc_js(__('Import Key', BAUM_MAIL_TEXT_DOMAIN)); ?>');
          }
        });
      });

      // Export GPG key
      $('#export-gpg-key').click(function() {
        var accountId = $(this).data('account-id');

        $.ajax({
          url: ajaxurl,
          type: 'POST',
          data: {
            action: 'baum_mail_export_gpg_key',
            account_id: accountId,
            nonce: '<?php echo wp_create_nonce('baum_mail_admin_nonce'); ?>'
          },
          success: function(response) {
            if (response.success) {
              // Create download link
              var blob = new Blob([response.data.public_key], {type: 'text/plain'});
              var url = window.URL.createObjectURL(blob);
              var a = document.createElement('a');
              a.href = url;
              a.download = 'public_key.asc';
              document.body.appendChild(a);
              a.click();
              document.body.removeChild(a);
              window.URL.revokeObjectURL(url);
            } else {
              alert('<?php echo esc_js(__('Error:', BAUM_MAIL_TEXT_DOMAIN)); ?> ' + response.data);
            }
          },
          error: function() {
            alert('<?php echo esc_js(__('Failed to export GPG key.', BAUM_MAIL_TEXT_DOMAIN)); ?>');
          }
        });
      });

      // Delete GPG key
      $('#delete-gpg-key').click(function() {
        if (!confirm('<?php echo esc_js(__('Are you sure you want to delete this GPG key?', BAUM_MAIL_TEXT_DOMAIN)); ?>')) {
          return;
        }

        var accountId = $(this).data('account-id');

        $.ajax({
          url: ajaxurl,
          type: 'POST',
          data: {
            action: 'baum_mail_delete_gpg_key',
            account_id: accountId,
            nonce: '<?php echo wp_create_nonce('baum_mail_admin_nonce'); ?>'
          },
          success: function(response) {
            if (response.success) {
              alert('<?php echo esc_js(__('GPG key deleted successfully!', BAUM_MAIL_TEXT_DOMAIN)); ?>');
              loadGPGKeyInfo();
            } else {
              alert('<?php echo esc_js(__('Error:', BAUM_MAIL_TEXT_DOMAIN)); ?> ' + response.data);
            }
          },
          error: function() {
            alert('<?php echo esc_js(__('Failed to delete GPG key.', BAUM_MAIL_TEXT_DOMAIN)); ?>');
          }
        });
      });
    });
    </script>
    <?php
  }

  /**
   * Admin page: Documentation
   *
   * @since 1.0.0
   */
  public function admin_page_documentation() {
    // Read the README file
    $readme_path = BAUM_MAIL_PLUGIN_DIR . 'README.md';
    $readme_content = '';

    if (file_exists($readme_path)) {
      $readme_content = file_get_contents($readme_path);
    } else {
      $readme_content = __('README.md file not found.', BAUM_MAIL_TEXT_DOMAIN);
    }

    // Enqueue highlight.js and markdown parser
    wp_enqueue_script(
      'baum-mail-highlight-js',
      BAUM_MAIL_PLUGIN_URL . 'assets/js/highlight.js',
      array(),
      BAUM_MAIL_VERSION,
      true
    );

    wp_enqueue_script(
      'baum-mail-markdown-parser',
      BAUM_MAIL_PLUGIN_URL . 'assets/js/markdown-parser.js',
      array('baum-mail-highlight-js'),
      BAUM_MAIL_VERSION,
      true
    );

    wp_enqueue_style(
      'baum-mail-dracula-theme',
      BAUM_MAIL_PLUGIN_URL . 'assets/css/dracula.css',
      array(),
      BAUM_MAIL_VERSION
    );
    ?>
    <div class="wrap">
      <h1><?php echo esc_html__('Baum Mail Documentation', BAUM_MAIL_TEXT_DOMAIN); ?></h1>

      <div class="baum-mail-docs">
        <style>
        .baum-mail-docs pre {
          background: #1e1e1e;
          color: #d4d4d4;
          padding: 25px;
          border-radius: 8px;
          overflow-x: auto;
          font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Source Code Pro', 'Menlo', 'Consolas', monospace;
          font-size: 13px;
          line-height: 1.6;
          max-height: 85vh;
          border: 1px solid #333;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
          position: relative;
        }

        .baum-mail-docs pre::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 30px;
          background: linear-gradient(90deg, #ff5f56, #ffbd2e, #27ca3f);
          border-radius: 8px 8px 0 0;
        }

        .baum-mail-docs pre code {
          display: block;
          margin-top: 30px;
        }

        .baum-mail-docs pre code {
          background: none;
          color: inherit;
          padding: 0;
          font-size: inherit;
          white-space: pre;
        }

        .baum-mail-docs {
          margin-top: 20px;
        }

        .docs-header {
          background: #f8f9fa;
          padding: 15px 20px;
          border-left: 4px solid #0073aa;
          margin-bottom: 20px;
          border-radius: 4px;
        }

        .docs-header h2 {
          margin: 0 0 10px 0;
          color: #0073aa;
        }

        .docs-header p {
          margin: 0;
          color: #666;
        }

        .docs-actions {
          margin-bottom: 20px;
        }

        .docs-actions .button {
          margin-right: 10px;
        }

        /* Markdown content styling */
        .markdown-content {
          background: #282a36;
          color: #f8f8f2;
          padding: 25px;
          border-radius: 8px;
          font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Source Code Pro', 'Menlo', 'Consolas', monospace;
          font-size: 14px;
          line-height: 1.6;
          max-height: 85vh;
          overflow-y: auto;
          border: 1px solid #44475a;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
          position: relative;
        }

        .markdown-content h1,
        .markdown-content h2,
        .markdown-content h3,
        .markdown-content h4,
        .markdown-content h5,
        .markdown-content h6 {
          color: #ff79c6;
          margin-top: 1.5em;
          margin-bottom: 0.5em;
          font-weight: bold;
        }

        .markdown-content h1 {
          font-size: 2em;
          border-bottom: 2px solid #44475a;
          padding-bottom: 0.3em;
        }

        .markdown-content h2 {
          font-size: 1.5em;
          border-bottom: 1px solid #44475a;
          padding-bottom: 0.3em;
        }

        .markdown-content h3 {
          font-size: 1.25em;
        }

        .markdown-content p {
          margin-bottom: 1em;
          line-height: 1.6;
        }

        .markdown-content ul,
        .markdown-content ol {
          margin-left: 2em;
          margin-bottom: 1em;
        }

        .markdown-content li {
          margin-bottom: 0.5em;
        }

        .markdown-content a {
          color: #8be9fd;
          text-decoration: none;
        }

        .markdown-content a:hover {
          text-decoration: underline;
        }

        .markdown-content code.inline-code {
          background: #44475a;
          color: #50fa7b;
          padding: 2px 4px;
          border-radius: 3px;
          font-size: 0.9em;
        }

        .markdown-content pre {
          background: #21222c;
          border: 1px solid #44475a;
          border-radius: 6px;
          padding: 1em;
          margin: 1em 0;
          overflow-x: auto;
        }

        .markdown-content pre code {
          background: none;
          color: inherit;
          padding: 0;
          font-size: inherit;
          white-space: pre;
        }

        .markdown-content blockquote {
          border-left: 4px solid #6272a4;
          padding-left: 1em;
          margin: 1em 0;
          color: #6272a4;
          font-style: italic;
        }

        .markdown-content strong {
          color: #ffb86c;
          font-weight: bold;
        }

        .markdown-content em {
          color: #f1fa8c;
          font-style: italic;
        }

        /* Raw content styling */
        .baum-mail-docs pre code {
          background: none;
          color: #d4d4d4;
          padding: 0;
          font-size: inherit;
          white-space: pre-wrap;
          word-wrap: break-word;
        }

        /* Terminal-like appearance */
        .baum-mail-docs pre {
          position: relative;
        }

        .baum-mail-docs pre::after {
          content: 'README.md';
          position: absolute;
          top: 5px;
          left: 50%;
          transform: translateX(-50%);
          color: #666;
          font-size: 11px;
          font-weight: 500;
        }

        @media print {
          .docs-header,
          .docs-actions {
            display: none;
          }

          .baum-mail-docs pre {
            background: white !important;
            color: black !important;
            border: 1px solid #ccc !important;
            box-shadow: none !important;
            max-height: none !important;
          }

          .baum-mail-docs pre::before,
          .baum-mail-docs pre::after {
            display: none !important;
          }

          .baum-mail-docs pre code {
            margin-top: 0 !important;
            color: black !important;
          }
        }
        </style>

        <div class="docs-header">
          <h2><?php echo esc_html__('Professional Mail Server Management Plugin', BAUM_MAIL_TEXT_DOMAIN); ?></h2>
          <p><?php echo esc_html__('Complete documentation for setup, configuration, and API usage.', BAUM_MAIL_TEXT_DOMAIN); ?></p>
        </div>

        <div class="docs-actions">
          <button class="button" onclick="toggleRenderMode()"><?php echo esc_html__('Toggle Raw/Rendered', BAUM_MAIL_TEXT_DOMAIN); ?></button>
          <button class="button" onclick="window.print()"><?php echo esc_html__('Print Documentation', BAUM_MAIL_TEXT_DOMAIN); ?></button>
          <button class="button" onclick="copyToClipboard()"><?php echo esc_html__('Copy to Clipboard', BAUM_MAIL_TEXT_DOMAIN); ?></button>
          <a href="<?php echo esc_url(admin_url('admin.php?page=baum-mail')); ?>" class="button button-primary"><?php echo esc_html__('Back to Overview', BAUM_MAIL_TEXT_DOMAIN); ?></a>
        </div>

        <div id="rendered-content" class="markdown-content"></div>
        <pre id="raw-content" style="display: none;"><code><?php echo esc_html($readme_content); ?></code></pre>

        <script type="text/markdown" id="markdown-source"><?php echo esc_html($readme_content); ?></script>
      </div>

      <script>
      let isRawMode = false;

      // Initialize markdown rendering when page loads
      document.addEventListener('DOMContentLoaded', function() {
        const markdownSource = document.getElementById('markdown-source').textContent;
        MarkdownParser.render('rendered-content', markdownSource);
      });

      function toggleRenderMode() {
        const renderedContent = document.getElementById('rendered-content');
        const rawContent = document.getElementById('raw-content');

        if (isRawMode) {
          renderedContent.style.display = 'block';
          rawContent.style.display = 'none';
          isRawMode = false;
        } else {
          renderedContent.style.display = 'none';
          rawContent.style.display = 'block';
          isRawMode = true;
        }
      }

      function copyToClipboard() {
        const content = document.getElementById('markdown-source').textContent;
        navigator.clipboard.writeText(content).then(function() {
          alert('<?php echo esc_js(__('Documentation copied to clipboard!', BAUM_MAIL_TEXT_DOMAIN)); ?>');
        }, function(err) {
          console.error('Could not copy text: ', err);
          alert('<?php echo esc_js(__('Failed to copy to clipboard.', BAUM_MAIL_TEXT_DOMAIN)); ?>');
        });
      }
      </script>
    </div>
    <?php
  }

  /**
   * Admin page: API Documentation
   *
   * @since 1.0.0
   */
  public function admin_page_api_documentation() {
    // Generate API documentation
    $docs_generator = new BaumMail_Docs_Generator();
    $docs = $docs_generator->generate_documentation();
    $api_markdown = $docs_generator->generate_markdown($docs);

    // Enqueue highlight.js and markdown parser
    wp_enqueue_script(
      'baum-mail-highlight-js',
      BAUM_MAIL_PLUGIN_URL . 'assets/js/highlight.js',
      array(),
      BAUM_MAIL_VERSION,
      true
    );

    wp_enqueue_script(
      'baum-mail-markdown-parser',
      BAUM_MAIL_PLUGIN_URL . 'assets/js/markdown-parser.js',
      array('baum-mail-highlight-js'),
      BAUM_MAIL_VERSION,
      true
    );

    wp_enqueue_style(
      'baum-mail-dracula-theme',
      BAUM_MAIL_PLUGIN_URL . 'assets/css/dracula.css',
      array(),
      BAUM_MAIL_VERSION
    );
    ?>
    <div class="wrap">
      <h1><?php echo esc_html__('Baum Mail API Reference', BAUM_MAIL_TEXT_DOMAIN); ?></h1>

      <div class="baum-mail-docs">
        <style>
        .markdown-content {
          background: var(--color-secondary);
          color: var(--color-white);
          padding: 25px;
          border-radius: 8px;
          font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Source Code Pro', 'Menlo', 'Consolas', monospace;
          font-size: 14px;
          line-height: 1.6;
          max-height: 85vh;
          overflow-y: auto;
          border: 1px solid var(--color-quaternary);
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .markdown-content h1,
        .markdown-content h2,
        .markdown-content h3,
        .markdown-content h4,
        .markdown-content h5,
        .markdown-content h6 {
          color: var(--color-gold);
          margin-top: 1.5em;
          margin-bottom: 0.5em;
          font-weight: bold;
        }

        .markdown-content h1 {
          font-size: 2em;
          border-bottom: 2px solid #44475a;
          padding-bottom: 0.3em;
        }

        .markdown-content h2 {
          font-size: 1.5em;
          border-bottom: 1px solid #44475a;
          padding-bottom: 0.3em;
        }

        .markdown-content h3 {
          font-size: 1.25em;
        }

        .markdown-content p {
          margin-bottom: 1em;
          line-height: 1.6;
        }

        .markdown-content ul,
        .markdown-content ol {
          margin-left: 2em;
          margin-bottom: 1em;
        }

        .markdown-content li {
          margin-bottom: 0.5em;
        }

        .markdown-content a {
          color: #8be9fd;
          text-decoration: none;
        }

        .markdown-content a:hover {
          text-decoration: underline;
        }

        .markdown-content code.inline-code {
          background: var(--color-quaternary);
          color: var(--color-green);
          padding: 2px 4px;
          border-radius: 3px;
          font-size: 0.9em;
        }

        .markdown-content pre {
          background: #21222c;
          border: 1px solid #44475a;
          border-radius: 6px;
          padding: 1em;
          margin: 1em 0;
          overflow-x: auto;
        }

        .markdown-content pre code {
          background: none;
          color: inherit;
          padding: 0;
          font-size: inherit;
          white-space: pre;
        }

        .markdown-content strong {
          color: #ffb86c;
          font-weight: bold;
        }

        .markdown-content em {
          color: #f1fa8c;
          font-style: italic;
        }

        .docs-header {
          background: #f8f9fa;
          padding: 15px 20px;
          border-left: 4px solid #0073aa;
          margin-bottom: 20px;
          border-radius: 4px;
        }

        .docs-header h2 {
          margin: 0 0 10px 0;
          color: #0073aa;
        }

        .docs-header p {
          margin: 0;
          color: #666;
        }

        .docs-actions {
          margin-bottom: 20px;
        }

        .docs-actions .button {
          margin-right: 10px;
        }
        </style>

        <div class="docs-header">
          <h2><?php echo esc_html__('API Reference Documentation', BAUM_MAIL_TEXT_DOMAIN); ?></h2>
          <p><?php echo esc_html__('Complete API documentation generated from PHPDoc and JSDoc comments.', BAUM_MAIL_TEXT_DOMAIN); ?></p>
        </div>

        <div class="docs-actions">
          <button class="button" onclick="window.print()"><?php echo esc_html__('Print Documentation', BAUM_MAIL_TEXT_DOMAIN); ?></button>
          <button class="button" onclick="copyApiDocsToClipboard()"><?php echo esc_html__('Copy to Clipboard', BAUM_MAIL_TEXT_DOMAIN); ?></button>
          <a href="<?php echo esc_url(admin_url('admin.php?page=baum-mail-docs')); ?>" class="button"><?php echo esc_html__('Main Documentation', BAUM_MAIL_TEXT_DOMAIN); ?></a>
          <a href="<?php echo esc_url(admin_url('admin.php?page=baum-mail')); ?>" class="button button-primary"><?php echo esc_html__('Back to Overview', BAUM_MAIL_TEXT_DOMAIN); ?></a>
        </div>

        <div id="api-docs-content" class="markdown-content"></div>

        <script type="text/markdown" id="api-markdown-source"><?php echo esc_html($api_markdown); ?></script>
      </div>

      <script>
      // Initialize markdown rendering when page loads
      document.addEventListener('DOMContentLoaded', function() {
        const markdownSource = document.getElementById('api-markdown-source').textContent;
        MarkdownParser.render('api-docs-content', markdownSource);
      });

      function copyApiDocsToClipboard() {
        const content = document.getElementById('api-markdown-source').textContent;
        navigator.clipboard.writeText(content).then(function() {
          alert('<?php echo esc_js(__('API documentation copied to clipboard!', BAUM_MAIL_TEXT_DOMAIN)); ?>');
        }, function(err) {
          console.error('Could not copy text: ', err);
          alert('<?php echo esc_js(__('Failed to copy to clipboard.', BAUM_MAIL_TEXT_DOMAIN)); ?>');
        });
      }
      </script>
    </div>
    <?php
  }
}

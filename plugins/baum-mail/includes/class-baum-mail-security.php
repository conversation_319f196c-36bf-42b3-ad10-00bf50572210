<?php
/**
 * Baum Mail Security Integration Class
 *
 * @package BaumMail
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
  exit;
}

/**
 * Security features integration (ClamAV, SpamAssassin, Rspamd)
 *
 * @since 1.0.0
 */
class BaumMail_Security {

  /**
   * Constructor
   *
   * @since 1.0.0
   */
  public function __construct() {
    $this->init_hooks();
  }

  /**
   * Initialize hooks
   *
   * @since 1.0.0
   */
  private function init_hooks() {
    add_action('wp_ajax_baum_mail_update_virus_definitions', array($this, 'ajax_update_virus_definitions'));
    add_action('wp_ajax_baum_mail_scan_mailbox', array($this, 'ajax_scan_mailbox'));
    add_action('wp_ajax_baum_mail_update_spam_rules', array($this, 'ajax_update_spam_rules'));
    add_action('wp_ajax_baum_mail_scan_file', array($this, 'ajax_scan_file'));
    add_action('wp_ajax_baum_mail_check_ip_blacklist', array($this, 'ajax_check_ip_blacklist'));

    // Hook into email processing for automatic virus scanning
    add_filter('baum_mail_process_email', array($this, 'scan_email_for_viruses'), 10, 2);
  }

  /**
   * Get server IP address
   *
   * @return string Server IP address
   * @since 1.0.0
   */
  public function get_server_ip() {
    // Try multiple methods to get server IP
    $ip_sources = array(
      'curl -s https://ipinfo.io/ip',
      'curl -s https://api.ipify.org',
      'curl -s https://checkip.amazonaws.com',
      'dig +short myip.opendns.com @resolver1.opendns.com',
      'wget -qO- https://ipinfo.io/ip'
    );

    foreach ($ip_sources as $command) {
      $ip = trim(shell_exec($command . ' 2>/dev/null'));
      if (!empty($ip) && filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
        return $ip;
      }
    }

    // Fallback to server variables
    $server_vars = array('SERVER_ADDR', 'LOCAL_ADDR', 'HTTP_HOST');
    foreach ($server_vars as $var) {
      if (!empty($_SERVER[$var])) {
        $ip = $_SERVER[$var];
        if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
          return $ip;
        }
      }
    }

    return 'Unknown';
  }

  /**
   * Check if IP is blacklisted
   *
   * @param string $ip IP address to check
   * @return array Blacklist check results
   * @since 1.0.0
   */
  public function check_ip_blacklist($ip) {
    $blacklists = array(
      'zen.spamhaus.org' => 'Spamhaus ZEN',
      'bl.spamcop.net' => 'SpamCop',
      'dnsbl.sorbs.net' => 'SORBS',
      'cbl.abuseat.org' => 'Composite Blocking List',
      'pbl.spamhaus.org' => 'Spamhaus PBL',
      'sbl.spamhaus.org' => 'Spamhaus SBL',
      'xbl.spamhaus.org' => 'Spamhaus XBL',
      'ubl.unsubscore.com' => 'Unsubscribe Blacklist',
      'dnsbl.dronebl.org' => 'DroneBL',
      'rbl.efnetrbl.org' => 'EFnet RBL'
    );

    $results = array();
    $reversed_ip = implode('.', array_reverse(explode('.', $ip)));

    foreach ($blacklists as $blacklist => $name) {
      $query = $reversed_ip . '.' . $blacklist;
      $result = gethostbyname($query);

      $is_listed = ($result !== $query && $result !== $reversed_ip . '.' . $blacklist);

      $results[] = array(
        'blacklist' => $name,
        'domain' => $blacklist,
        'listed' => $is_listed,
        'response' => $is_listed ? $result : null
      );
    }

    return $results;
  }

  /**
   * Get security status overview
   *
   * @return array
   * @since 1.0.0
   */
  public function get_security_status() {
    $status = array(
      'server_ip' => $this->get_server_ip(),
      'clamav' => $this->get_clamav_status(),
      'spamassassin' => $this->get_spamassassin_status(),
      'rspamd' => $this->get_rspamd_status(),
      'blacklist' => $this->get_blacklist_status(),
      'ssl' => $this->get_ssl_status()
    );

    return $status;
  }

  /**
   * Get ClamAV status
   *
   * @return array
   * @since 1.0.0
   */
  public function get_clamav_status() {
    $status = array(
      'enabled' => get_option('baum_mail_enable_clamav', true),
      'running' => false,
      'version' => '',
      'database_version' => '',
      'last_update' => '',
      'scanned_today' => 0,
      'threats_found' => 0
    );

    if (!$status['enabled']) {
      return $status;
    }

    // Check if ClamAV is running
    $clamd_status = $this->execute_command('systemctl is-active clamav-daemon');
    $status['running'] = (trim($clamd_status) === 'active');

    // Get ClamAV version
    $version_output = $this->execute_command('clamdscan --version');
    if (preg_match('/ClamAV (\d+\.\d+\.\d+)/', $version_output, $matches)) {
      $status['version'] = $matches[1];
    }

    // Get database version
    $db_output = $this->execute_command('sigtool --info /var/lib/clamav/main.cvd | grep "Version:"');
    if (preg_match('/Version: (\d+)/', $db_output, $matches)) {
      $status['database_version'] = $matches[1];
    }

    // Get last update time
    $update_time = $this->execute_command('stat -c %Y /var/lib/clamav/main.cvd');
    if ($update_time) {
      $status['last_update'] = date('Y-m-d H:i:s', intval(trim($update_time)));
    }

    // Get scan statistics (would typically come from logs)
    $status['scanned_today'] = $this->get_scan_count_today();
    $status['threats_found'] = $this->get_threats_count_today();

    return $status;
  }

  /**
   * Get SpamAssassin status
   *
   * @return array
   * @since 1.0.0
   */
  public function get_spamassassin_status() {
    $status = array(
      'enabled' => get_option('baum_mail_enable_spamassassin', true),
      'running' => false,
      'version' => '',
      'rules_version' => '',
      'processed_today' => 0,
      'spam_caught' => 0,
      'ham_learned' => 0
    );

    if (!$status['enabled']) {
      return $status;
    }

    // Check if SpamAssassin is running
    $spamd_status = $this->execute_command('systemctl is-active spamassassin');
    $status['running'] = (trim($spamd_status) === 'active');

    // Get SpamAssassin version
    $version_output = $this->execute_command('spamassassin --version');
    if (preg_match('/SpamAssassin version (\d+\.\d+\.\d+)/', $version_output, $matches)) {
      $status['version'] = $matches[1];
    }

    // Get rules version
    $rules_output = $this->execute_command('sa-update --checkonly 2>&1');
    if (preg_match('/Update available for channel (\d+)/', $rules_output, $matches)) {
      $status['rules_version'] = $matches[1];
    }

    // Get processing statistics
    $status['processed_today'] = $this->get_spam_processed_today();
    $status['spam_caught'] = $this->get_spam_caught_today();

    return $status;
  }

  /**
   * Get Rspamd status
   *
   * @return array
   * @since 1.0.0
   */
  public function get_rspamd_status() {
    $status = array(
      'enabled' => get_option('baum_mail_enable_rspamd', false),
      'running' => false,
      'version' => '',
      'uptime' => '',
      'processed_today' => 0,
      'actions' => array()
    );

    if (!$status['enabled']) {
      return $status;
    }

    // Check if Rspamd is running
    $rspamd_status = $this->execute_command('systemctl is-active rspamd');
    $status['running'] = (trim($rspamd_status) === 'active');

    // Get Rspamd version and stats via HTTP API
    $stats_json = $this->execute_command('curl -s http://localhost:11334/stat');
    $stats = json_decode($stats_json, true);

    if ($stats) {
      $status['version'] = $stats['version'] ?? '';
      $status['uptime'] = $stats['uptime'] ?? '';
      $status['processed_today'] = $stats['scanned'] ?? 0;
      $status['actions'] = $stats['actions'] ?? array();
    }

    return $status;
  }

  /**
   * Get blacklist status
   *
   * @return array
   * @since 1.0.0
   */
  public function get_blacklist_status() {
    $status = array(
      'enabled' => true,
      'blocked_ips' => 0,
      'blocked_domains' => 0,
      'last_update' => '',
      'sources' => array()
    );

    // Get blocked IPs count
    $ip_count = $this->execute_command('iptables -L INPUT | grep DROP | wc -l');
    $status['blocked_ips'] = intval(trim($ip_count));

    // Get blocked domains from Postfix maps
    $domain_count = $this->execute_command('wc -l < /etc/postfix/blocked_domains 2>/dev/null || echo 0');
    $status['blocked_domains'] = intval(trim($domain_count));

    return $status;
  }

  /**
   * Get SSL status
   *
   * @return array
   * @since 1.0.0
   */
  public function get_ssl_status() {
    $cert_path = get_option('baum_mail_ssl_cert_path', '/etc/ssl/certs') . '/mail.crt';
    $key_path = get_option('baum_mail_ssl_key_path', '/etc/ssl/private') . '/mail.key';

    $status = array(
      'cert_exists' => file_exists($cert_path),
      'key_exists' => file_exists($key_path),
      'cert_valid' => false,
      'expires_at' => '',
      'days_until_expiry' => 0,
      'issuer' => '',
      'subject' => ''
    );

    if ($status['cert_exists']) {
      // Get certificate information
      $cert_info = $this->execute_command("openssl x509 -in {$cert_path} -text -noout");
      
      // Extract expiry date
      if (preg_match('/Not After : (.+)/', $cert_info, $matches)) {
        $expires_at = strtotime(trim($matches[1]));
        $status['expires_at'] = date('Y-m-d H:i:s', $expires_at);
        $status['days_until_expiry'] = ceil(($expires_at - time()) / 86400);
        $status['cert_valid'] = ($expires_at > time());
      }

      // Extract issuer
      if (preg_match('/Issuer: (.+)/', $cert_info, $matches)) {
        $status['issuer'] = trim($matches[1]);
      }

      // Extract subject
      if (preg_match('/Subject: (.+)/', $cert_info, $matches)) {
        $status['subject'] = trim($matches[1]);
      }
    }

    return $status;
  }

  /**
   * Update virus definitions
   *
   * @return bool|WP_Error
   * @since 1.0.0
   */
  public function update_virus_definitions() {
    if (!get_option('baum_mail_enable_clamav', true)) {
      return new WP_Error('clamav_disabled', __('ClamAV is disabled.', BAUM_MAIL_TEXT_DOMAIN));
    }

    $output = $this->execute_command('freshclam 2>&1');
    
    if (strpos($output, 'Database updated') !== false || strpos($output, 'up to date') !== false) {
      return true;
    }

    return new WP_Error('update_failed', __('Failed to update virus definitions.', BAUM_MAIL_TEXT_DOMAIN) . ' ' . $output);
  }

  /**
   * Scan mailbox for viruses
   *
   * @param string $email Email address
   * @return array
   * @since 1.0.0
   */
  public function scan_mailbox($email) {
    if (!get_option('baum_mail_enable_clamav', true)) {
      return new WP_Error('clamav_disabled', __('ClamAV is disabled.', BAUM_MAIL_TEXT_DOMAIN));
    }

    $mailbox_path = '/var/mail/vhosts/' . $email;
    
    if (!is_dir($mailbox_path)) {
      return new WP_Error('mailbox_not_found', __('Mailbox not found.', BAUM_MAIL_TEXT_DOMAIN));
    }

    $output = $this->execute_command("clamdscan --recursive {$mailbox_path}");
    
    $result = array(
      'scanned_files' => 0,
      'infected_files' => 0,
      'threats' => array(),
      'scan_time' => 0
    );

    // Parse scan results
    if (preg_match('/Scanned files: (\d+)/', $output, $matches)) {
      $result['scanned_files'] = intval($matches[1]);
    }

    if (preg_match('/Infected files: (\d+)/', $output, $matches)) {
      $result['infected_files'] = intval($matches[1]);
    }

    if (preg_match('/Time: (\d+\.\d+)/', $output, $matches)) {
      $result['scan_time'] = floatval($matches[1]);
    }

    // Extract threat details
    preg_match_all('/(.+): (.+) FOUND/', $output, $threat_matches, PREG_SET_ORDER);
    foreach ($threat_matches as $match) {
      $result['threats'][] = array(
        'file' => $match[1],
        'threat' => $match[2]
      );
    }

    return $result;
  }

  /**
   * Update spam rules
   *
   * @return bool|WP_Error
   * @since 1.0.0
   */
  public function update_spam_rules() {
    if (!get_option('baum_mail_enable_spamassassin', true)) {
      return new WP_Error('spamassassin_disabled', __('SpamAssassin is disabled.', BAUM_MAIL_TEXT_DOMAIN));
    }

    $output = $this->execute_command('sa-update 2>&1');
    
    if (strpos($output, 'Update successfully completed') !== false || strpos($output, 'no fresh updates') !== false) {
      // Restart SpamAssassin to load new rules
      $this->execute_command('systemctl restart spamassassin');
      return true;
    }

    return new WP_Error('update_failed', __('Failed to update spam rules.', BAUM_MAIL_TEXT_DOMAIN) . ' ' . $output);
  }

  /**
   * Add IP to blacklist
   *
   * @param string $ip IP address
   * @param string $reason Reason for blocking
   * @return bool
   * @since 1.0.0
   */
  public function blacklist_ip($ip, $reason = '') {
    // Validate IP address
    if (!filter_var($ip, FILTER_VALIDATE_IP)) {
      return false;
    }

    // Add to iptables
    $this->execute_command("iptables -I INPUT -s {$ip} -j DROP");
    
    // Save iptables rules
    $this->execute_command('iptables-save > /etc/iptables/rules.v4');
    
    // Log the action
    error_log("Blacklisted IP: {$ip} - Reason: {$reason}");
    
    return true;
  }

  /**
   * Remove IP from blacklist
   *
   * @param string $ip IP address
   * @return bool
   * @since 1.0.0
   */
  public function whitelist_ip($ip) {
    // Validate IP address
    if (!filter_var($ip, FILTER_VALIDATE_IP)) {
      return false;
    }

    // Remove from iptables
    $this->execute_command("iptables -D INPUT -s {$ip} -j DROP");
    
    // Save iptables rules
    $this->execute_command('iptables-save > /etc/iptables/rules.v4');
    
    // Log the action
    error_log("Whitelisted IP: {$ip}");
    
    return true;
  }

  /**
   * Get scan count for today
   *
   * @return int
   * @since 1.0.0
   */
  private function get_scan_count_today() {
    $today = date('Y-m-d');
    $log_output = $this->execute_command("grep '{$today}' /var/log/clamav/clamav.log | grep 'SCAN SUMMARY' | wc -l");
    return intval(trim($log_output));
  }

  /**
   * Get threats count for today
   *
   * @return int
   * @since 1.0.0
   */
  private function get_threats_count_today() {
    $today = date('Y-m-d');
    $log_output = $this->execute_command("grep '{$today}' /var/log/clamav/clamav.log | grep 'FOUND' | wc -l");
    return intval(trim($log_output));
  }

  /**
   * Get spam processed count for today
   *
   * @return int
   * @since 1.0.0
   */
  private function get_spam_processed_today() {
    $today = date('Y-m-d');
    $log_output = $this->execute_command("grep '{$today}' /var/log/mail.log | grep 'spamd' | wc -l");
    return intval(trim($log_output));
  }

  /**
   * Get spam caught count for today
   *
   * @return int
   * @since 1.0.0
   */
  private function get_spam_caught_today() {
    $today = date('Y-m-d');
    $log_output = $this->execute_command("grep '{$today}' /var/log/mail.log | grep 'identified spam' | wc -l");
    return intval(trim($log_output));
  }

  /**
   * Execute system command safely
   *
   * @param string $command Command to execute
   * @return string Command output
   * @since 1.0.0
   */
  private function execute_command($command) {
    // Sanitize command
    $command = escapeshellcmd($command);
    
    // Execute and capture output
    ob_start();
    $return_var = 0;
    $output = shell_exec($command . ' 2>&1');
    ob_end_clean();

    return $output ?: '';
  }

  /**
   * AJAX: Update virus definitions
   *
   * @since 1.0.0
   */
  public function ajax_update_virus_definitions() {
    check_ajax_referer('baum_mail_admin_nonce', 'nonce');

    if (!current_user_can('manage_options')) {
      wp_die(__('Insufficient permissions.', BAUM_MAIL_TEXT_DOMAIN));
    }

    $result = $this->update_virus_definitions();

    if (is_wp_error($result)) {
      wp_send_json_error($result->get_error_message());
    } else {
      wp_send_json_success(__('Virus definitions updated successfully.', BAUM_MAIL_TEXT_DOMAIN));
    }
  }

  /**
   * AJAX: Scan mailbox
   *
   * @since 1.0.0
   */
  public function ajax_scan_mailbox() {
    check_ajax_referer('baum_mail_admin_nonce', 'nonce');

    if (!current_user_can('manage_options')) {
      wp_die(__('Insufficient permissions.', BAUM_MAIL_TEXT_DOMAIN));
    }

    $email = sanitize_email($_POST['email']);
    $result = $this->scan_mailbox($email);

    if (is_wp_error($result)) {
      wp_send_json_error($result->get_error_message());
    } else {
      wp_send_json_success($result);
    }
  }

  /**
   * AJAX: Update spam rules
   *
   * @since 1.0.0
   */
  public function ajax_update_spam_rules() {
    check_ajax_referer('baum_mail_admin_nonce', 'nonce');

    if (!current_user_can('manage_options')) {
      wp_die(__('Insufficient permissions.', BAUM_MAIL_TEXT_DOMAIN));
    }

    $result = $this->update_spam_rules();

    if (is_wp_error($result)) {
      wp_send_json_error($result->get_error_message());
    } else {
      wp_send_json_success(__('Spam rules updated successfully.', BAUM_MAIL_TEXT_DOMAIN));
    }
  }

  /**
   * Scan file for viruses using ClamAV
   *
   * @param string $file_path Path to file to scan
   * @return array|WP_Error Scan results or error
   * @since 1.0.0
   */
  public function scan_file_for_viruses($file_path) {
    if (!file_exists($file_path)) {
      return new WP_Error('file_not_found', __('File not found.', BAUM_MAIL_TEXT_DOMAIN));
    }

    // Check if ClamAV is available
    if (!$this->command_exists('clamscan')) {
      return new WP_Error('clamav_not_available', __('ClamAV is not available.', BAUM_MAIL_TEXT_DOMAIN));
    }

    // Run ClamAV scan
    $command = sprintf('clamscan --no-summary %s 2>&1', escapeshellarg($file_path));
    $output = shell_exec($command);

    $is_infected = strpos($output, 'FOUND') !== false;
    $virus_name = '';

    if ($is_infected && preg_match('/: (.+) FOUND/', $output, $matches)) {
      $virus_name = $matches[1];
    }

    // Log the scan result
    $this->log_virus_scan($file_path, $is_infected, $virus_name);

    return array(
      'file_path' => $file_path,
      'is_infected' => $is_infected,
      'virus_name' => $virus_name,
      'scan_output' => $output,
      'scanned_at' => current_time('mysql')
    );
  }

  /**
   * Scan email for viruses
   *
   * @param string $email_content Email content
   * @param array $email_data Email metadata
   * @return string|WP_Error Processed email content or error
   * @since 1.0.0
   */
  public function scan_email_for_viruses($email_content, $email_data) {
    // Create temporary file for scanning
    $temp_file = tempnam(sys_get_temp_dir(), 'baum_mail_scan_');
    file_put_contents($temp_file, $email_content);

    $scan_result = $this->scan_file_for_viruses($temp_file);

    // Clean up temporary file
    unlink($temp_file);

    if (is_wp_error($scan_result)) {
      return $email_content; // Continue processing if scan fails
    }

    if ($scan_result['is_infected']) {
      // Log virus detection
      $this->log_virus_detection($email_data, $scan_result['virus_name']);

      // Quarantine or reject the email
      return new WP_Error('virus_detected', sprintf(
        __('Virus detected: %s. Email quarantined.', BAUM_MAIL_TEXT_DOMAIN),
        $scan_result['virus_name']
      ));
    }

    return $email_content;
  }

  /**
   * Log virus scan result
   *
   * @param string $file_path File path
   * @param bool $is_infected Whether virus was found
   * @param string $virus_name Virus name if found
   * @since 1.0.0
   */
  private function log_virus_scan($file_path, $is_infected, $virus_name = '') {
    $core = baum_mail()->get_component('core');

    $message = $is_infected ?
      sprintf('Virus detected in %s: %s', $file_path, $virus_name) :
      sprintf('File scanned clean: %s', $file_path);

    $core->log_action('virus_scan', $message, array(
      'file_path' => $file_path,
      'is_infected' => $is_infected,
      'virus_name' => $virus_name
    ));
  }

  /**
   * Log virus detection in email
   *
   * @param array $email_data Email metadata
   * @param string $virus_name Virus name
   * @since 1.0.0
   */
  private function log_virus_detection($email_data, $virus_name) {
    $core = baum_mail()->get_component('core');

    $message = sprintf(
      'Virus detected in email from %s to %s: %s',
      $email_data['from'] ?? 'unknown',
      $email_data['to'] ?? 'unknown',
      $virus_name
    );

    $core->log_action('virus_detected', $message, array(
      'email_data' => $email_data,
      'virus_name' => $virus_name
    ));
  }

  /**
   * Check if command exists
   *
   * @param string $command Command to check
   * @return bool Command exists
   * @since 1.0.0
   */
  private function command_exists($command) {
    $result = shell_exec("which {$command} 2>/dev/null");
    return !empty($result);
  }

  /**
   * AJAX: Scan file for viruses
   *
   * @since 1.0.0
   */
  public function ajax_scan_file() {
    check_ajax_referer('baum_mail_admin_nonce', 'nonce');

    if (!current_user_can('manage_options')) {
      wp_die(__('Insufficient permissions.', BAUM_MAIL_TEXT_DOMAIN));
    }

    $file_path = sanitize_text_field($_POST['file_path']);

    if (empty($file_path)) {
      wp_send_json_error(__('File path is required.', BAUM_MAIL_TEXT_DOMAIN));
    }

    $result = $this->scan_file_for_viruses($file_path);

    if (is_wp_error($result)) {
      wp_send_json_error($result->get_error_message());
    } else {
      wp_send_json_success($result);
    }
  }

  /**
   * AJAX: Check IP blacklist
   *
   * @since 1.0.0
   */
  public function ajax_check_ip_blacklist() {
    check_ajax_referer('baum_mail_admin_nonce', 'nonce');

    if (!current_user_can('manage_options')) {
      wp_die(__('Insufficient permissions.', BAUM_MAIL_TEXT_DOMAIN));
    }

    $ip = sanitize_text_field($_POST['ip']) ?: $this->get_server_ip();

    $results = $this->check_ip_blacklist($ip);

    wp_send_json_success(array(
      'ip' => $ip,
      'results' => $results
    ));
  }
}

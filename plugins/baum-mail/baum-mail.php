<?php
/**
 * Plugin Name: Baum Mail
 * Plugin URI: https://baumpress.com/plugins/baum-mail
 * Description: Comprehensive email management plugin with Dovecot/Postfix integration, CRUD operations, security features, and monitoring capabilities.
 * Version: 1.0.0
 * Author: BaumPress
 * Author URI: https://baumpress.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: baum-mail
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * Network: false
 *
 * @package BaumMail
 * @version 1.0.0
 * <AUTHOR>
 * @copyright 2025 BaumPress
 * @license GPL-2.0-or-later
 */

// Prevent direct access
if (!defined('ABSPATH')) {
  exit;
}

// Define plugin constants
define('BAUM_MAIL_VERSION', '1.0.0');
define('BAUM_MAIL_PLUGIN_FILE', __FILE__);
define('BAUM_MAIL_PLUGIN_DIR', dirname(__FILE__) . '/');
define('BAUM_MAIL_PLUGIN_URL', get_template_directory_uri() . '/plugins/baum-mail/');
define('BAUM_MAIL_PLUGIN_BASENAME', 'baum-mail/baum-mail.php');
define('BAUM_MAIL_TEXT_DOMAIN', 'baum-mail');

/**
 * Main BaumMail Plugin Class
 *
 * @since 1.0.0
 */
final class BaumMail {

  /**
   * Plugin instance
   *
   * @var BaumMail
   * @since 1.0.0
   */
  private static $instance = null;

  /**
   * Core components
   *
   * @var array
   * @since 1.0.0
   */
  private $components = array();

  /**
   * Get plugin instance
   *
   * @return BaumMail
   * @since 1.0.0
   */
  public static function instance() {
    if (null === self::$instance) {
      self::$instance = new self();
    }
    return self::$instance;
  }

  /**
   * Constructor
   *
   * @since 1.0.0
   */
  private function __construct() {
    $this->init_hooks();
  }

  /**
   * Initialize hooks
   *
   * @since 1.0.0
   */
  private function init_hooks() {
    add_action('init', array($this, 'init'), 0);
    add_action('plugins_loaded', array($this, 'plugins_loaded'));
    add_action('admin_enqueue_scripts', array($this, 'admin_enqueue_scripts'));

    // Activation/Deactivation hooks
    register_activation_hook(__FILE__, array($this, 'activate'));
    register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    register_uninstall_hook(__FILE__, array('BaumMail', 'uninstall'));
  }

  /**
   * Initialize plugin
   *
   * @since 1.0.0
   */
  public function init() {
    // Load text domain
    load_plugin_textdomain(
      BAUM_MAIL_TEXT_DOMAIN,
      false,
      dirname(BAUM_MAIL_PLUGIN_BASENAME) . '/languages'
    );

    // Initialize components
    $this->init_components();
  }

  /**
   * Initialize after plugins loaded
   *
   * @since 1.0.0
   */
  public function plugins_loaded() {
    // Check requirements
    if (!$this->check_requirements()) {
      return;
    }


  }

  /**
   * Check plugin requirements
   *
   * @return bool
   * @since 1.0.0
   */
  private function check_requirements() {
    // Check PHP version
    if (version_compare(PHP_VERSION, '7.4', '<')) {
      add_action('admin_notices', function() {
        echo '<div class="notice notice-error"><p>';
        echo esc_html__('Baum Mail requires PHP 7.4 or higher.', BAUM_MAIL_TEXT_DOMAIN);
        echo '</p></div>';
      });
      return false;
    }

    // Check WordPress version
    if (version_compare(get_bloginfo('version'), '5.0', '<')) {
      add_action('admin_notices', function() {
        echo '<div class="notice notice-error"><p>';
        echo esc_html__('Baum Mail requires WordPress 5.0 or higher.', BAUM_MAIL_TEXT_DOMAIN);
        echo '</p></div>';
      });
      return false;
    }

    return true;
  }

  /**
   * Initialize plugin components
   *
   * @since 1.0.0
   */
  private function init_components() {
    // Load core classes
    $this->load_dependencies();

    // Initialize components
    $this->components['core'] = new BaumMail_Core();

    // Initialize admin component only in admin
    if (is_admin()) {
      $this->components['admin'] = new BaumMail_Admin();
    }

    $this->components['postfix'] = new BaumMail_Postfix();
    $this->components['dovecot'] = new BaumMail_Dovecot();
    $this->components['security'] = new BaumMail_Security();
    $this->components['monitor'] = new BaumMail_Monitor();
    $this->components['autoresponder'] = new BaumMail_Autoresponder();
    $this->components['encryption'] = new BaumMail_Encryption();
    $this->components['analytics'] = new BaumMail_Analytics();
    $this->components['api'] = new BaumMail_API();
    $this->components['map'] = new BaumMail_Map();
    $this->components['imap'] = new BaumMail_IMAP();
    $this->components['smtp'] = new BaumMail_SMTP();
  }



  /**
   * Load plugin dependencies
   *
   * @since 1.0.0
   */
  private function load_dependencies() {
    $includes = array(
      'includes/class-baum-mail-core.php',
      'includes/class-baum-mail-postfix.php',
      'includes/class-baum-mail-dovecot.php',
      'includes/class-baum-mail-security.php',
      'includes/class-baum-mail-monitor.php',
      'includes/class-baum-mail-autoresponder.php',
      'includes/class-baum-mail-encryption.php',
      'includes/class-baum-mail-analytics.php',
      'includes/class-baum-mail-api.php',
      'includes/class-baum-mail-docs-generator.php',
      'includes/class-baum-mail-map.php',
      'includes/class-baum-mail-imap.php',
      'includes/class-baum-mail-smtp.php',
      'includes/class-baum-mail-utils.php'
    );

    // Load admin class only in admin
    if (is_admin()) {
      $includes[] = 'includes/class-baum-mail-admin.php';
    }

    foreach ($includes as $file) {
      $filepath = BAUM_MAIL_PLUGIN_DIR . $file;
      if (file_exists($filepath)) {
        require_once $filepath;
      }
    }
  }

  /**
   * Enqueue admin scripts and styles
   *
   * @param string $hook_suffix Current admin page hook suffix
   * @since 1.0.0
   */
  public function admin_enqueue_scripts($hook_suffix) {
    // Only load on Baum Mail admin pages
    if (strpos($hook_suffix, 'baum-mail') === false) {
      return;
    }

    // Enqueue admin CSS
    wp_enqueue_style(
      'baum-mail-admin',
      BAUM_MAIL_PLUGIN_URL . 'assets/css/admin.css',
      array(),
      BAUM_MAIL_VERSION
    );

    // Enqueue admin JavaScript
    wp_enqueue_script(
      'baum-mail-admin',
      BAUM_MAIL_PLUGIN_URL . 'assets/js/admin.js',
      array('jquery'),
      BAUM_MAIL_VERSION,
      true
    );

    // Localize script with admin data
    wp_localize_script('baum-mail-admin', 'baumMailAdmin', array(
      'nonce' => wp_create_nonce('baum_mail_admin_nonce'),
      'ajaxUrl' => admin_url('admin-ajax.php'),
      'strings' => array(
        'confirmDelete' => __('Are you sure you want to delete this item?', BAUM_MAIL_TEXT_DOMAIN),
        'confirmRestart' => __('Are you sure you want to restart this service?', BAUM_MAIL_TEXT_DOMAIN),
        'loading' => __('Loading...', BAUM_MAIL_TEXT_DOMAIN),
        'error' => __('An error occurred. Please try again.', BAUM_MAIL_TEXT_DOMAIN)
      )
    ));
  }

  /**
   * Get component instance
   *
   * @param string $component Component name
   * @return object|null
   * @since 1.0.0
   */
  public function get_component($component) {
    return isset($this->components[$component]) ? $this->components[$component] : null;
  }

  /**
   * Plugin activation
   *
   * @since 1.0.0
   */
  public function activate() {
    // Create database tables
    $this->create_tables();
    
    // Set default options
    $this->set_default_options();
    
    // Flush rewrite rules
    flush_rewrite_rules();
    
    // Log activation
    error_log('Baum Mail plugin activated');
  }

  /**
   * Plugin deactivation
   *
   * @since 1.0.0
   */
  public function deactivate() {
    // Flush rewrite rules
    flush_rewrite_rules();
    
    // Log deactivation
    error_log('Baum Mail plugin deactivated');
  }

  /**
   * Plugin uninstall
   *
   * @since 1.0.0
   */
  public static function uninstall() {
    // Remove database tables
    self::drop_tables();
    
    // Remove options
    self::remove_options();
    
    // Log uninstall
    error_log('Baum Mail plugin uninstalled');
  }

  /**
   * Create database tables
   *
   * @since 1.0.0
   */
  private function create_tables() {
    global $wpdb;

    $charset_collate = $wpdb->get_charset_collate();

    // Email domains table
    $domains_table = $wpdb->prefix . 'baum_mail_domains';
    $domains_sql = "CREATE TABLE $domains_table (
      id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
      domain varchar(255) NOT NULL,
      description text,
      active tinyint(1) DEFAULT 1,
      created_at datetime DEFAULT CURRENT_TIMESTAMP,
      updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      PRIMARY KEY (id),
      UNIQUE KEY domain (domain),
      KEY active (active)
    ) $charset_collate;";

    // Email accounts table
    $accounts_table = $wpdb->prefix . 'baum_mail_accounts';
    $accounts_sql = "CREATE TABLE $accounts_table (
      id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
      email varchar(255) NOT NULL,
      password varchar(255) NOT NULL,
      domain_id bigint(20) unsigned NOT NULL,
      quota bigint(20) DEFAULT 0,
      active tinyint(1) DEFAULT 1,
      created_at datetime DEFAULT CURRENT_TIMESTAMP,
      updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      PRIMARY KEY (id),
      UNIQUE KEY email (email),
      KEY domain_id (domain_id),
      KEY active (active),
      FOREIGN KEY (domain_id) REFERENCES $domains_table(id) ON DELETE CASCADE
    ) $charset_collate;";

    // Email aliases table
    $aliases_table = $wpdb->prefix . 'baum_mail_aliases';
    $aliases_sql = "CREATE TABLE $aliases_table (
      id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
      source varchar(255) NOT NULL,
      destination text NOT NULL,
      domain_id bigint(20) unsigned NOT NULL,
      active tinyint(1) DEFAULT 1,
      created_at datetime DEFAULT CURRENT_TIMESTAMP,
      updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      PRIMARY KEY (id),
      UNIQUE KEY source (source),
      KEY domain_id (domain_id),
      KEY active (active),
      FOREIGN KEY (domain_id) REFERENCES $domains_table(id) ON DELETE CASCADE
    ) $charset_collate;";

    // Autoresponders table
    $autoresponders_table = $wpdb->prefix . 'baum_mail_autoresponders';
    $autoresponders_sql = "CREATE TABLE $autoresponders_table (
      id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
      account_id bigint(20) unsigned NOT NULL,
      email varchar(255) NOT NULL,
      subject varchar(255) NOT NULL,
      message text NOT NULL,
      start_date datetime DEFAULT NULL,
      end_date datetime DEFAULT NULL,
      options text DEFAULT NULL,
      active tinyint(1) DEFAULT 1,
      created_at datetime DEFAULT CURRENT_TIMESTAMP,
      updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      PRIMARY KEY (id),
      KEY account_id (account_id),
      KEY email (email),
      KEY active (active),
      KEY start_date (start_date),
      KEY end_date (end_date),
      FOREIGN KEY (account_id) REFERENCES $accounts_table(id) ON DELETE CASCADE
    ) $charset_collate;";

    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($domains_sql);
    dbDelta($accounts_sql);
    dbDelta($aliases_sql);
    dbDelta($autoresponders_sql);
  }

  /**
   * Set default plugin options
   *
   * @since 1.0.0
   */
  private function set_default_options() {
    $defaults = array(
      'baum_mail_postfix_config_path' => '/etc/postfix',
      'baum_mail_dovecot_config_path' => '/etc/dovecot',
      'baum_mail_default_quota' => **********, // 1GB in bytes
      'baum_mail_enable_clamav' => true,
      'baum_mail_enable_spamassassin' => true,
      'baum_mail_ssl_cert_path' => '/etc/ssl/certs',
      'baum_mail_ssl_key_path' => '/etc/ssl/private',
      'baum_mail_monitor_interval' => 300, // 5 minutes
      'baum_mail_api_enabled' => true
    );

    foreach ($defaults as $option => $value) {
      if (get_option($option) === false) {
        add_option($option, $value);
      }
    }
  }

  /**
   * Drop database tables
   *
   * @since 1.0.0
   */
  private static function drop_tables() {
    global $wpdb;

    $tables = array(
      $wpdb->prefix . 'baum_mail_autoresponders',
      $wpdb->prefix . 'baum_mail_aliases',
      $wpdb->prefix . 'baum_mail_accounts',
      $wpdb->prefix . 'baum_mail_domains'
    );

    foreach ($tables as $table) {
      $wpdb->query("DROP TABLE IF EXISTS $table");
    }
  }

  /**
   * Remove plugin options
   *
   * @since 1.0.0
   */
  private static function remove_options() {
    $options = array(
      'baum_mail_postfix_config_path',
      'baum_mail_dovecot_config_path',
      'baum_mail_default_quota',
      'baum_mail_enable_clamav',
      'baum_mail_enable_spamassassin',
      'baum_mail_ssl_cert_path',
      'baum_mail_ssl_key_path',
      'baum_mail_monitor_interval',
      'baum_mail_api_enabled'
    );

    foreach ($options as $option) {
      delete_option($option);
    }
  }
}

/**
 * Initialize the plugin
 *
 * @return BaumMail
 * @since 1.0.0
 */
function baum_mail() {
  return BaumMail::instance();
}

// Initialize the plugin
baum_mail();

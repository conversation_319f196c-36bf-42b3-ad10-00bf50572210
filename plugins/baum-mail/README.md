# Baum Mail - WordPress Email Server Management Plugin

A comprehensive WordPress plugin for managing email servers with Postfix, Dovecot, and security features including ClamAV, SpamAssassin, and Rspamd integration.

## Features

- **Complete Email Server Management**: Full CRUD operations for domains, email accounts, and aliases
- **Postfix Integration**: Automatic configuration generation and management
- **Dovecot Integration**: IMAP/POP3 server configuration and mailbox management
- **Security Features**: ClamAV antivirus, SpamAssassin/Rspamd spam filtering
- **System Monitoring**: Real-time status monitoring and health checks
- **REST API**: Programmatic access for integration with other plugins
- **WordPress Admin Interface**: User-friendly management interface
- **CLI Wrappers**: Lightweight API layer for command-line tools
- **SSL/TLS Support**: Certificate management and monitoring
- **Blacklist Management**: IP and domain blacklisting capabilities

## Requirements

- WordPress 5.0 or higher
- PHP 7.4 or higher
- MySQL 5.7 or higher
- Linux server with root access
- Postfix mail server
- Dovecot IMAP/POP3 server
- ClamAV (optional)
- SpamAs<PERSON>ssin or Rspamd (optional)

## Installation

1. Upload the `baum-mail` folder to your `/wp-content/plugins/` directory
2. Activate the plugin through the 'Plugins' menu in WordPress
3. Configure your email server settings in the Baum Mail settings page
4. Follow the setup instructions for each email service component

## Configuration

### Basic Settings

Navigate to **Baum Mail > Settings** in your WordPress admin to configure:

- Postfix configuration path (default: `/etc/postfix`)
- Dovecot configuration path (default: `/etc/dovecot`)
- SSL certificate paths
- Default quota settings
- Security feature toggles
- API access settings

### Database Tables

The plugin creates the following database tables:

- `wp_baum_mail_domains` - Email domains
- `wp_baum_mail_accounts` - Email accounts
- `wp_baum_mail_aliases` - Email aliases

## Email Server Setup

### Postfix Setup

1. Install Postfix:
```bash
sudo apt-get update
sudo apt-get install postfix
```

2. Configure basic settings:
```bash
sudo postconf -e "myhostname = mail.yourdomain.com"
sudo postconf -e "mydomain = yourdomain.com"
sudo postconf -e "myorigin = \$mydomain"
```

3. Enable virtual domains:
```bash
sudo postconf -e "virtual_mailbox_domains = hash:/etc/postfix/virtual_domains"
sudo postconf -e "virtual_mailbox_maps = hash:/etc/postfix/virtual_mailboxes"
sudo postconf -e "virtual_alias_maps = hash:/etc/postfix/virtual_aliases"
```

4. Set mailbox location:
```bash
sudo postconf -e "virtual_mailbox_base = /var/mail/vhosts"
sudo postconf -e "virtual_uid_maps = static:5000"
sudo postconf -e "virtual_gid_maps = static:5000"
```

5. Create vmail user:
```bash
sudo groupadd -g 5000 vmail
sudo useradd -g vmail -u 5000 vmail -d /var/mail/vhosts -m
```

6. Restart Postfix:
```bash
sudo systemctl restart postfix
sudo systemctl enable postfix
```

### Dovecot Setup

1. Install Dovecot:
```bash
sudo apt-get install dovecot-core dovecot-imapd dovecot-pop3d dovecot-lmtpd dovecot-mysql
```

2. Configure authentication:
```bash
sudo nano /etc/dovecot/conf.d/10-auth.conf
```

Add:
```
auth_mechanisms = plain login
auth_username_format = %Lu
```

3. Configure mail location:
```bash
sudo nano /etc/dovecot/conf.d/10-mail.conf
```

Add:
```
mail_location = maildir:/var/mail/vhosts/%d/%n
mail_privileged_group = vmail
mail_uid = vmail
mail_gid = vmail
```

4. Configure SSL:
```bash
sudo nano /etc/dovecot/conf.d/10-ssl.conf
```

Add:
```
ssl = required
ssl_cert = </etc/ssl/certs/mail.crt
ssl_key = </etc/ssl/private/mail.key
```

5. Restart Dovecot:
```bash
sudo systemctl restart dovecot
sudo systemctl enable dovecot
```

### ClamAV Setup (Optional)

1. Install ClamAV:
```bash
sudo apt-get install clamav clamav-daemon
```

2. Update virus definitions:
```bash
sudo freshclam
```

3. Start ClamAV:
```bash
sudo systemctl start clamav-daemon
sudo systemctl enable clamav-daemon
```

### SpamAssassin Setup (Optional)

1. Install SpamAssassin:
```bash
sudo apt-get install spamassassin spamc
```

2. Enable SpamAssassin:
```bash
sudo systemctl start spamassassin
sudo systemctl enable spamassassin
```

3. Update spam rules:
```bash
sudo sa-update
```

## Usage

### WordPress Admin Interface

1. **Overview**: View system statistics and quick actions
2. **Domains**: Manage email domains
3. **Email Accounts**: Create and manage email accounts
4. **Aliases**: Set up email aliases and forwarding
5. **Security**: Monitor antivirus and spam filtering
6. **Monitoring**: Real-time system health monitoring
7. **Settings**: Configure plugin options

### REST API

Enable the REST API in settings and use these endpoints:

#### Domains
- `GET /wp-json/baum-mail/v1/domains` - List domains
- `POST /wp-json/baum-mail/v1/domains` - Create domain
- `GET /wp-json/baum-mail/v1/domains/{id}` - Get domain
- `PUT /wp-json/baum-mail/v1/domains/{id}` - Update domain
- `DELETE /wp-json/baum-mail/v1/domains/{id}` - Delete domain

#### Email Accounts
- `GET /wp-json/baum-mail/v1/accounts` - List accounts
- `POST /wp-json/baum-mail/v1/accounts` - Create account
- `GET /wp-json/baum-mail/v1/accounts/{id}` - Get account
- `PUT /wp-json/baum-mail/v1/accounts/{id}` - Update account
- `DELETE /wp-json/baum-mail/v1/accounts/{id}` - Delete account

#### System Status
- `GET /wp-json/baum-mail/v1/status` - Get system status

### Programmatic Usage

```php
// Get plugin instance
$baum_mail = baum_mail();
$core = $baum_mail->get_component('core');

// Create domain with limits and encryption
$domain_id = $core->create_domain('example.com', 'Example Domain');

// Create email account with GPG encryption
$account_id = $core->create_account(
  '<EMAIL>',
  'secure_password',
  **********, // 1GB quota
  array(
    'daily_send_limit' => 500,
    'daily_receive_limit' => 2000,
    'encryption_enabled' => true
  )
);

// Create alias
$alias_id = $core->create_alias('<EMAIL>', '<EMAIL>');

// Get system status
$status = $baum_mail->get_component('monitor')->get_system_status();

// Check blacklist status
$blacklist_report = $baum_mail->get_component('security')->check_blacklist_status('*******');

// Get logs
$logs = $core->get_logs(array(
  'action' => 'domain_created',
  'limit' => 50,
  'date_from' => '2025-01-01'
));

// Generate GPG key pair
$encryption = $baum_mail->get_component('encryption');
$keys = $encryption->generate_key_pair('<EMAIL>', 'John Doe', 'passphrase');

// Bulk operations
$domains = array('domain1.com', 'domain2.com', 'domain3.com');
foreach ($domains as $domain) {
  $result = $core->create_domain($domain, "Auto-created domain: {$domain}");
  if (is_wp_error($result)) {
    error_log("Failed to create domain {$domain}: " . $result->get_error_message());
  }
}
```

## Programmatic API Documentation

### Core Mail Management

```php
// Get plugin instance
$baum_mail = baum_mail();
$core = $baum_mail->get_component('core');

// Domain Management
$domain_id = $core->create_domain('example.com', 'Example Domain', array(
  'max_accounts' => 100,
  'daily_send_limit' => 1000,
  'daily_receive_limit' => 5000,
  'encryption_enabled' => true
));

$domains = $core->get_domains(array('active' => 1));
$core->delete_domain($domain_id);

// Account Management
$account_id = $core->create_account(
  '<EMAIL>',
  'secure_password',
  **********, // 1GB quota
  array(
    'daily_send_limit' => 500,
    'daily_receive_limit' => 2000,
    'encryption_enabled' => true
  )
);

$accounts = $core->get_accounts(array('domain_id' => $domain_id));
$core->delete_account($account_id);

// Alias Management
$alias_id = $core->create_alias('<EMAIL>', '<EMAIL>,<EMAIL>');
$aliases = $core->get_aliases(array('domain_id' => $domain_id));
$core->delete_alias($alias_id);
```

### IMAP Operations

```php
$imap = $baum_mail->get_component('imap');

// Connect and get messages
$messages = $imap->get_messages('<EMAIL>', 'password', 'INBOX', 50);

// Get message content
$content = $imap->get_message_content('<EMAIL>', 'password', 123);

// Mailbox management
$mailboxes = $imap->get_mailboxes('<EMAIL>', 'password');
$imap->create_mailbox('<EMAIL>', 'password', 'Custom Folder');
$imap->delete_mailbox('<EMAIL>', 'password', 'Custom Folder');

// Server status
$status = $imap->get_server_status();
```

### SMTP Operations

```php
$smtp = $baum_mail->get_component('smtp');

// Send email with encryption and limits
$result = $smtp->send_email(
  '<EMAIL>',
  'Subject',
  'Message body',
  array('From: <EMAIL>'),
  array('/path/to/attachment.pdf')
);

// Test SMTP connection
$test = $smtp->test_connection('localhost', 587, 'user', 'pass', 'tls');

// Queue management
$queue_status = $smtp->get_queue_status();
$smtp->flush_queue();

// Server status
$status = $smtp->get_server_status();
```

### Encryption Operations

```php
$encryption = $baum_mail->get_component('encryption');

// Generate GPG key pair
$keys = $encryption->generate_key_pair(
  '<EMAIL>',
  'John Doe',
  'secure_passphrase'
);

// Import public key
$encryption->import_public_key('<EMAIL>', $public_key_data);

// Encrypt message
$encrypted = $encryption->encrypt_message('Secret message', '<EMAIL>');

// Check encryption status
$status = $encryption->get_encryption_status();
```

### Security & Monitoring

```php
$security = $baum_mail->get_component('security');
$monitor = $baum_mail->get_component('monitor');

// Security checks
$security_status = $security->get_security_status();
$blacklist_report = $security->check_blacklist_status('*******');

// System monitoring
$system_status = $monitor->get_system_status();
$service_status = $monitor->check_service('postfix');

// Logs and reporting
$logs = $core->get_logs(array(
  'action' => 'email_sent',
  'limit' => 100,
  'date_from' => '2025-01-01'
));
```

## CLI Utilities

The plugin provides CLI wrapper functions for common operations:

```php
// Postfix operations
BaumMail_Utils::postconf_get('myhostname');
BaumMail_Utils::postmap('/etc/postfix/virtual_domains');
BaumMail_Utils::postqueue_flush();

// Dovecot operations
BaumMail_Utils::doveadm_quota('<EMAIL>');
BaumMail_Utils::doveadm_kick('<EMAIL>');

// System operations
BaumMail_Utils::service_restart('postfix');
BaumMail_Utils::get_disk_usage('/var/mail');
```

## Security

- All user inputs are sanitized and validated
- Database queries use prepared statements
- Command execution is properly escaped
- API endpoints require proper authentication
- SSL/TLS encryption is enforced where possible

## Troubleshooting

### Common Issues

1. **Permission Errors**: Ensure the web server user has appropriate permissions
2. **Service Not Starting**: Check system logs and configuration files
3. **Mail Not Delivered**: Verify DNS records and firewall settings
4. **Database Errors**: Check WordPress database credentials

### Log Files

Monitor these log files for issues:
- `/var/log/mail.log` - General mail server logs
- `/var/log/postfix.log` - Postfix specific logs
- `/var/log/dovecot.log` - Dovecot specific logs
- `/var/log/clamav/clamav.log` - ClamAV logs

## Support

For support and bug reports, please create an issue in the plugin repository or contact the development team.

## License

This plugin is licensed under the GPL v2 or later.

## Changelog

### 1.0.0
- Initial release
- Complete email server management
- WordPress admin interface
- REST API endpoints
- Security features integration
- System monitoring capabilities

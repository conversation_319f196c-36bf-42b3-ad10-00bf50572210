# Postfix SMTP Server

## Overview

Postfix is a fast, secure, and reliable SMTP server that handles email sending and routing for the Baum Mail plugin. It provides mail transfer agent (MTA) functionality with robust security features including SSL/TLS encryption for transport security, spam filtering integration, and virtual domain support.

## What Postfix Does

- **SMTP Server**: Handles incoming and outgoing email delivery
- **Mail Routing**: Routes emails between domains and servers
- **Queue Management**: Manages email queues for reliable delivery
- **Virtual Domains**: Supports multiple domains on single server
- **SSL/TLS Support**: Encrypts SMTP connections for transport security
- **Anti-Spam Integration**: Works with SpamAssassin and other filters
- **Relay Control**: Prevents unauthorized email relaying

## How Baum Mail Uses Postfix

### Core Integration

The Baum Mail plugin integrates with Postfix through the `BaumMail_Postfix` class:

```php
// Get Postfix component
$postfix = baum_mail()->get_component('postfix');

// Check Postfix status
$status = $postfix->get_status();

// Reload configuration
$postfix->reload_config();

// Update domain maps
$postfix->update_domain_maps();
```

### Configuration Management

The plugin automatically generates Postfix configuration:

```php
// Generate main.cf configuration
$config = $postfix->generate_main_config();

// Generate virtual domain maps
$postfix->update_domain_maps();

// Generate virtual mailbox maps
$postfix->update_mailbox_maps();

// Generate virtual alias maps
$postfix->update_alias_maps();
```

### Queue Management

```php
// Get queue status
$queue_status = $postfix->get_queue_status();

// Flush mail queue
$postfix->flush_queue();

// Delete specific message
$postfix->delete_message($queue_id);

// Hold message in queue
$postfix->hold_message($queue_id);
```

## Command Line Usage

### Service Management

```bash
# Check Postfix status
sudo systemctl status postfix

# Start Postfix
sudo systemctl start postfix

# Stop Postfix
sudo systemctl stop postfix

# Restart Postfix
sudo systemctl restart postfix

# Reload configuration
sudo systemctl reload postfix
# OR
sudo postfix reload
```

### Configuration Management

```bash
# Check configuration
sudo postfix check

# Show configuration
sudo postconf

# Show specific parameter
sudo postconf myhostname

# Set configuration parameter
sudo postconf -e "myhostname = mail.example.com"

# Show non-default settings
sudo postconf -n
```

### Queue Management

```bash
# View mail queue
sudo postqueue -p

# Flush mail queue (attempt delivery)
sudo postqueue -f

# Delete all messages in queue
sudo postsuper -d ALL

# Delete specific message
sudo postsuper -d [QUEUE_ID]

# Hold message in queue
sudo postsuper -h [QUEUE_ID]

# Release held message
sudo postsuper -H [QUEUE_ID]

# Show message content
sudo postcat -q [QUEUE_ID]
```

### Map Management

```bash
# Update hash maps
sudo postmap /etc/postfix/virtual_domains
sudo postmap /etc/postfix/virtual_mailboxes
sudo postmap /etc/postfix/virtual_aliases

# View map contents
sudo postmap -q domain.com /etc/postfix/virtual_domains
sudo postmap -q <EMAIL> /etc/postfix/virtual_mailboxes
```

### Log Analysis

```bash
# View mail log
sudo tail -f /var/log/mail.log

# Search for specific domain
sudo grep "domain.com" /var/log/mail.log

# Show delivery statistics
sudo pflogsumm /var/log/mail.log

# Real-time log monitoring
sudo journalctl -u postfix -f
```

## Programmatic API Usage

### PHP Integration

```php
class BaumMail_Postfix {
  
  /**
   * Get queue statistics
   */
  public function get_queue_stats() {
    $output = $this->execute_command('postqueue -p');
    return $this->parse_queue_output($output);
  }
  
  /**
   * Send test email
   */
  public function send_test_email($to, $from, $subject, $body) {
    $command = sprintf(
      'echo "%s" | mail -s "%s" -r "%s" "%s"',
      escapeshellarg($body),
      escapeshellarg($subject),
      escapeshellarg($from),
      escapeshellarg($to)
    );
    return $this->execute_command($command);
  }
  
  /**
   * Update virtual maps
   */
  public function update_virtual_maps() {
    $this->update_domain_maps();
    $this->update_mailbox_maps();
    $this->update_alias_maps();
    return $this->reload_config();
  }
}
```

### WordPress Integration

```php
// Hook into WordPress mail system
add_action('phpmailer_init', function($phpmailer) {
  if (get_option('baum_mail_use_postfix', true)) {
    $phpmailer->isSMTP();
    $phpmailer->Host = 'localhost';
    $phpmailer->Port = 587;
    $phpmailer->SMTPAuth = true;
    $phpmailer->SMTPSecure = 'tls';
  }
});

// Custom mail function
function baum_mail_send($to, $subject, $message, $headers = '') {
  $postfix = baum_mail()->get_component('postfix');
  return $postfix->send_mail($to, $subject, $message, $headers);
}
```

### REST API Integration

```php
// WordPress REST API endpoints
add_action('rest_api_init', function() {
  register_rest_route('baum-mail/v1', '/postfix/status', array(
    'methods' => 'GET',
    'callback' => 'baum_mail_get_postfix_status',
    'permission_callback' => 'baum_mail_check_permissions'
  ));
  
  register_rest_route('baum-mail/v1', '/postfix/queue', array(
    'methods' => 'GET',
    'callback' => 'baum_mail_get_queue_status',
    'permission_callback' => 'baum_mail_check_permissions'
  ));
  
  register_rest_route('baum-mail/v1', '/postfix/flush', array(
    'methods' => 'POST',
    'callback' => 'baum_mail_flush_queue',
    'permission_callback' => 'baum_mail_check_permissions'
  ));
});
```

## End-User Usage

### Sending Email

**SMTP Settings for Mail Clients:**
- Server: mail.yourdomain.com
- Port: 587 (STARTTLS) or 465 (SSL/TLS)
- Security: STARTTLS or SSL/TLS
- Authentication: Required
- Username: full email address
- Password: account password

### Common Mail Client Configuration

**Thunderbird:**
1. Account Settings → Outgoing Server (SMTP)
2. Add new SMTP server
3. Configure server settings
4. Test connection

**Outlook:**
1. File → Account Settings
2. Select account → Change
3. More Settings → Outgoing Server
4. Configure SMTP settings

**Apple Mail:**
1. Mail → Preferences → Accounts
2. Select account → Server Settings
3. Configure Outgoing Mail Server
4. Test connection

### Webmail Integration

```php
// Example webmail SMTP configuration
$smtp_config = array(
  'host' => 'localhost',
  'port' => 587,
  'auth' => true,
  'username' => $user_email,
  'password' => $user_password,
  'secure' => 'tls'
);
```

## Configuration Files

### Main Configuration (/etc/postfix/main.cf)

```
# Basic settings
myhostname = mail.yourdomain.com
mydomain = yourdomain.com
myorigin = $mydomain
inet_interfaces = all
inet_protocols = ipv4

# Virtual domains and mailboxes
virtual_mailbox_domains = hash:/etc/postfix/virtual_domains
virtual_mailbox_maps = hash:/etc/postfix/virtual_mailboxes
virtual_alias_maps = hash:/etc/postfix/virtual_aliases
virtual_mailbox_base = /var/mail/vhosts
virtual_minimum_uid = 100
virtual_uid_maps = static:5000
virtual_gid_maps = static:5000

# SSL/TLS settings
smtpd_tls_cert_file = /etc/ssl/certs/mail.crt
smtpd_tls_key_file = /etc/ssl/private/mail.key
smtpd_use_tls = yes
smtpd_tls_security_level = may
smtp_tls_security_level = may

# SASL authentication
smtpd_sasl_type = dovecot
smtpd_sasl_path = private/auth
smtpd_sasl_auth_enable = yes

# Relay restrictions
smtpd_relay_restrictions = 
  permit_mynetworks,
  permit_sasl_authenticated,
  defer_unauth_destination

# Recipient restrictions
smtpd_recipient_restrictions =
  permit_mynetworks,
  permit_sasl_authenticated,
  reject_unauth_destination,
  reject_invalid_hostname,
  reject_non_fqdn_sender,
  reject_non_fqdn_recipient,
  reject_unknown_sender_domain,
  reject_unknown_recipient_domain

# Message size limits
message_size_limit = 52428800
mailbox_size_limit = 1073741824

# Queue settings
maximal_queue_lifetime = 5d
bounce_queue_lifetime = 5d
```

### Master Configuration (/etc/postfix/master.cf)

```
# SMTP service
smtp      inet  n       -       y       -       -       smtpd

# Submission service (port 587)
submission inet n       -       y       -       -       smtpd
  -o syslog_name=postfix/submission
  -o smtpd_tls_security_level=encrypt
  -o smtpd_sasl_auth_enable=yes
  -o smtpd_client_restrictions=permit_sasl_authenticated,reject

# SMTPS service (port 465)
smtps     inet  n       -       y       -       -       smtpd
  -o syslog_name=postfix/smtps
  -o smtpd_tls_wrappermode=yes
  -o smtpd_sasl_auth_enable=yes

# Local delivery
pickup    unix  n       -       y       60      1       pickup
cleanup   unix  n       -       y       -       0       cleanup
qmgr      unix  n       -       n       300     1       qmgr
tlsmgr    unix  -       -       y       1000?   1       tlsmgr
rewrite   unix  -       -       y       -       -       trivial-rewrite
bounce    unix  -       -       y       -       0       bounce
defer     unix  -       -       y       -       0       bounce
trace     unix  -       -       y       -       0       bounce
verify    unix  -       -       y       -       1       verify
flush     unix  n       -       y       1000?   0       flush
proxymap  unix  -       -       n       -       -       proxymap
proxywrite unix -       -       n       -       1       proxymap
smtp      unix  -       -       y       -       -       smtp
relay     unix  -       -       y       -       -       smtp
showq     unix  n       -       y       -       -       showq
error     unix  -       -       y       -       -       error
retry     unix  -       -       y       -       -       error
discard   unix  -       -       y       -       -       discard
local     unix  -       n       n       -       -       local
virtual   unix  -       n       n       -       -       virtual
lmtp      unix  -       -       y       -       -       lmtp
anvil     unix  -       -       y       -       1       anvil
scache    unix  -       -       y       -       1       scache
```

### Virtual Domain Maps (/etc/postfix/virtual_domains)

```
domain1.com    OK
domain2.com    OK
domain3.com    OK
```

### Virtual Mailbox Maps (/etc/postfix/virtual_mailboxes)

```
<EMAIL>    domain1.com/user1/
<EMAIL>    domain1.com/user2/
<EMAIL>    domain2.com/admin/
```

### Virtual Alias Maps (/etc/postfix/virtual_aliases)

```
<EMAIL>     <EMAIL>
<EMAIL>  <EMAIL>,<EMAIL>
<EMAIL>  <EMAIL>
```

## Troubleshooting

### Common Issues

**Connection Refused:**
- Check if Postfix is running: `systemctl status postfix`
- Verify ports are open: `netstat -tlnp | grep :25`
- Check firewall settings: `ufw status`

**Authentication Failed:**
- Verify SASL configuration with Dovecot
- Check authentication socket: `ls -la /var/spool/postfix/private/auth`
- Review authentication logs

**Mail Not Delivered:**
- Check mail queue: `postqueue -p`
- Review mail logs: `tail -f /var/log/mail.log`
- Verify DNS MX records: `dig MX domain.com`

**SSL/TLS Errors:**
- Verify certificate paths and permissions
- Test certificate: `openssl s_client -connect mail.domain.com:587 -starttls smtp`
- Check SSL configuration in main.cf

### Log Analysis

```bash
# Common log locations
/var/log/mail.log
/var/log/postfix.log
/var/log/syslog

# Useful grep patterns
grep "reject" /var/log/mail.log
grep "bounced" /var/log/mail.log
grep "deferred" /var/log/mail.log
grep "sent" /var/log/mail.log
```

### Performance Tuning

```
# Increase connection limits
default_process_limit = 100
smtpd_client_connection_count_limit = 50
smtpd_client_connection_rate_limit = 30

# Queue management
maximal_queue_lifetime = 5d
bounce_queue_lifetime = 5d
minimal_backoff_time = 300s
maximal_backoff_time = 4000s

# Memory optimization
smtpd_client_message_rate_limit = 100
```

## Security Considerations

- Always use SSL/TLS encryption for SMTP connections
- Implement proper relay restrictions to prevent spam
- Regular monitoring of mail logs for suspicious activity
- Use strong authentication mechanisms
- Keep Postfix updated to latest stable version
- Implement rate limiting to prevent abuse
- Configure proper firewall rules
- Regular security audits and penetration testing

## Integration with Other Services

- **Dovecot**: Handles IMAP/POP3 and SASL authentication
- **SpamAssassin**: Provides spam filtering via milter
- **ClamAV**: Antivirus scanning via milter
- **Amavis**: Content filtering and virus scanning
- **OpenDKIM**: DKIM signing and verification
- **OpenDMARC**: DMARC policy enforcement

# Dovecot IMAP/POP3 Server

## Overview

Dovecot is a secure, fast, and lightweight IMAP and POP3 server that handles email retrieval for the Baum Mail plugin. It provides mailbox access, user authentication, and mail storage management with robust security features including SSL/TLS encryption for transport security.

## What Dovecot Does

- **IMAP/POP3 Server**: Provides email retrieval services for mail clients
- **Mailbox Management**: Handles mailbox creation, deletion, and quota management
- **User Authentication**: Authenticates users against database or system accounts
- **SSL/TLS Support**: Encrypts connections between mail clients and server
- **Mail Storage**: Manages mail storage in Maildir or mbox formats
- **Sieve Filtering**: Supports server-side email filtering and rules

## How Baum Mail Uses Dovecot

### Core Integration

The Baum Mail plugin integrates with <PERSON>cot through the `BaumMail_Dovecot` class:

```php
// Get Dovecot component
$dovecot = baum_mail()->get_component('dovecot');

// Check Dovecot status
$status = $dovecot->get_status();

// Reload configuration
$dovecot->reload_config();
```

### Configuration Management

The plugin automatically generates Dovecot configuration files:

```php
// Generate main dovecot.conf
$config = $dovecot->generate_main_config();

// Generate SQL authentication config
$sql_config = $dovecot->generate_sql_config();

// Generate SSL configuration
$ssl_config = $dovecot->generate_ssl_config();
```

### Mailbox Operations

```php
// Create mailbox for user
$dovecot->create_mailbox($account_id);

// Delete mailbox
$dovecot->delete_mailbox($account_id);

// Set mailbox quota
$dovecot->set_quota($email, $quota_mb);
```

## Command Line Usage

### Service Management

```bash
# Check Dovecot status
sudo systemctl status dovecot

# Start Dovecot
sudo systemctl start dovecot

# Stop Dovecot
sudo systemctl stop dovecot

# Restart Dovecot
sudo systemctl restart dovecot

# Reload configuration
sudo systemctl reload dovecot
```

### Configuration Testing

```bash
# Test configuration syntax
sudo dovecot -n

# Check for configuration errors
sudo dovecot -c /etc/dovecot/dovecot.conf -n

# Test specific configuration file
sudo doveconf -n
```

### User Management with doveadm

```bash
# List active users
sudo doveadm who

# Kick user session
sudo <NAME_EMAIL>

# Check user quota
sudo doveadm quota get -u <EMAIL>

# Set user quota
sudo doveadm quota set -u <EMAIL> "User quota" 1G

# Force quota recalculation
sudo doveadm quota recalc -u <EMAIL>
```

### Mailbox Management

```bash
# List user mailboxes
sudo doveadm mailbox list -u <EMAIL>

# Create mailbox
sudo doveadm mailbox create -u <EMAIL> INBOX.Folder

# Delete mailbox
sudo doveadm mailbox delete -u <EMAIL> INBOX.Folder

# Purge deleted messages
sudo doveadm purge -u <EMAIL>
```

### Mail Search and Management

```bash
# Search messages
sudo doveadm search -u <EMAIL> mailbox INBOX subject "test"

# Move messages
sudo doveadm move -u <EMAIL> INBOX.Spam mailbox INBOX subject "spam"

# Delete messages
sudo doveadm expunge -u <EMAIL> mailbox INBOX subject "delete"

# Copy messages
sudo doveadm copy -u <EMAIL> INBOX.Backup mailbox INBOX all
```

## Programmatic API Usage

### PHP Integration

```php
// Execute doveadm commands through plugin
class BaumMail_Dovecot {
  
  /**
   * Get user quota information
   */
  public function get_user_quota($email) {
    $command = sprintf('doveadm quota get -u %s', escapeshellarg($email));
    $output = $this->execute_command($command);
    return $this->parse_quota_output($output);
  }
  
  /**
   * Kick user session
   */
  public function kick_user($email) {
    $command = sprintf('doveadm kick %s', escapeshellarg($email));
    return $this->execute_command($command);
  }
  
  /**
   * Get mailbox list
   */
  public function get_mailboxes($email) {
    $command = sprintf('doveadm mailbox list -u %s', escapeshellarg($email));
    $output = $this->execute_command($command);
    return explode("\n", trim($output));
  }
}
```

### REST API Integration

```php
// WordPress REST API endpoints
add_action('rest_api_init', function() {
  register_rest_route('baum-mail/v1', '/dovecot/status', array(
    'methods' => 'GET',
    'callback' => 'baum_mail_get_dovecot_status',
    'permission_callback' => 'baum_mail_check_permissions'
  ));
  
  register_rest_route('baum-mail/v1', '/dovecot/reload', array(
    'methods' => 'POST',
    'callback' => 'baum_mail_reload_dovecot',
    'permission_callback' => 'baum_mail_check_permissions'
  ));
});
```

### Database Integration

```php
// SQL authentication queries
$sql_config = array(
  'driver' => 'mysql',
  'connect' => sprintf(
    'host=%s dbname=%s user=%s password=%s',
    DB_HOST, DB_NAME, DB_USER, DB_PASSWORD
  ),
  'default_pass_scheme' => 'CRYPT',
  'password_query' => "
    SELECT email as user, password 
    FROM wp_baum_mail_accounts 
    WHERE email = '%u' AND active = 1
  ",
  'user_query' => "
    SELECT 
      CONCAT('/var/mail/vhosts/', domain, '/', SUBSTRING_INDEX(email, '@', 1)) as home,
      5000 as uid, 5000 as gid
    FROM wp_baum_mail_accounts a
    JOIN wp_baum_mail_domains d ON a.domain_id = d.id
    WHERE email = '%u' AND a.active = 1
  "
);
```

## End-User Usage

### Mail Client Configuration

**IMAP Settings:**
- Server: mail.yourdomain.com
- Port: 993 (SSL/TLS) or 143 (STARTTLS)
- Security: SSL/TLS or STARTTLS
- Authentication: Normal password

**POP3 Settings:**
- Server: mail.yourdomain.com  
- Port: 995 (SSL/TLS) or 110 (STARTTLS)
- Security: SSL/TLS or STARTTLS
- Authentication: Normal password

### Common Mail Clients

**Thunderbird:**
1. Add new account
2. Enter email and password
3. Select "Configure manually"
4. Set IMAP/POP3 server settings
5. Test connection

**Outlook:**
1. File → Add Account
2. Enter email address
3. Advanced options → Manual setup
4. Configure IMAP/POP3 settings
5. Test account settings

**Apple Mail:**
1. Mail → Add Account
2. Choose "Other Mail Account"
3. Enter account details
4. Configure incoming mail server
5. Verify settings

### Mobile Configuration

**iOS Mail:**
1. Settings → Mail → Accounts
2. Add Account → Other
3. Add Mail Account
4. Configure IMAP/POP3 settings

**Android Gmail:**
1. Gmail app → Settings
2. Add account → Other
3. Enter email details
4. Configure server settings

## Configuration Files

### Main Configuration (/etc/dovecot/dovecot.conf)

```
# Basic settings
protocols = imap pop3 lmtp
listen = *, ::
base_dir = /var/run/dovecot/

# SSL/TLS settings  
ssl = required
ssl_cert = </etc/ssl/certs/mail.crt
ssl_key = </etc/ssl/private/mail.key
ssl_protocols = !SSLv2 !SSLv3

# Authentication
auth_mechanisms = plain login
auth_username_format = %Lu

# Mail location
mail_location = maildir:/var/mail/vhosts/%d/%n
mail_privileged_group = vmail
mail_uid = vmail
mail_gid = vmail

# SQL authentication
passdb {
  driver = sql
  args = /etc/dovecot/dovecot-sql.conf.ext
}

userdb {
  driver = sql  
  args = /etc/dovecot/dovecot-sql.conf.ext
}
```

### SQL Configuration (/etc/dovecot/dovecot-sql.conf.ext)

```
driver = mysql
connect = host=localhost dbname=wordpress user=mailuser password=mailpass

default_pass_scheme = CRYPT

password_query = SELECT email as user, password FROM wp_baum_mail_accounts WHERE email = '%u' AND active = 1

user_query = SELECT CONCAT('/var/mail/vhosts/', domain, '/', SUBSTRING_INDEX(email, '@', 1)) as home, 5000 as uid, 5000 as gid FROM wp_baum_mail_accounts a JOIN wp_baum_mail_domains d ON a.domain_id = d.id WHERE email = '%u' AND a.active = 1
```

## Troubleshooting

### Common Issues

**Connection Refused:**
- Check if Dovecot is running: `systemctl status dovecot`
- Verify ports are open: `netstat -tlnp | grep :993`
- Check firewall settings

**Authentication Failed:**
- Verify database connection in dovecot-sql.conf.ext
- Check user credentials in database
- Review authentication logs: `tail -f /var/log/dovecot.log`

**SSL/TLS Errors:**
- Verify certificate paths and permissions
- Check certificate validity: `openssl x509 -in /etc/ssl/certs/mail.crt -text -noout`
- Ensure proper SSL configuration

**Permission Errors:**
- Check mail directory permissions: `ls -la /var/mail/vhosts/`
- Verify vmail user exists: `id vmail`
- Set correct ownership: `chown -R vmail:vmail /var/mail/vhosts/`

### Log Files

- Main log: `/var/log/dovecot.log`
- Info log: `/var/log/dovecot-info.log`  
- Debug log: `/var/log/dovecot-debug.log`
- Plugin log: `/var/www/html/wp-content/plugins/baum-mail/logs/dovecot.log`

### Performance Tuning

```
# Increase connection limits
default_process_limit = 1000
default_client_limit = 1000

# Enable high-performance mode
mail_fsync = never
mmap_disable = yes

# Optimize for SSD
mail_nfs_storage = no
mail_nfs_index = no
```

## Security Considerations

- Always use SSL/TLS encryption for client connections
- Regularly update Dovecot to latest stable version
- Monitor authentication logs for suspicious activity
- Use strong passwords and consider two-factor authentication
- Restrict access to configuration files (chmod 600)
- Regular security audits and penetration testing

## Integration with Other Services

- **Postfix**: Handles SMTP delivery to Dovecot via LMTP
- **SpamAssassin**: Filters spam before delivery to mailboxes
- **ClamAV**: Scans attachments for viruses
- **Sieve**: Provides server-side filtering rules
- **Roundcube/SOGo**: Web-based email clients

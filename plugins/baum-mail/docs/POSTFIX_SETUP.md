# Postfix Setup Guide for Baum Mail

This guide will help you set up Postfix mail server to work with the Baum Mail WordPress plugin.

## Prerequisites

- Ubuntu/Debian Linux server with root access
- Domain name with proper DNS records
- SSL certificate for your mail domain

## Installation

### 1. Install Postfix

```bash
sudo apt-get update
sudo apt-get install postfix postfix-mysql
```

During installation, select "Internet Site" and enter your domain name.

### 2. Basic Configuration

Edit the main Postfix configuration file:

```bash
sudo nano /etc/postfix/main.cf
```

Add or modify these settings:

```
# Basic settings
myhostname = mail.yourdomain.com
mydomain = yourdomain.com
myorigin = $mydomain
inet_interfaces = all
inet_protocols = ipv4

# Virtual domains and mailboxes
virtual_mailbox_domains = hash:/etc/postfix/virtual_domains
virtual_mailbox_maps = hash:/etc/postfix/virtual_mailboxes
virtual_alias_maps = hash:/etc/postfix/virtual_aliases
virtual_mailbox_base = /var/mail/vhosts
virtual_minimum_uid = 100
virtual_uid_maps = static:5000
virtual_gid_maps = static:5000

# Security settings
smtpd_tls_cert_file = /etc/ssl/certs/mail.crt
smtpd_tls_key_file = /etc/ssl/private/mail.key
smtpd_use_tls = yes
smtpd_tls_session_cache_database = btree:${data_directory}/smtpd_scache
smtp_tls_session_cache_database = btree:${data_directory}/smtp_scache

# SASL authentication
smtpd_sasl_type = dovecot
smtpd_sasl_path = private/auth
smtpd_sasl_auth_enable = yes
smtpd_sasl_security_options = noanonymous
smtpd_sasl_local_domain = $myhostname
broken_sasl_auth_clients = yes

# Restrictions
smtpd_recipient_restrictions = permit_sasl_authenticated, permit_mynetworks, reject_unauth_destination
smtpd_sender_restrictions = permit_sasl_authenticated, permit_mynetworks

# Message size limits
message_size_limit = 52428800
mailbox_size_limit = 0

# Queue settings
maximal_queue_lifetime = 7d
bounce_queue_lifetime = 7d
maximal_backoff_time = 4000s
minimal_backoff_time = 300s
queue_run_delay = 300s
```

### 3. Configure Master Services

Edit the master configuration file:

```bash
sudo nano /etc/postfix/master.cf
```

Add these service definitions:

```
# SMTP service
smtp      inet  n       -       y       -       -       smtpd

# Submission service (port 587)
submission inet n       -       y       -       -       smtpd
  -o syslog_name=postfix/submission
  -o smtpd_tls_security_level=encrypt
  -o smtpd_sasl_auth_enable=yes
  -o smtpd_reject_unlisted_recipient=no
  -o smtpd_client_restrictions=$mua_client_restrictions
  -o smtpd_helo_restrictions=$mua_helo_restrictions
  -o smtpd_sender_restrictions=$mua_sender_restrictions
  -o smtpd_recipient_restrictions=permit_sasl_authenticated,reject
  -o milter_macro_daemon_name=ORIGINATING

# SMTPS service (port 465)
smtps     inet  n       -       y       -       -       smtpd
  -o syslog_name=postfix/smtps
  -o smtpd_tls_wrappermode=yes
  -o smtpd_sasl_auth_enable=yes
  -o smtpd_reject_unlisted_recipient=no
  -o smtpd_client_restrictions=$mua_client_restrictions
  -o smtpd_helo_restrictions=$mua_helo_restrictions
  -o smtpd_sender_restrictions=$mua_sender_restrictions
  -o smtpd_recipient_restrictions=permit_sasl_authenticated,reject
  -o milter_macro_daemon_name=ORIGINATING
```

### 4. Create Virtual Mail User

Create a dedicated user for virtual mailboxes:

```bash
sudo groupadd -g 5000 vmail
sudo useradd -g vmail -u 5000 vmail -d /var/mail/vhosts -m
sudo chown -R vmail:vmail /var/mail/vhosts
sudo chmod -R 750 /var/mail/vhosts
```

### 5. Create Virtual Map Files

Create empty virtual map files that will be managed by Baum Mail:

```bash
sudo touch /etc/postfix/virtual_domains
sudo touch /etc/postfix/virtual_mailboxes
sudo touch /etc/postfix/virtual_aliases
sudo chown root:root /etc/postfix/virtual_*
sudo chmod 644 /etc/postfix/virtual_*
```

### 6. SSL Certificate Setup

If you don't have SSL certificates, you can create self-signed ones for testing:

```bash
sudo openssl req -new -x509 -days 365 -nodes -out /etc/ssl/certs/mail.crt -keyout /etc/ssl/private/mail.key
sudo chmod 600 /etc/ssl/private/mail.key
sudo chmod 644 /etc/ssl/certs/mail.crt
```

For production, use Let's Encrypt or a commercial certificate.

### 7. Firewall Configuration

Open the necessary ports:

```bash
sudo ufw allow 25/tcp    # SMTP
sudo ufw allow 587/tcp   # Submission
sudo ufw allow 465/tcp   # SMTPS
```

### 8. Start and Enable Postfix

```bash
sudo systemctl restart postfix
sudo systemctl enable postfix
```

## Testing

### 1. Check Service Status

```bash
sudo systemctl status postfix
```

### 2. Check Port Listening

```bash
sudo netstat -tlnp | grep :25
sudo netstat -tlnp | grep :587
sudo netstat -tlnp | grep :465
```

### 3. Test Configuration

```bash
sudo postfix check
```

### 4. View Logs

```bash
sudo tail -f /var/log/mail.log
```

## Integration with Baum Mail

Once Postfix is configured:

1. Go to **Baum Mail > Settings** in WordPress admin
2. Set the Postfix configuration path (default: `/etc/postfix`)
3. The plugin will automatically manage virtual domains, mailboxes, and aliases
4. Create domains and email accounts through the WordPress interface

## Troubleshooting

### Common Issues

1. **Permission Denied Errors**
   - Check file permissions on configuration files
   - Ensure vmail user owns mailbox directories

2. **Mail Not Delivered**
   - Check DNS MX records
   - Verify firewall settings
   - Check mail logs for errors

3. **Authentication Failures**
   - Ensure Dovecot is properly configured for SASL
   - Check socket permissions

4. **TLS/SSL Issues**
   - Verify certificate paths and permissions
   - Test certificate validity

### Log Files

Monitor these files for troubleshooting:
- `/var/log/mail.log` - General mail logs
- `/var/log/postfix.log` - Postfix specific logs
- `/var/log/syslog` - System logs

### Useful Commands

```bash
# Check mail queue
postqueue -p

# Flush mail queue
postqueue -f

# Delete specific message
postsuper -d MESSAGE_ID

# Test configuration
postfix check

# Reload configuration
postfix reload

# View configuration
postconf -n
```

## Security Considerations

1. **Regular Updates**: Keep Postfix updated
2. **Rate Limiting**: Configure rate limiting to prevent abuse
3. **Monitoring**: Monitor logs for suspicious activity
4. **Backup**: Regular backup of configuration files
5. **Firewall**: Use proper firewall rules

## Performance Tuning

For high-volume servers, consider these optimizations:

```bash
# Increase process limits
default_process_limit = 200
smtpd_client_connection_count_limit = 50
smtpd_client_connection_rate_limit = 100

# Queue management
maximal_queue_lifetime = 5d
bounce_queue_lifetime = 5d
```

## Next Steps

After Postfix setup:
1. Configure Dovecot for IMAP/POP3 access
2. Set up spam filtering with SpamAssassin or Rspamd
3. Configure antivirus scanning with ClamAV
4. Set up monitoring and alerting

# GPG Message Content Encryption

## Overview

GPG (GNU Privacy Guard) provides **end-to-end message content encryption** for the Baum Mail plugin. Unlike SSL/TLS which only protects data in transit, GPG encrypts the actual message content so it remains protected even when stored on servers. GPG uses public-key cryptography to ensure only the intended recipient can decrypt and read the message.

## What GPG Does

- **Message Content Encryption**: Encrypts email message body and attachments
- **Digital Signatures**: Verifies message authenticity and integrity
- **Key Management**: Manages public/private key pairs for users
- **End-to-End Security**: Messages remain encrypted from sender to recipient
- **Forward Secrecy**: Past messages remain secure even if keys are compromised
- **Cross-Platform**: Works across different email clients and systems

## GPG vs SSL/TLS Comparison

| Feature | GPG (Message Encryption) | SSL/TLS (Transport Encryption) |
|---------|--------------------------|--------------------------------|
| **Protection Scope** | Message content only | Entire connection |
| **Duration** | Permanent (until decrypted) | During transmission only |
| **Key Management** | User manages key pairs | Server manages certificates |
| **Transparency** | Requires user action | Automatic/transparent |
| **Storage Security** | Messages encrypted at rest | Messages stored in plain text |
| **Server Access** | Server cannot read content | Server can read all content |
| **Compliance** | GDPR, HIPAA content protection | PCI DSS, SOX transport protection |
| **Performance Impact** | Per-message encryption cost | Per-connection handshake cost |

## How Baum Mail Uses GPG

### Core Integration

The Baum Mail plugin integrates GPG through the `BaumMail_Encryption` class:

```php
// Get encryption component
$encryption = baum_mail()->get_component('encryption');

// Check if GPG is available
$available = $encryption->is_encryption_available();

// Generate key pair for user
$keys = $encryption->generate_key_pair($email, $name, $passphrase);

// Encrypt message for recipient
$encrypted_message = $encryption->encrypt_message($message, $recipient_email);
```

### Key Management

```php
// Generate GPG key pair
$key_result = $encryption->generate_key_pair(
  '<EMAIL>',
  'John Doe',
  'secure_passphrase'
);

// Import public key
$encryption->import_public_key($email, $public_key_data);

// Export public key
$public_key = $encryption->export_public_key($email);

// List available keys
$keys = $encryption->list_keys();
```

### Message Operations

```php
// Encrypt message content
$encrypted = $encryption->encrypt_message(
  'This is a secret message',
  '<EMAIL>'
);

// Decrypt message content
$decrypted = $encryption->decrypt_message(
  $encrypted_message,
  $private_key_passphrase
);

// Sign message
$signed = $encryption->sign_message($message, $sender_email);

// Verify signature
$verified = $encryption->verify_signature($signed_message);
```

## Command Line Usage

### Key Generation

```bash
# Generate new key pair
gpg --full-generate-key

# Generate key with specific parameters
gpg --batch --generate-key <<EOF
Key-Type: RSA
Key-Length: 4096
Subkey-Type: RSA
Subkey-Length: 4096
Name-Real: John Doe
Name-Email: <EMAIL>
Expire-Date: 2y
Passphrase: secure_passphrase
%commit
%echo done
EOF
```

### Key Management

```bash
# List public keys
gpg --list-keys

# List private keys
gpg --list-secret-keys

# Export public key
gpg --armor --export <EMAIL> > john_public.asc

# Export private key
gpg --armor --export-secret-keys <EMAIL> > john_private.asc

# Import public key
gpg --import recipient_public.asc

# Delete key
gpg --delete-key <EMAIL>
gpg --delete-secret-key <EMAIL>
```

### Message Encryption/Decryption

```bash
# Encrypt message for recipient
echo "Secret message" | gpg --armor --encrypt --recipient <EMAIL>

# Encrypt and sign message
echo "Secret message" | gpg --armor --encrypt --sign --recipient <EMAIL> --local-user <EMAIL>

# Decrypt message
gpg --decrypt encrypted_message.asc

# Verify signature
gpg --verify signed_message.asc
```

### Key Server Operations

```bash
# Send public key to key server
gpg --send-keys --keyserver keyserver.ubuntu.com KEY_ID

# Receive public key from key server
gpg --recv-keys --keyserver keyserver.ubuntu.com KEY_ID

# Search for keys
gpg --search-keys --keyserver keyserver.ubuntu.com <EMAIL>

# Refresh keys from key server
gpg --refresh-keys --keyserver keyserver.ubuntu.com
```

## Programmatic API Usage

### PHP GnuPG Extension

```php
class BaumMail_GPG {
  
  private $gpg;
  
  public function __construct() {
    if (!extension_loaded('gnupg')) {
      throw new Exception('GnuPG extension not available');
    }
    
    $this->gpg = new gnupg();
    $this->gpg->seterrormode(GNUPG_ERROR_EXCEPTION);
  }
  
  /**
   * Encrypt message for recipient
   */
  public function encrypt_message($message, $recipient_email) {
    // Import recipient's public key
    $public_key = $this->get_public_key($recipient_email);
    $this->gpg->import($public_key);
    
    // Find key ID
    $keys = $this->gpg->keyinfo();
    $key_id = $this->find_key_id($keys, $recipient_email);
    
    // Add encryption key and encrypt
    $this->gpg->addencryptkey($key_id);
    return $this->gpg->encrypt($message);
  }
  
  /**
   * Decrypt message with private key
   */
  public function decrypt_message($encrypted_message, $passphrase) {
    $this->gpg->adddecryptkey('', $passphrase);
    return $this->gpg->decrypt($encrypted_message);
  }
  
  /**
   * Sign message with private key
   */
  public function sign_message($message, $sender_email, $passphrase) {
    $this->gpg->addsignkey($sender_email, $passphrase);
    return $this->gpg->sign($message);
  }
}
```

### WordPress Integration

```php
// Hook into email sending to encrypt messages
add_filter('wp_mail', function($args) {
  if (get_option('baum_mail_auto_encrypt', false)) {
    $encryption = baum_mail()->get_component('encryption');
    
    // Check if recipient has public key
    if ($encryption->has_public_key($args['to'])) {
      // Encrypt message content
      $encrypted_message = $encryption->encrypt_message(
        $args['message'],
        $args['to']
      );
      
      if (!is_wp_error($encrypted_message)) {
        $args['message'] = $encrypted_message;
        $args['headers'][] = 'Content-Type: text/plain; charset=UTF-8';
        $args['headers'][] = 'X-GPG-Encrypted: true';
      }
    }
  }
  
  return $args;
});
```

### REST API Integration

```php
// GPG encryption endpoints
add_action('rest_api_init', function() {
  register_rest_route('baum-mail/v1', '/gpg/encrypt', array(
    'methods' => 'POST',
    'callback' => function($request) {
      $encryption = baum_mail()->get_component('encryption');
      
      $message = $request->get_param('message');
      $recipient = $request->get_param('recipient');
      
      $encrypted = $encryption->encrypt_message($message, $recipient);
      
      if (is_wp_error($encrypted)) {
        return new WP_Error('encryption_failed', $encrypted->get_error_message());
      }
      
      return array('encrypted_message' => $encrypted);
    },
    'permission_callback' => 'baum_mail_check_permissions'
  ));
});
```

## End-User Usage

### Email Client Integration

**Thunderbird with Enigmail:**
1. Install Enigmail add-on
2. Generate or import GPG keys
3. Compose email normally
4. Click "Encrypt" button before sending
5. Recipient needs your public key to reply encrypted

**Outlook with Gpg4win:**
1. Install Gpg4win (includes GPA and Kleopatra)
2. Generate key pair in Kleopatra
3. Install GpgOL plugin for Outlook
4. Use encrypt/decrypt buttons in Outlook

**Apple Mail with GPGMail:**
1. Install GPG Suite for macOS
2. Generate keys in GPG Keychain Access
3. GPGMail integrates with Apple Mail
4. Use encrypt/sign buttons when composing

### Webmail Integration

```javascript
// JavaScript GPG integration for webmail
class WebmailGPG {
  
  async encryptMessage(message, recipientEmail) {
    // Use OpenPGP.js for client-side encryption
    const openpgp = require('openpgp');
    
    // Get recipient's public key
    const publicKey = await this.getPublicKey(recipientEmail);
    
    // Encrypt message
    const encrypted = await openpgp.encrypt({
      message: openpgp.createMessage({ text: message }),
      publicKeys: publicKey
    });
    
    return encrypted;
  }
  
  async decryptMessage(encryptedMessage, privateKey, passphrase) {
    const openpgp = require('openpgp');
    
    // Decrypt message
    const { data: decrypted } = await openpgp.decrypt({
      message: await openpgp.readMessage({ armoredMessage: encryptedMessage }),
      privateKeys: privateKey,
      passphrase: passphrase
    });
    
    return decrypted;
  }
}
```

### Mobile Usage

**Android - OpenKeychain:**
1. Install OpenKeychain app
2. Generate or import keys
3. Use with compatible email apps
4. Share public keys via QR codes

**iOS - PGP Everywhere:**
1. Install PGP Everywhere
2. Generate key pairs
3. Integrate with Mail app
4. Encrypt/decrypt messages

## GPG vs ProtonMail Security Comparison

### Encryption Strength

**GPG:**
- RSA 4096-bit or ECC P-384 keys
- AES-256 symmetric encryption
- SHA-256 or SHA-512 hashing
- Open source, peer-reviewed algorithms
- User controls all keys

**ProtonMail:**
- RSA 4096-bit keys (same as GPG)
- AES-256 symmetric encryption
- Uses OpenPGP standard (GPG-compatible)
- Additional server-side security layers
- Zero-access encryption architecture

### Key Management

**GPG:**
- ✅ Full user control over keys
- ✅ Can use offline key generation
- ✅ Multiple key backup options
- ❌ User responsible for key security
- ❌ More complex for average users

**ProtonMail:**
- ✅ Simplified key management
- ✅ Automatic key backup/sync
- ✅ User-friendly interface
- ❌ Keys stored on ProtonMail servers
- ❌ Less control over key generation

### Security Model

**GPG Security:**
```
[Your Device] --GPG Encrypt--> [Email Server] --GPG Encrypted--> [Recipient]
     |                              |                               |
  Private Key                 Cannot Decrypt                  Private Key
```

**ProtonMail Security:**
```
[Your Browser] --TLS--> [ProtonMail] --TLS--> [Recipient Browser]
      |                      |                        |
   Client-side           Zero-access              Client-side
   Encryption            Encryption               Decryption
```

### Verdict: GPG vs ProtonMail

**GPG is more secure when:**
- You need maximum control over keys
- You can manage keys securely
- You need offline encryption capability
- You require open-source transparency

**ProtonMail is more secure when:**
- You need simplified key management
- You want professional security management
- You need integrated secure email service
- You prefer user-friendly encryption

**Best of Both Worlds:**
Use GPG with ProtonMail - ProtonMail supports importing your own GPG keys, giving you both security models.

## Other Encryption Types

### S/MIME (Secure/Multipurpose Internet Mail Extensions)

```php
// S/MIME certificate-based encryption
class BaumMail_SMIME {
  
  public function encrypt_with_smime($message, $recipient_cert) {
    // Use OpenSSL for S/MIME encryption
    $encrypted = '';
    openssl_pkcs7_encrypt(
      $message,
      $encrypted,
      $recipient_cert,
      array(),
      PKCS7_BINARY
    );
    return $encrypted;
  }
}
```

**S/MIME vs GPG:**
- S/MIME uses X.509 certificates (like SSL/TLS)
- Better enterprise integration
- Requires Certificate Authority
- Less flexible than GPG
- Built into most email clients

### Age Encryption

```bash
# Modern alternative to GPG
# Generate key
age-keygen -o key.txt

# Encrypt file
age -r age1ql3z... -o encrypted.age message.txt

# Decrypt file
age -d -i key.txt encrypted.age
```

### Signal Protocol

- Used by Signal, WhatsApp, Facebook Messenger
- Perfect forward secrecy
- Double ratchet algorithm
- Not suitable for email (requires real-time communication)

## Security Best Practices

### Key Security
- Use strong passphrases (12+ characters, mixed case, numbers, symbols)
- Store private keys securely (encrypted storage, hardware tokens)
- Regular key rotation (every 2-4 years)
- Backup keys securely (multiple locations, encrypted)
- Use subkeys for different purposes (signing, encryption, authentication)

### Operational Security
- Verify key fingerprints through secure channels
- Use key servers cautiously (can be poisoned)
- Regular security audits of key management
- Monitor for key compromise indicators
- Implement key revocation procedures

### Integration Security
```php
// Secure GPG integration example
class SecureGPGIntegration {
  
  private function secure_key_storage($private_key, $passphrase) {
    // Encrypt private key with additional layer
    $encrypted_key = openssl_encrypt(
      $private_key,
      'AES-256-GCM',
      hash('sha256', $passphrase . wp_salt()),
      0,
      $iv,
      $tag
    );
    
    // Store with integrity check
    return base64_encode($iv . $tag . $encrypted_key);
  }
  
  private function validate_key_integrity($key_data) {
    // Verify key hasn't been tampered with
    $key_info = openssl_pkey_get_details(openssl_pkey_get_private($key_data));
    return $key_info !== false;
  }
}
```

## Troubleshooting

### Common GPG Issues

**Key Not Found:**
```bash
# Check available keys
gpg --list-keys
gpg --list-secret-keys

# Import missing key
gpg --import public_key.asc
```

**Encryption Failed:**
```bash
# Check key trust level
gpg --edit-key <EMAIL>
# In GPG prompt: trust, then select trust level
```

**Permission Errors:**
```bash
# Fix GPG directory permissions
chmod 700 ~/.gnupg
chmod 600 ~/.gnupg/*
```

**Performance Issues:**
- Use hardware acceleration when available
- Consider using ECC keys instead of RSA
- Implement key caching for frequently used keys
- Use GPG agent for passphrase caching

## Integration with Other Services

- **Key Servers**: Distribute public keys globally
- **Hardware Tokens**: Store private keys securely (YubiKey, etc.)
- **Password Managers**: Secure passphrase storage
- **Backup Services**: Encrypted key backup solutions
- **Enterprise PKI**: Integration with corporate certificate infrastructure
- **Mobile Apps**: Cross-platform key synchronization

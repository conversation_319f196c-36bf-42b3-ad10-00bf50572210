# Dovecot Setup Guide for Baum Mail

This guide will help you set up Dovecot IMAP/POP3 server to work with the Baum Mail WordPress plugin and Postfix.

## Prerequisites

- Postfix already configured and running
- Ubuntu/Debian Linux server with root access
- SSL certificate for your mail domain
- MySQL/MariaDB database (WordPress database can be used)

## Installation

### 1. Install Dovecot

```bash
sudo apt-get update
sudo apt-get install dovecot-core dovecot-imapd dovecot-pop3d dovecot-lmtpd dovecot-mysql
```

### 2. Basic Configuration

Edit the main Dovecot configuration file:

```bash
sudo nano /etc/dovecot/dovecot.conf
```

Add or modify these settings:

```
# Basic settings
protocols = imap pop3 lmtp
listen = *, ::
base_dir = /var/run/dovecot/
instance_name = dovecot

# SSL settings
ssl = required
ssl_cert = </etc/ssl/certs/mail.crt
ssl_key = </etc/ssl/private/mail.key
ssl_protocols = !SSLv2 !SSLv3
ssl_cipher_list = ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES128-GCM-SHA256
ssl_prefer_server_ciphers = yes
ssl_dh_parameters_length = 2048

# Authentication
auth_mechanisms = plain login
auth_username_format = %Lu

# Mail location
mail_location = maildir:/var/mail/vhosts/%d/%n
mail_privileged_group = vmail
mail_uid = vmail
mail_gid = vmail

# Include additional configurations
!include conf.d/*.conf
!include_try local.conf
```

### 3. Configure Authentication

Edit the authentication configuration:

```bash
sudo nano /etc/dovecot/conf.d/10-auth.conf
```

Modify these settings:

```
# Disable plain text authentication except over SSL/TLS
disable_plaintext_auth = yes

# Authentication mechanisms
auth_mechanisms = plain login

# Username format
auth_username_format = %Lu

# Include SQL authentication
!include auth-sql.conf.ext
```

### 4. Configure Mail Location

Edit the mail configuration:

```bash
sudo nano /etc/dovecot/conf.d/10-mail.conf
```

Set these values:

```
# Mail location
mail_location = maildir:/var/mail/vhosts/%d/%n

# Mail user and group
mail_uid = vmail
mail_gid = vmail
mail_privileged_group = vmail

# Mailbox creation
first_valid_uid = 5000
last_valid_uid = 5000
first_valid_gid = 5000
last_valid_gid = 5000
```

### 5. Configure SSL

Edit the SSL configuration:

```bash
sudo nano /etc/dovecot/conf.d/10-ssl.conf
```

Configure SSL settings:

```
# SSL is required
ssl = required

# SSL certificate and key
ssl_cert = </etc/ssl/certs/mail.crt
ssl_key = </etc/ssl/private/mail.key

# SSL protocols and ciphers
ssl_protocols = !SSLv2 !SSLv3
ssl_cipher_list = ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-AES256-GCM-SHA384
ssl_prefer_server_ciphers = yes

# DH parameters
ssl_dh_parameters_length = 2048
```

### 6. Configure Services

Edit the service configuration:

```bash
sudo nano /etc/dovecot/conf.d/10-master.conf
```

Configure services:

```
service imap-login {
  inet_listener imap {
    port = 143
  }
  inet_listener imaps {
    port = 993
    ssl = yes
  }
}

service pop3-login {
  inet_listener pop3 {
    port = 110
  }
  inet_listener pop3s {
    port = 995
    ssl = yes
  }
}

service lmtp {
  unix_listener /var/spool/postfix/private/dovecot-lmtp {
    mode = 0600
    user = postfix
    group = postfix
  }
}

service auth {
  unix_listener /var/spool/postfix/private/auth {
    mode = 0666
    user = postfix
    group = postfix
  }
  unix_listener auth-userdb {
    mode = 0600
    user = vmail
    group = vmail
  }
  user = dovecot
}

service auth-worker {
  user = vmail
}
```

### 7. Configure SQL Authentication

Create the SQL authentication configuration:

```bash
sudo nano /etc/dovecot/conf.d/auth-sql.conf.ext
```

Add this content:

```
# SQL authentication
passdb {
  driver = sql
  args = /etc/dovecot/dovecot-sql.conf.ext
}

userdb {
  driver = sql
  args = /etc/dovecot/dovecot-sql.conf.ext
}
```

### 8. Configure SQL Connection

Create the SQL connection file:

```bash
sudo nano /etc/dovecot/dovecot-sql.conf.ext
```

Add this content (replace with your WordPress database details):

```
# Database connection
driver = mysql
connect = host=localhost dbname=wordpress_db user=wp_user password=wp_password

# Default password scheme
default_pass_scheme = SSHA

# Password query
password_query = SELECT email as user, password FROM wp_baum_mail_accounts WHERE email = '%u' AND active = 1

# User query
user_query = SELECT email as user, 'vmail' as uid, 'vmail' as gid, '/var/mail/vhosts/%d/%n' as home, CONCAT('maildir:/var/mail/vhosts/', email) as mail FROM wp_baum_mail_accounts WHERE email = '%u' AND active = 1

# Iterate query for doveadm
iterate_query = SELECT email as user FROM wp_baum_mail_accounts WHERE active = 1
```

### 9. Set Permissions

Set proper permissions on configuration files:

```bash
sudo chown root:dovecot /etc/dovecot/dovecot-sql.conf.ext
sudo chmod 640 /etc/dovecot/dovecot-sql.conf.ext
```

### 10. Configure Namespace

Edit the namespace configuration:

```bash
sudo nano /etc/dovecot/conf.d/10-mail.conf
```

Add namespace configuration:

```
namespace inbox {
  type = private
  separator = .
  prefix = INBOX.
  location = 
  inbox = yes
  hidden = no
  list = yes
  subscriptions = yes

  mailbox Drafts {
    special_use = \Drafts
  }
  mailbox Junk {
    special_use = \Junk
  }
  mailbox Sent {
    special_use = \Sent
  }
  mailbox "Sent Messages" {
    special_use = \Sent
  }
  mailbox Trash {
    special_use = \Trash
  }
}
```

### 11. Configure Protocols

Edit protocol-specific settings:

```bash
sudo nano /etc/dovecot/conf.d/20-imap.conf
```

```
protocol imap {
  mail_max_userip_connections = 20
  imap_idle_notify_interval = 2 mins
}
```

```bash
sudo nano /etc/dovecot/conf.d/20-pop3.conf
```

```
protocol pop3 {
  mail_max_userip_connections = 10
  pop3_uidl_format = %08Xu%08Xv
}
```

```bash
sudo nano /etc/dovecot/conf.d/20-lmtp.conf
```

```
protocol lmtp {
  postmaster_address = <EMAIL>
}
```

### 12. Configure Logging

Edit the logging configuration:

```bash
sudo nano /etc/dovecot/conf.d/10-logging.conf
```

```
# Log file locations
log_path = /var/log/dovecot/dovecot.log
info_log_path = /var/log/dovecot/dovecot-info.log
debug_log_path = /var/log/dovecot/dovecot-debug.log

# Syslog facility
syslog_facility = mail

# Log verbosity
auth_verbose = yes
auth_debug = no
mail_debug = no
```

Create log directory:

```bash
sudo mkdir -p /var/log/dovecot
sudo chown dovecot:dovecot /var/log/dovecot
```

### 13. Firewall Configuration

Open the necessary ports:

```bash
sudo ufw allow 143/tcp   # IMAP
sudo ufw allow 993/tcp   # IMAPS
sudo ufw allow 110/tcp   # POP3
sudo ufw allow 995/tcp   # POP3S
```

### 14. Start and Enable Dovecot

```bash
sudo systemctl restart dovecot
sudo systemctl enable dovecot
```

## Testing

### 1. Check Service Status

```bash
sudo systemctl status dovecot
```

### 2. Check Port Listening

```bash
sudo netstat -tlnp | grep :143
sudo netstat -tlnp | grep :993
sudo netstat -tlnp | grep :110
sudo netstat -tlnp | grep :995
```

### 3. Test Configuration

```bash
sudo dovecot -n
```

### 4. Test Authentication

```bash
sudo doveadm <NAME_EMAIL> password123
```

### 5. View Logs

```bash
sudo tail -f /var/log/dovecot/dovecot.log
```

## Integration with Postfix

Update Postfix to use Dovecot for LMTP delivery:

```bash
sudo nano /etc/postfix/main.cf
```

Add:

```
# Use Dovecot LMTP for local delivery
virtual_transport = lmtp:unix:private/dovecot-lmtp
```

Restart Postfix:

```bash
sudo systemctl restart postfix
```

## Troubleshooting

### Common Issues

1. **Authentication Failures**
   - Check database connection settings
   - Verify user credentials in database
   - Check SQL query syntax

2. **Permission Errors**
   - Ensure vmail user owns mailbox directories
   - Check socket permissions for Postfix integration

3. **SSL/TLS Issues**
   - Verify certificate paths and permissions
   - Test certificate validity

4. **Connection Issues**
   - Check firewall settings
   - Verify service is listening on correct ports

### Useful Commands

```bash
# Test configuration
dovecot -n

# Test authentication
doveadm <NAME_EMAIL> password

# Check user mailbox
doveadm mailbox status -u <EMAIL> messages INBOX

# Get user quota
doveadm quota get -u <EMAIL>

# Kick user connections
<NAME_EMAIL>

# Reload configuration
doveadm reload
```

## Security Considerations

1. **Regular Updates**: Keep Dovecot updated
2. **Strong SSL**: Use strong SSL/TLS configuration
3. **Rate Limiting**: Configure connection limits
4. **Monitoring**: Monitor logs for suspicious activity
5. **Backup**: Regular backup of configuration files

## Performance Tuning

For high-volume servers:

```bash
# Increase connection limits
service imap-login {
  process_limit = 500
  service_count = 1
}

# Enable high-performance mode
mail_fsync = never
mmap_disable = yes
```

## Next Steps

After Dovecot setup:
1. Test email delivery end-to-end
2. Configure spam filtering
3. Set up antivirus scanning
4. Configure monitoring and alerting
5. Set up backup procedures

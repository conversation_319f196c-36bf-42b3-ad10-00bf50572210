# ClamAV Antivirus Scanning

## Overview

ClamAV is an open-source antivirus engine that integrates with the Baum Mail plugin to provide real-time virus and malware scanning for email attachments and content. It protects against viruses, trojans, malware, and other malicious threats by scanning incoming and outgoing emails before delivery.

## What ClamAV Does

- **Virus Detection**: Scans files and email attachments for known viruses
- **Malware Protection**: Detects trojans, worms, and other malicious software
- **Real-time Scanning**: Provides on-access scanning for email processing
- **Signature Updates**: Automatically updates virus definitions
- **Quarantine Management**: Isolates infected files for safe handling
- **Performance Optimization**: Efficient scanning with minimal system impact

## How Baum Mail Uses ClamAV

### Core Integration

The Baum Mail plugin integrates with Clam<PERSON> through the `BaumMail_Security` class:

```php
// Get security component
$security = baum_mail()->get_component('security');

// Check ClamAV status
$clamav_status = $security->get_clamav_status();

// Scan file for viruses
$scan_result = $security->scan_file('/path/to/file');

// Scan mailbox
$mailbox_result = $security->scan_mailbox('<EMAIL>');

// Update virus definitions
$security->update_virus_definitions();
```

### Automatic Email Scanning

```php
// Email scanning integration
class BaumMail_VirusScanner {
  
  /**
   * Scan email content and attachments
   */
  public function scan_email($email_data) {
    if (!get_option('baum_mail_enable_clamav', true)) {
      return array('clean' => true, 'threats' => array());
    }
    
    $threats = array();
    
    // Scan email body
    $body_result = $this->scan_content($email_data['body']);
    if (!$body_result['clean']) {
      $threats = array_merge($threats, $body_result['threats']);
    }
    
    // Scan attachments
    if (!empty($email_data['attachments'])) {
      foreach ($email_data['attachments'] as $attachment) {
        $attachment_result = $this->scan_file($attachment['path']);
        if (!$attachment_result['clean']) {
          $threats = array_merge($threats, $attachment_result['threats']);
        }
      }
    }
    
    return array(
      'clean' => empty($threats),
      'threats' => $threats,
      'action' => empty($threats) ? 'deliver' : 'quarantine'
    );
  }
  
  /**
   * Scan file using ClamAV
   */
  public function scan_file($file_path) {
    if (!file_exists($file_path)) {
      return array('clean' => false, 'error' => 'File not found');
    }
    
    $command = sprintf('clamdscan --no-summary %s', escapeshellarg($file_path));
    $output = shell_exec($command);
    
    if (strpos($output, 'FOUND') !== false) {
      // Extract threat name
      preg_match('/: (.+) FOUND/', $output, $matches);
      $threat_name = isset($matches[1]) ? $matches[1] : 'Unknown threat';
      
      return array(
        'clean' => false,
        'threats' => array($threat_name),
        'output' => $output
      );
    }
    
    return array('clean' => true, 'threats' => array());
  }
}
```

### Quarantine Management

```php
/**
 * Quarantine infected files
 */
public function quarantine_file($file_path, $threat_name) {
  $quarantine_dir = get_option('baum_mail_quarantine_dir', '/var/quarantine/');
  
  if (!is_dir($quarantine_dir)) {
    wp_mkdir_p($quarantine_dir);
  }
  
  $quarantine_file = $quarantine_dir . basename($file_path) . '_' . time();
  
  // Move file to quarantine
  if (rename($file_path, $quarantine_file)) {
    // Log quarantine action
    global $wpdb;
    $wpdb->insert(
      $wpdb->prefix . 'baum_mail_quarantine',
      array(
        'original_path' => $file_path,
        'quarantine_path' => $quarantine_file,
        'threat_name' => $threat_name,
        'quarantined_at' => current_time('mysql')
      ),
      array('%s', '%s', '%s', '%s')
    );
    
    return true;
  }
  
  return false;
}
```

## Command Line Usage

### Service Management

```bash
# Check ClamAV daemon status
sudo systemctl status clamav-daemon

# Start ClamAV daemon
sudo systemctl start clamav-daemon

# Stop ClamAV daemon
sudo systemctl stop clamav-daemon

# Restart ClamAV daemon
sudo systemctl restart clamav-daemon

# Enable auto-start
sudo systemctl enable clamav-daemon

# Check freshclam (update service) status
sudo systemctl status clamav-freshclam
```

### File Scanning

```bash
# Scan single file
clamscan /path/to/file

# Scan directory recursively
clamscan -r /path/to/directory/

# Scan with daemon (faster)
clamdscan /path/to/file

# Scan directory with daemon
clamdscan --multiscan /path/to/directory/

# Scan and remove infected files
clamscan --remove /path/to/directory/

# Scan and move infected files
clamscan --move=/quarantine/ /path/to/directory/

# Scan with detailed output
clamscan -v /path/to/file

# Scan compressed files
clamscan --scan-archive /path/to/archive.zip
```

### Virus Database Management

```bash
# Update virus definitions manually
sudo freshclam

# Check database version
sigtool --info /var/lib/clamav/main.cvd

# Test database integrity
sigtool --check /var/lib/clamav/main.cvd

# Show database statistics
sigtool --stats /var/lib/clamav/main.cvd

# Create custom signature
sigtool --md5 suspicious_file > custom.hdb

# Compile custom signatures
sigtool --compile custom.hdb
```

### Performance Testing

```bash
# Test ClamAV performance
clamscan --detect-pua --scan-archive --max-filesize=100M /path/to/test/

# Benchmark scanning speed
time clamscan -r /path/to/directory/

# Memory usage monitoring
ps aux | grep clamd

# Check daemon socket
clamdscan --ping
```

### Configuration Testing

```bash
# Test ClamAV configuration
clamd --config-check

# Test freshclam configuration
freshclam --config-check

# Show configuration
clamd --help

# Test daemon connection
echo "PING" | nc -U /var/run/clamav/clamd.ctl
```

## Programmatic API Usage

### PHP Integration

```php
class BaumMail_ClamAV {
  
  private $socket_path;
  
  public function __construct() {
    $this->socket_path = get_option('baum_mail_clamav_socket', '/var/run/clamav/clamd.ctl');
  }
  
  /**
   * Scan file using ClamAV daemon
   */
  public function scan_file($file_path) {
    if (!file_exists($file_path)) {
      return new WP_Error('file_not_found', 'File does not exist');
    }
    
    // Connect to ClamAV daemon
    $socket = socket_create(AF_UNIX, SOCK_STREAM, 0);
    
    if (!socket_connect($socket, $this->socket_path)) {
      return new WP_Error('connection_failed', 'Cannot connect to ClamAV daemon');
    }
    
    // Send scan command
    $command = "SCAN " . $file_path . "\n";
    socket_write($socket, $command, strlen($command));
    
    // Read response
    $response = socket_read($socket, 1024);
    socket_close($socket);
    
    return $this->parse_scan_result($response);
  }
  
  /**
   * Scan stream data
   */
  public function scan_stream($data) {
    $socket = socket_create(AF_UNIX, SOCK_STREAM, 0);
    
    if (!socket_connect($socket, $this->socket_path)) {
      return new WP_Error('connection_failed', 'Cannot connect to ClamAV daemon');
    }
    
    // Send INSTREAM command
    socket_write($socket, "zINSTREAM\0", 10);
    
    // Send data length and data
    $data_length = pack('N', strlen($data));
    socket_write($socket, $data_length, 4);
    socket_write($socket, $data, strlen($data));
    
    // Send end marker
    socket_write($socket, pack('N', 0), 4);
    
    // Read response
    $response = socket_read($socket, 1024);
    socket_close($socket);
    
    return $this->parse_scan_result($response);
  }
  
  /**
   * Parse ClamAV scan result
   */
  private function parse_scan_result($response) {
    if (strpos($response, 'OK') !== false) {
      return array('clean' => true, 'threat' => null);
    } elseif (strpos($response, 'FOUND') !== false) {
      // Extract threat name
      preg_match('/: (.+) FOUND/', $response, $matches);
      $threat = isset($matches[1]) ? $matches[1] : 'Unknown threat';
      
      return array('clean' => false, 'threat' => $threat);
    } else {
      return new WP_Error('scan_error', 'ClamAV scan error: ' . $response);
    }
  }
  
  /**
   * Get ClamAV version and statistics
   */
  public function get_version_info() {
    $socket = socket_create(AF_UNIX, SOCK_STREAM, 0);
    
    if (!socket_connect($socket, $this->socket_path)) {
      return new WP_Error('connection_failed', 'Cannot connect to ClamAV daemon');
    }
    
    // Get version
    socket_write($socket, "VERSION\n", 8);
    $version = trim(socket_read($socket, 1024));
    
    // Get stats
    socket_write($socket, "STATS\n", 6);
    $stats = trim(socket_read($socket, 1024));
    
    socket_close($socket);
    
    return array(
      'version' => $version,
      'stats' => $stats
    );
  }
}
```

### WordPress Integration

```php
// Hook into email processing
add_filter('baum_mail_process_incoming_email', function($email_data) {
  if (get_option('baum_mail_enable_clamav', true)) {
    $scanner = new BaumMail_ClamAV();
    
    // Scan email content
    $scan_result = $scanner->scan_stream($email_data['raw_content']);
    
    if (is_wp_error($scan_result)) {
      error_log('ClamAV scan error: ' . $scan_result->get_error_message());
    } elseif (!$scan_result['clean']) {
      // Virus found - quarantine email
      $email_data['quarantined'] = true;
      $email_data['threat'] = $scan_result['threat'];
      
      // Log threat
      error_log("Virus detected in email: {$scan_result['threat']}");
      
      // Notify administrator
      wp_mail(
        get_option('admin_email'),
        'Virus Detected in Email',
        "A virus was detected: {$scan_result['threat']}"
      );
    }
  }
  
  return $email_data;
});

// Scheduled virus definition updates
add_action('baum_mail_daily_maintenance', function() {
  if (get_option('baum_mail_auto_update_virus_db', true)) {
    $output = shell_exec('freshclam 2>&1');
    
    if (strpos($output, 'updated') !== false) {
      error_log('ClamAV virus definitions updated successfully');
    }
  }
});
```

### REST API Integration

```php
// ClamAV management endpoints
add_action('rest_api_init', function() {
  register_rest_route('baum-mail/v1', '/clamav/status', array(
    'methods' => 'GET',
    'callback' => function() {
      $security = baum_mail()->get_component('security');
      return $security->get_clamav_status();
    },
    'permission_callback' => 'baum_mail_check_permissions'
  ));
  
  register_rest_route('baum-mail/v1', '/clamav/scan', array(
    'methods' => 'POST',
    'callback' => function($request) {
      $scanner = new BaumMail_ClamAV();
      
      $file_path = $request->get_param('file_path');
      $result = $scanner->scan_file($file_path);
      
      if (is_wp_error($result)) {
        return $result;
      }
      
      return $result;
    },
    'permission_callback' => 'baum_mail_check_permissions'
  ));
  
  register_rest_route('baum-mail/v1', '/clamav/update', array(
    'methods' => 'POST',
    'callback' => function() {
      $security = baum_mail()->get_component('security');
      $result = $security->update_virus_definitions();
      
      if (is_wp_error($result)) {
        return $result;
      }
      
      return array('success' => true, 'message' => 'Virus definitions updated');
    },
    'permission_callback' => 'baum_mail_check_permissions'
  ));
});
```

## End-User Usage

### Email Client Integration

**Automatic Virus Scanning:**
- ClamAV runs transparently on the server
- Infected emails are automatically quarantined
- Clean emails are delivered normally
- Users receive notifications about blocked threats

**Quarantine Management:**
1. **View Quarantined Items**: Access quarantine folder through webmail
2. **Release False Positives**: Restore legitimate files from quarantine
3. **Delete Threats**: Permanently remove confirmed threats
4. **Reporting**: View virus detection reports and statistics

### Webmail Integration

```php
// Webmail quarantine management
class BaumMail_WebmailQuarantine {
  
  /**
   * Get quarantined items for user
   */
  public function get_quarantined_items($user_email) {
    global $wpdb;
    
    $items = $wpdb->get_results($wpdb->prepare(
      "SELECT * FROM {$wpdb->prefix}baum_mail_quarantine 
       WHERE original_path LIKE %s 
       ORDER BY quarantined_at DESC",
      '%' . $user_email . '%'
    ));
    
    return $items;
  }
  
  /**
   * Release item from quarantine
   */
  public function release_item($quarantine_id) {
    global $wpdb;
    
    $item = $wpdb->get_row($wpdb->prepare(
      "SELECT * FROM {$wpdb->prefix}baum_mail_quarantine WHERE id = %d",
      $quarantine_id
    ));
    
    if ($item) {
      // Move file back to original location
      if (rename($item->quarantine_path, $item->original_path)) {
        // Remove from quarantine database
        $wpdb->delete(
          $wpdb->prefix . 'baum_mail_quarantine',
          array('id' => $quarantine_id),
          array('%d')
        );
        
        return true;
      }
    }
    
    return false;
  }
}
```

## Configuration Files

### ClamAV Daemon Configuration (/etc/clamav/clamd.conf)

```
# ClamAV Daemon Configuration for Baum Mail

# Socket settings
LocalSocket /var/run/clamav/clamd.ctl
LocalSocketGroup clamav
LocalSocketMode 666

# Process settings
User clamav
PidFile /var/run/clamav/clamd.pid
DatabaseDirectory /var/lib/clamav

# Scanning settings
MaxScanSize 100M
MaxFileSize 25M
MaxRecursion 16
MaxFiles 10000
MaxEmbeddedPE 10M
MaxHTMLNormalize 10M
MaxHTMLNoTags 2M
MaxScriptNormalize 5M
MaxZipTypeRcg 1M

# Archive scanning
ScanArchive yes
ScanPE yes
ScanELF yes
ScanOLE2 yes
ScanPDF yes
ScanSWF yes
ScanHTML yes

# Heuristic detection
HeuristicScanPrecedence yes
StructuredDataDetection yes
StructuredMinCreditCardCount 3
StructuredMinSSNCount 3

# Performance settings
ReadTimeout 180
CommandReadTimeout 5
SendBufTimeout 200
MaxQueue 100
IdleTimeout 30
ExcludePath ^/proc/
ExcludePath ^/sys/

# Logging
LogFile /var/log/clamav/clamav.log
LogTime yes
LogFileUnlock no
LogFileMaxSize 0
LogVerbose no
LogSyslog no
LogFacility LOG_LOCAL6
LogClean no
LogInfected yes

# Self protection
SelfCheck 3600
DetectPUA yes
ExcludePUA NetTool
ExcludePUA PWTool
AlgorithmicDetection yes
Bytecode yes
BytecodeSecurity TrustSigned
BytecodeTimeout 60000
```

### Freshclam Configuration (/etc/clamav/freshclam.conf)

```
# Freshclam Configuration for Baum Mail

# Database directory
DatabaseDirectory /var/lib/clamav

# Update settings
UpdateLogFile /var/log/clamav/freshclam.log
LogVerbose no
LogSyslog no
LogFacility LOG_LOCAL6
LogFileMaxSize 0
LogRotate yes
LogTime yes

# Database mirrors
DatabaseMirror db.local.clamav.net
DatabaseMirror database.clamav.net

# Update frequency
Checks 24
DNSDatabaseInfo current.cvd.clamav.net

# Connection settings
ConnectTimeout 30
ReceiveTimeout 30
TestDatabases yes
ScriptedUpdates yes
CompressLocalDatabase no

# Notification settings
NotifyClamd /etc/clamav/clamd.conf

# User settings
DatabaseOwner clamav
AllowSupplementaryGroups no

# Proxy settings (if needed)
# HTTPProxyServer myproxy.com
# HTTPProxyPort 1234
# HTTPProxyUsername myusername
# HTTPProxyPassword mypass
```

### Postfix ClamAV Integration

```
# /etc/postfix/main.cf - Milter integration
smtpd_milters = unix:/clamav/clamav-milter.ctl
non_smtpd_milters = unix:/clamav/clamav-milter.ctl
milter_default_action = tempfail
```

## Performance Optimization

### Memory Management

```
# Optimize ClamAV memory usage
MaxThreads 12
ThreadTimeout 180
ReadTimeout 180

# Database caching
DatabaseDirectory /var/lib/clamav
ScanOnAccess no
OnAccessMaxFileSize 5M
```

### Scanning Optimization

```bash
# Exclude unnecessary file types
echo "*.log" >> /etc/clamav/clamd.conf
echo "*.tmp" >> /etc/clamav/clamd.conf

# Optimize for email scanning
MaxScanSize 100M
MaxFileSize 25M
MaxRecursion 10
```

## Troubleshooting

### Common Issues

**ClamAV Daemon Not Starting:**
```bash
# Check configuration
clamd --config-check

# Check permissions
ls -la /var/run/clamav/
sudo chown clamav:clamav /var/run/clamav/

# Check logs
tail -f /var/log/clamav/clamav.log
```

**Database Update Failures:**
```bash
# Manual update
sudo freshclam

# Check network connectivity
ping database.clamav.net

# Check disk space
df -h /var/lib/clamav/
```

**High CPU/Memory Usage:**
- Reduce MaxThreads setting
- Increase scanning timeouts
- Exclude large directories from scanning
- Use SSD storage for virus database

**False Positives:**
```bash
# Report false positive to ClamAV
clamscan --gen-json --database=/var/lib/clamav/ suspicious_file

# Create custom whitelist
echo "hash:file_hash" >> /var/lib/clamav/whitelist.ign2
```

### Log Analysis

```bash
# ClamAV daemon logs
tail -f /var/log/clamav/clamav.log

# Freshclam update logs
tail -f /var/log/clamav/freshclam.log

# Scan statistics
grep "FOUND" /var/log/clamav/clamav.log | wc -l
```

## Integration with Other Services

- **Postfix**: Milter integration for email scanning
- **Amavis**: Content filtering framework
- **SpamAssassin**: Combined spam and virus filtering
- **Dovecot**: Sieve rules for quarantine management
- **Rspamd**: Alternative content filtering
- **Fail2ban**: Intrusion detection integration

# SSL/TLS Transport Encryption

## Overview

SSL/TLS (Secure Sockets Layer/Transport Layer Security) provides **transport encryption** for the Baum Mail plugin. This encrypts the connection between email clients and servers, protecting data in transit but not the message content itself once delivered. SSL/TLS is essential for secure email communication and is enabled by default in Baum Mail.

## What SSL/TLS Does

- **Transport Security**: Encrypts data transmission between client and server
- **Authentication**: Verifies server identity using certificates
- **Data Integrity**: Ensures data hasn't been tampered with during transmission
- **Connection Security**: Protects against eavesdropping and man-in-the-middle attacks
- **Protocol Support**: Secures SMTP, IMAP, POP3, and HTTP connections

## SSL vs TLS vs STARTTLS

### SSL (Secure Sockets Layer)
- **Legacy protocol** (SSL 2.0, 3.0) - **DEPRECATED**
- Security vulnerabilities discovered
- Should not be used in modern systems

### TLS (Transport Layer Security)
- **Modern replacement** for SSL (TLS 1.0, 1.1, 1.2, 1.3)
- TLS 1.2+ recommended for security
- Backward compatible with SSL terminology

### STARTTLS
- **Upgrade mechanism** that starts with plain text connection
- Upgrades to TLS encryption after initial handshake
- Used on standard ports (25, 587, 143, 110)
- Allows fallback to unencrypted if TLS fails

### SSL/TLS Wrapper Mode
- **Direct TLS connection** from the start
- Uses dedicated encrypted ports (465, 993, 995)
- No plain text communication at any point
- More secure than STARTTLS

## How Baum Mail Uses SSL/TLS

### Automatic Configuration

The Baum Mail plugin automatically configures SSL/TLS for all services:

```php
// SSL/TLS is enabled by default
$ssl_config = array(
  'smtp_ssl' => true,
  'imap_ssl' => true,
  'pop3_ssl' => true,
  'force_ssl' => get_option('baum_mail_force_ssl', true)
);
```

### Certificate Management

```php
// Get SSL component
$ssl = baum_mail()->get_component('security');

// Check SSL status
$ssl_status = $ssl->get_ssl_status();

// Generate self-signed certificate (development)
$ssl->generate_self_signed_cert($domain);

// Install Let's Encrypt certificate (production)
$ssl->install_letsencrypt_cert($domain);
```

### Service Configuration

**Postfix SMTP SSL/TLS:**
```php
// Configure Postfix for SSL/TLS
$postfix_ssl = array(
  'smtpd_tls_cert_file' => '/etc/ssl/certs/mail.crt',
  'smtpd_tls_key_file' => '/etc/ssl/private/mail.key',
  'smtpd_use_tls' => 'yes',
  'smtpd_tls_security_level' => 'may',
  'smtp_tls_security_level' => 'may'
);
```

**Dovecot IMAP/POP3 SSL/TLS:**
```php
// Configure Dovecot for SSL/TLS
$dovecot_ssl = array(
  'ssl' => 'required',
  'ssl_cert' => '</etc/ssl/certs/mail.crt',
  'ssl_key' => '</etc/ssl/private/mail.key',
  'ssl_protocols' => '!SSLv2 !SSLv3',
  'ssl_cipher_list' => 'ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES128-GCM-SHA256'
);
```

## Command Line Usage

### Certificate Generation

```bash
# Generate private key
openssl genrsa -out /etc/ssl/private/mail.key 4096

# Generate certificate signing request
openssl req -new -key /etc/ssl/private/mail.key -out /tmp/mail.csr

# Generate self-signed certificate (development only)
openssl x509 -req -days 365 -in /tmp/mail.csr -signkey /etc/ssl/private/mail.key -out /etc/ssl/certs/mail.crt

# Set proper permissions
chmod 600 /etc/ssl/private/mail.key
chmod 644 /etc/ssl/certs/mail.crt
```

### Let's Encrypt (Production)

```bash
# Install Certbot
sudo apt-get install certbot

# Generate certificate
sudo certbot certonly --standalone -d mail.yourdomain.com

# Certificate files will be in:
# /etc/letsencrypt/live/mail.yourdomain.com/fullchain.pem
# /etc/letsencrypt/live/mail.yourdomain.com/privkey.pem

# Set up auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### Certificate Testing

```bash
# Test SMTP STARTTLS
openssl s_client -connect mail.yourdomain.com:587 -starttls smtp

# Test SMTP SSL/TLS (port 465)
openssl s_client -connect mail.yourdomain.com:465

# Test IMAP SSL/TLS
openssl s_client -connect mail.yourdomain.com:993

# Test POP3 SSL/TLS
openssl s_client -connect mail.yourdomain.com:995

# Check certificate details
openssl x509 -in /etc/ssl/certs/mail.crt -text -noout

# Check certificate expiration
openssl x509 -in /etc/ssl/certs/mail.crt -noout -dates
```

### SSL/TLS Debugging

```bash
# Test SSL/TLS connection with verbose output
openssl s_client -connect mail.yourdomain.com:587 -starttls smtp -debug -msg

# Check supported ciphers
nmap --script ssl-enum-ciphers -p 587 mail.yourdomain.com

# Test SSL/TLS configuration
testssl.sh mail.yourdomain.com:587
```

## Programmatic API Usage

### PHP SSL Context

```php
// Create SSL context for secure connections
$ssl_context = stream_context_create(array(
  'ssl' => array(
    'verify_peer' => true,
    'verify_peer_name' => true,
    'allow_self_signed' => false,
    'cafile' => '/etc/ssl/certs/ca-certificates.crt'
  )
));

// Use with IMAP
$imap = imap_open(
  '{mail.yourdomain.com:993/imap/ssl}INBOX',
  $username,
  $password,
  0,
  1,
  array('DISABLE_AUTHENTICATOR' => 'GSSAPI')
);
```

### WordPress Mail Configuration

```php
// Configure WordPress to use SSL/TLS SMTP
add_action('phpmailer_init', function($phpmailer) {
  $phpmailer->isSMTP();
  $phpmailer->Host = 'mail.yourdomain.com';
  $phpmailer->SMTPAuth = true;
  $phpmailer->Username = get_option('baum_mail_smtp_username');
  $phpmailer->Password = get_option('baum_mail_smtp_password');
  
  // Use STARTTLS on port 587
  $phpmailer->Port = 587;
  $phpmailer->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
  
  // OR use SSL/TLS wrapper on port 465
  // $phpmailer->Port = 465;
  // $phpmailer->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS;
  
  // SSL/TLS options
  $phpmailer->SMTPOptions = array(
    'ssl' => array(
      'verify_peer' => true,
      'verify_peer_name' => true,
      'allow_self_signed' => false
    )
  );
});
```

### Certificate Validation

```php
class BaumMail_SSL {
  
  /**
   * Validate SSL certificate
   */
  public function validate_certificate($hostname, $port = 443) {
    $context = stream_context_create(array(
      'ssl' => array(
        'capture_peer_cert' => true,
        'verify_peer' => true,
        'verify_peer_name' => true
      )
    ));
    
    $socket = stream_socket_client(
      "ssl://{$hostname}:{$port}",
      $errno,
      $errstr,
      30,
      STREAM_CLIENT_CONNECT,
      $context
    );
    
    if (!$socket) {
      return new WP_Error('ssl_connection_failed', $errstr);
    }
    
    $cert = stream_context_get_params($socket)['options']['ssl']['peer_certificate'];
    $cert_info = openssl_x509_parse($cert);
    
    return array(
      'valid' => true,
      'subject' => $cert_info['subject'],
      'issuer' => $cert_info['issuer'],
      'valid_from' => date('Y-m-d H:i:s', $cert_info['validFrom_time_t']),
      'valid_to' => date('Y-m-d H:i:s', $cert_info['validTo_time_t'])
    );
  }
}
```

## End-User Configuration

### Mail Client SSL/TLS Settings

**SMTP (Outgoing Mail):**
- **Port 587 (STARTTLS)**: Recommended for most clients
  - Security: STARTTLS
  - Authentication: Required
- **Port 465 (SSL/TLS)**: Direct SSL connection
  - Security: SSL/TLS
  - Authentication: Required

**IMAP (Incoming Mail):**
- **Port 993 (SSL/TLS)**: Encrypted IMAP
  - Security: SSL/TLS
  - Authentication: Required
- **Port 143 (STARTTLS)**: IMAP with STARTTLS
  - Security: STARTTLS
  - Authentication: Required

**POP3 (Incoming Mail):**
- **Port 995 (SSL/TLS)**: Encrypted POP3
  - Security: SSL/TLS
  - Authentication: Required
- **Port 110 (STARTTLS)**: POP3 with STARTTLS
  - Security: STARTTLS
  - Authentication: Required

### Common Client Configuration

**Thunderbird:**
1. Account Settings → Server Settings
2. Connection security: SSL/TLS or STARTTLS
3. Authentication method: Normal password
4. Test connection

**Outlook:**
1. File → Account Settings → Change
2. More Settings → Advanced
3. Check "This server requires an encrypted connection (SSL/TLS)"
4. Set appropriate ports

**Apple Mail:**
1. Mail → Preferences → Accounts
2. Advanced → Use SSL/TLS
3. Set correct ports for SSL/TLS

## SSL/TLS vs GPG Encryption

### Key Differences

| Aspect | SSL/TLS | GPG |
|--------|---------|-----|
| **Purpose** | Transport encryption | Message content encryption |
| **Scope** | Connection between client/server | End-to-end message protection |
| **Duration** | During transmission only | Permanent message encryption |
| **Key Management** | Server certificates | Personal key pairs |
| **Transparency** | Automatic/transparent | Requires user action |
| **Standards** | X.509 certificates | OpenPGP standard |

### When to Use Each

**SSL/TLS Transport Encryption:**
- ✅ Always enabled for all connections
- ✅ Protects credentials and data in transit
- ✅ Transparent to end users
- ✅ Required for compliance (PCI DSS, HIPAA)
- ❌ Doesn't protect stored messages
- ❌ Server administrators can read messages

**GPG Message Encryption:**
- ✅ End-to-end message protection
- ✅ Messages remain encrypted when stored
- ✅ Only recipient can decrypt
- ✅ Perfect forward secrecy possible
- ❌ Requires key management
- ❌ Not transparent to users
- ❌ Compatibility issues with some clients

### Combined Security

**Best Practice: Use Both**
```
[Client] --SSL/TLS--> [Server] --SSL/TLS--> [Recipient]
    |                                           |
    +-- GPG Encrypt Message Content ------------+
```

1. **SSL/TLS** protects the transmission
2. **GPG** protects the message content
3. Even if SSL/TLS is compromised, message content remains secure
4. Even if message is intercepted, transport was still protected

## Security Best Practices

### Certificate Security
- Use certificates from trusted Certificate Authorities
- Implement Certificate Transparency monitoring
- Regular certificate rotation (annual or bi-annual)
- Use strong key lengths (2048-bit RSA minimum, 4096-bit preferred)
- Implement HSTS (HTTP Strict Transport Security) for web interfaces

### Protocol Security
- Disable SSL 2.0, SSL 3.0, TLS 1.0, TLS 1.1
- Use TLS 1.2 minimum, TLS 1.3 preferred
- Configure strong cipher suites
- Implement perfect forward secrecy
- Regular security audits and penetration testing

### Configuration Security
```
# Strong SSL/TLS configuration example
ssl_protocols = TLSv1.2 TLSv1.3
ssl_ciphers = ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384
ssl_prefer_server_ciphers = on
ssl_session_cache = shared:SSL:10m
ssl_session_timeout = 10m
```

## Troubleshooting

### Common SSL/TLS Issues

**Certificate Errors:**
- Verify certificate chain completeness
- Check certificate expiration dates
- Ensure hostname matches certificate CN/SAN
- Verify certificate authority trust

**Connection Failures:**
- Check firewall rules for SSL/TLS ports
- Verify service is listening on SSL/TLS ports
- Test with openssl s_client commands
- Review server logs for SSL/TLS errors

**Performance Issues:**
- Enable SSL session caching
- Use ECDHE ciphers for better performance
- Consider SSL/TLS acceleration hardware
- Monitor CPU usage during SSL/TLS handshakes

### Monitoring and Alerts

```php
// Monitor certificate expiration
function baum_mail_check_cert_expiration() {
  $cert_path = '/etc/ssl/certs/mail.crt';
  $cert_data = file_get_contents($cert_path);
  $cert_info = openssl_x509_parse($cert_data);
  
  $expires = $cert_info['validTo_time_t'];
  $days_until_expiry = ($expires - time()) / (24 * 60 * 60);
  
  if ($days_until_expiry < 30) {
    // Send alert email
    wp_mail(
      get_option('admin_email'),
      'SSL Certificate Expiring Soon',
      "Certificate expires in {$days_until_expiry} days"
    );
  }
}

// Schedule daily check
wp_schedule_event(time(), 'daily', 'baum_mail_check_cert_expiration');
```

## Integration with Other Services

- **Let's Encrypt**: Automated certificate management
- **Cloudflare**: SSL/TLS termination and optimization
- **Load Balancers**: SSL/TLS offloading
- **CDN Services**: SSL/TLS acceleration
- **Monitoring Tools**: Certificate expiration monitoring
- **Security Scanners**: SSL/TLS configuration testing

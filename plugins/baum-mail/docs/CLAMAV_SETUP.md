# ClamAV Setup Guide for Baum Mail

This guide will help you set up ClamAV antivirus scanning to work with the Baum Mail WordPress plugin and your email server.

## Prerequisites

- Postfix and Dovecot already configured and running
- Ubuntu/Debian Linux server with root access
- At least 2GB RAM (ClamAV can be memory intensive)
- Sufficient disk space for virus definitions

## Installation

### 1. Install ClamAV

```bash
sudo apt-get update
sudo apt-get install clamav clamav-daemon clamav-freshclam clamav-milter
```

### 2. Configure ClamAV Daemon

Edit the main ClamAV daemon configuration:

```bash
sudo nano /etc/clamav/clamd.conf
```

Key settings to configure:

```
# Basic settings
LogFile /var/log/clamav/clamav.log
LogTime yes
LogClean yes
LogSyslog yes
LogFacility LOG_LOCAL6
LogVerbose yes
ExtendedDetectionInfo yes

# Database settings
DatabaseDirectory /var/lib/clamav
OfficialDatabaseOnly no
LocalSocket /var/run/clamav/clamd.ctl
LocalSocketGroup clamav
LocalSocketMode 666

# Scanning settings
ScanMail yes
ScanArchive yes
ArchiveBlockEncrypted no
MaxDirectoryRecursion 15
FollowDirectorySymlinks no
FollowFileSymlinks no
ReadTimeout 180
MaxThreads 12
MaxConnectionQueueLength 15
LogFileMaxSize 0
LogRotate yes
LogFileUnlock no
LocalSocketMaxPending 200
TCPSocket 3310
TCPAddr 127.0.0.1

# Performance settings
MaxScanSize 100M
MaxFileSize 25M
MaxRecursion 16
MaxFiles 10000
MaxEmbeddedPE 10M
MaxHTMLNormalize 10M
MaxHTMLNoTags 2M
MaxScriptNormalize 5M
MaxZipTypeRcg 1M
MaxPartitions 50
MaxIconsPE 100
PCREMatchLimit 10000
PCRERecMatchLimit 5000

# Heuristic settings
HeuristicScanPrecedence yes
StructuredDataDetection yes
StructuredMinCreditCardCount 3
StructuredMinSSNCount 3
StructuredSSNFormatNormal yes
StructuredSSNFormatStripped yes

# Executable settings
ScanPE yes
ScanELF yes
ScanOLE2 yes
ScanPDF yes
ScanSWF yes
ScanHTML yes
```

### 3. Configure Freshclam (Virus Definition Updates)

Edit the freshclam configuration:

```bash
sudo nano /etc/clamav/freshclam.conf
```

Configure these settings:

```
# Update settings
DatabaseOwner clamav
UpdateLogFile /var/log/clamav/freshclam.log
LogVerbose yes
LogSyslog yes
LogFacility LOG_LOCAL6
LogFileMaxSize 0
LogRotate yes
LogTime yes
Foreground no
Debug no
MaxAttempts 5
DatabaseDirectory /var/lib/clamav
DNSDatabaseInfo current.cvd.clamav.net
ConnectTimeout 30
ReceiveTimeout 30
TestDatabases yes
ScriptedUpdates yes
CompressLocalDatabase no

# Update frequency
Checks 24
DatabaseMirror db.local.clamav.net
DatabaseMirror database.clamav.net

# Notification settings
NotifyClamd /etc/clamav/clamd.conf
OnUpdateExecute /etc/clamav/clamd-reload
OnErrorExecute /etc/clamav/clamd-reload
OnOutdatedExecute /etc/clamav/clamd-reload
```

### 4. Configure ClamAV Milter for Postfix Integration

Edit the milter configuration:

```bash
sudo nano /etc/clamav/clamav-milter.conf
```

Configure these settings:

```
# Milter settings
MilterSocket /var/run/clamav/clamav-milter.ctl
MilterSocketGroup postfix
MilterSocketMode 660
FixStaleSocket yes
User clamav
ReadTimeout 120
Foreground no
PidFile /var/run/clamav/clamav-milter.pid
ClamdSocket unix:/var/run/clamav/clamd.ctl
OnClean Accept
OnInfected Reject
OnFail Defer
AddHeader Replace
LogFile /var/log/clamav/clamav-milter.log
LogTime yes
LogSyslog yes
LogFacility LOG_LOCAL6
LogVerbose yes
MaxFileSize 25M

# Quarantine settings
QuarantineDir /var/lib/clamav/quarantine
```

### 5. Create Log Directory and Set Permissions

```bash
sudo mkdir -p /var/log/clamav
sudo chown clamav:clamav /var/log/clamav
sudo chmod 755 /var/log/clamav

sudo mkdir -p /var/lib/clamav/quarantine
sudo chown clamav:clamav /var/lib/clamav/quarantine
sudo chmod 755 /var/lib/clamav/quarantine
```

### 6. Update Virus Definitions

```bash
sudo freshclam
```

### 7. Configure Postfix Integration

Edit Postfix main configuration:

```bash
sudo nano /etc/postfix/main.cf
```

Add milter settings:

```
# Milter settings
milter_protocol = 6
milter_default_action = tempfail
smtpd_milters = unix:/var/run/clamav/clamav-milter.ctl
non_smtpd_milters = unix:/var/run/clamav/clamav-milter.ctl
milter_connect_timeout = 60s
milter_command_timeout = 60s
milter_content_timeout = 300s
```

### 8. Start and Enable Services

```bash
sudo systemctl start clamav-daemon
sudo systemctl enable clamav-daemon
sudo systemctl start clamav-freshclam
sudo systemctl enable clamav-freshclam
sudo systemctl start clamav-milter
sudo systemctl enable clamav-milter
```

### 9. Restart Postfix

```bash
sudo systemctl restart postfix
```

## Testing

### 1. Check Service Status

```bash
sudo systemctl status clamav-daemon
sudo systemctl status clamav-freshclam
sudo systemctl status clamav-milter
```

### 2. Test ClamAV Scanning

```bash
# Test with EICAR test virus
echo 'X5O!P%@AP[4\PZX54(P^)7CC)7}$EICAR-STANDARD-ANTIVIRUS-TEST-FILE!$H+H*' | clamdscan -
```

### 3. Check Socket Permissions

```bash
ls -la /var/run/clamav/
```

### 4. Test Email Scanning

Send a test email with the EICAR test string to verify scanning works.

### 5. Monitor Logs

```bash
sudo tail -f /var/log/clamav/clamav.log
sudo tail -f /var/log/clamav/clamav-milter.log
sudo tail -f /var/log/mail.log
```

## Configuration for Baum Mail Plugin

### 1. WordPress Settings

In the Baum Mail settings page:
- Enable ClamAV integration
- Set ClamAV socket path: `/var/run/clamav/clamd.ctl`
- Configure scan options and quarantine settings

### 2. Manual Scanning

The plugin provides manual scanning capabilities:

```php
// Scan a specific file
$result = baum_mail()->get_component('security')->scan_file('/path/to/file');

// Scan a mailbox
$result = baum_mail()->get_component('security')->scan_mailbox('<EMAIL>');
```

## Maintenance

### 1. Automatic Updates

Freshclam automatically updates virus definitions. Monitor the update process:

```bash
sudo tail -f /var/log/clamav/freshclam.log
```

### 2. Manual Updates

Force an immediate update:

```bash
sudo freshclam
```

### 3. Database Information

Check current database version:

```bash
sigtool --info /var/lib/clamav/main.cvd
sigtool --info /var/lib/clamav/daily.cvd
```

### 4. Log Rotation

Configure logrotate for ClamAV logs:

```bash
sudo nano /etc/logrotate.d/clamav
```

```
/var/log/clamav/*.log {
    daily
    missingok
    rotate 14
    compress
    notifempty
    create 640 clamav clamav
    postrotate
        /bin/kill -HUP `cat /var/run/clamav/clamd.pid 2> /dev/null` 2> /dev/null || true
        /bin/kill -HUP `cat /var/run/clamav/clamav-milter.pid 2> /dev/null` 2> /dev/null || true
    endscript
}
```

## Troubleshooting

### Common Issues

1. **High Memory Usage**
   - ClamAV can use significant RAM
   - Consider increasing server memory or adjusting MaxThreads

2. **Socket Permission Errors**
   - Ensure proper ownership and permissions on socket files
   - Check that postfix user can access ClamAV sockets

3. **Timeout Issues**
   - Increase timeout values in both ClamAV and Postfix configurations
   - Monitor scan times for large files

4. **Database Update Failures**
   - Check internet connectivity
   - Verify DNS resolution for ClamAV mirrors
   - Check disk space for database storage

### Performance Optimization

1. **Memory Settings**
```bash
# For servers with limited RAM
MaxThreads 2
MaxConnectionQueueLength 5
```

2. **File Size Limits**
```bash
# Adjust based on typical email sizes
MaxScanSize 50M
MaxFileSize 10M
```

3. **Exclude Scanning for Trusted Sources**
Configure Postfix to skip scanning for internal/trusted networks.

### Monitoring Commands

```bash
# Check ClamAV status
clamdscan --version
clamdtop

# View current connections
ss -tulpn | grep clam

# Check database age
stat /var/lib/clamav/daily.cvd

# Test milter connection
telnet localhost 3310
```

## Security Considerations

1. **Regular Updates**: Ensure automatic updates are working
2. **Quarantine Management**: Regularly review and clean quarantine directory
3. **Log Monitoring**: Monitor logs for threats and performance issues
4. **Access Control**: Restrict access to ClamAV sockets and configuration files

## Integration with Other Security Tools

ClamAV works well with:
- SpamAssassin for spam filtering
- Fail2ban for intrusion prevention
- Rspamd as an alternative to SpamAssassin
- Amavis for additional content filtering

## Next Steps

After ClamAV setup:
1. Configure SpamAssassin or Rspamd for spam filtering
2. Set up monitoring and alerting for virus detections
3. Configure backup and disaster recovery procedures
4. Implement additional security hardening measures

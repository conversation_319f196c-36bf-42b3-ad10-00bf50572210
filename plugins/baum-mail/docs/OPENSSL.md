# OpenSSL Certificate Management

## Overview

OpenSSL is a cryptographic library and toolkit that provides SSL/TLS certificate management for the Baum Mail plugin. It handles certificate generation, validation, and SSL/TLS configuration for secure email transport. OpenSSL is essential for creating and managing the certificates that enable encrypted connections between email clients and servers.

## What OpenSSL Does

- **Certificate Generation**: Creates SSL/TLS certificates and private keys
- **Certificate Signing**: Signs certificates with Certificate Authorities
- **Certificate Validation**: Verifies certificate authenticity and validity
- **Cryptographic Operations**: Provides encryption, decryption, and hashing functions
- **SSL/TLS Testing**: Tests SSL/TLS connections and configurations
- **Key Management**: Generates and manages cryptographic keys

## How Baum Mail Uses OpenSSL

### Automatic Certificate Generation

The Baum Mail plugin uses OpenSSL for automatic SSL/TLS certificate setup:

```php
// Generate SSL certificates during setup
class BaumMail_SSL {
  
  /**
   * Generate self-signed certificate for development
   */
  public function generate_self_signed_cert($hostname) {
    $cert_path = '/etc/ssl/certs/mail.crt';
    $key_path = '/etc/ssl/private/mail.key';
    
    // Generate private key
    $this->execute_command("openssl genrsa -out {$key_path} 4096");
    
    // Generate certificate
    $subject = "/C=US/ST=State/L=City/O=Organization/CN={$hostname}";
    $this->execute_command(
      "openssl req -new -x509 -key {$key_path} -out {$cert_path} -days 365 -subj '{$subject}'"
    );
    
    // Set permissions
    $this->execute_command("chmod 600 {$key_path}");
    $this->execute_command("chmod 644 {$cert_path}");
    
    return true;
  }
}
```

### Certificate Validation

```php
/**
 * Validate SSL certificate
 */
public function validate_certificate($cert_path) {
  // Check certificate validity
  $cert_info = $this->execute_command("openssl x509 -in {$cert_path} -text -noout");
  
  // Check expiration
  $end_date = $this->execute_command("openssl x509 -in {$cert_path} -noout -enddate");
  
  // Parse expiration date
  if (preg_match('/notAfter=(.+)/', $end_date, $matches)) {
    $expires = strtotime($matches[1]);
    $days_until_expiry = ($expires - time()) / (24 * 60 * 60);
    
    return array(
      'valid' => true,
      'expires' => date('Y-m-d H:i:s', $expires),
      'days_until_expiry' => round($days_until_expiry)
    );
  }
  
  return array('valid' => false);
}
```

### SSL/TLS Configuration Testing

```php
/**
 * Test SSL/TLS connection
 */
public function test_ssl_connection($hostname, $port) {
  $command = "echo 'QUIT' | openssl s_client -connect {$hostname}:{$port} -verify_return_error";
  $output = $this->execute_command($command);
  
  return array(
    'connected' => strpos($output, 'Verify return code: 0 (ok)') !== false,
    'certificate_valid' => strpos($output, 'verify error') === false,
    'output' => $output
  );
}
```

## Command Line Usage

### Certificate Generation

```bash
# Generate private key (4096-bit RSA)
openssl genrsa -out private.key 4096

# Generate private key with passphrase
openssl genrsa -aes256 -out private.key 4096

# Generate elliptic curve private key (more efficient)
openssl ecparam -genkey -name secp384r1 -out private.key

# Generate certificate signing request (CSR)
openssl req -new -key private.key -out certificate.csr

# Generate self-signed certificate
openssl req -new -x509 -key private.key -out certificate.crt -days 365

# Generate certificate and key in one command
openssl req -new -x509 -keyout private.key -out certificate.crt -days 365 -nodes
```

### Certificate Operations

```bash
# View certificate details
openssl x509 -in certificate.crt -text -noout

# Check certificate expiration
openssl x509 -in certificate.crt -noout -dates

# Verify certificate against CA
openssl verify -CAfile ca-bundle.crt certificate.crt

# Check certificate and key match
openssl x509 -noout -modulus -in certificate.crt | openssl md5
openssl rsa -noout -modulus -in private.key | openssl md5

# Convert certificate formats
openssl x509 -in certificate.crt -outform DER -out certificate.der
openssl x509 -in certificate.der -inform DER -outform PEM -out certificate.pem
```

### SSL/TLS Connection Testing

```bash
# Test HTTPS connection
openssl s_client -connect example.com:443

# Test SMTP STARTTLS
openssl s_client -connect mail.example.com:587 -starttls smtp

# Test IMAP SSL/TLS
openssl s_client -connect mail.example.com:993

# Test POP3 SSL/TLS
openssl s_client -connect mail.example.com:995

# Test with SNI (Server Name Indication)
openssl s_client -connect example.com:443 -servername example.com

# Test specific TLS version
openssl s_client -connect example.com:443 -tls1_2

# Show certificate chain
openssl s_client -connect example.com:443 -showcerts
```

### Certificate Signing Request (CSR) Operations

```bash
# Create CSR with config file
openssl req -new -key private.key -out certificate.csr -config csr.conf

# View CSR details
openssl req -in certificate.csr -text -noout

# Verify CSR signature
openssl req -in certificate.csr -verify -noout

# Extract public key from CSR
openssl req -in certificate.csr -pubkey -noout
```

### Let's Encrypt Integration

```bash
# Generate certificate with Certbot
certbot certonly --standalone -d mail.example.com

# Generate certificate with DNS challenge
certbot certonly --manual --preferred-challenges dns -d mail.example.com

# Renew certificates
certbot renew

# Test renewal
certbot renew --dry-run

# Convert Let's Encrypt certificates for use
cp /etc/letsencrypt/live/mail.example.com/fullchain.pem /etc/ssl/certs/mail.crt
cp /etc/letsencrypt/live/mail.example.com/privkey.pem /etc/ssl/private/mail.key
```

## Programmatic API Usage

### PHP OpenSSL Integration

```php
class BaumMail_OpenSSL {
  
  /**
   * Generate private key
   */
  public function generate_private_key($bits = 4096) {
    $config = array(
      'digest_alg' => 'sha256',
      'private_key_bits' => $bits,
      'private_key_type' => OPENSSL_KEYTYPE_RSA,
    );
    
    $private_key = openssl_pkey_new($config);
    
    if (!$private_key) {
      return new WP_Error('key_generation_failed', openssl_error_string());
    }
    
    openssl_pkey_export($private_key, $private_key_string);
    
    return $private_key_string;
  }
  
  /**
   * Generate certificate signing request
   */
  public function generate_csr($private_key, $subject_data) {
    $subject = array(
      'countryName' => $subject_data['country'] ?? 'US',
      'stateOrProvinceName' => $subject_data['state'] ?? 'State',
      'localityName' => $subject_data['city'] ?? 'City',
      'organizationName' => $subject_data['organization'] ?? 'Organization',
      'commonName' => $subject_data['hostname']
    );
    
    $csr = openssl_csr_new($subject, $private_key);
    
    if (!$csr) {
      return new WP_Error('csr_generation_failed', openssl_error_string());
    }
    
    openssl_csr_export($csr, $csr_string);
    
    return $csr_string;
  }
  
  /**
   * Generate self-signed certificate
   */
  public function generate_self_signed_cert($private_key, $subject_data, $days = 365) {
    $csr = $this->generate_csr($private_key, $subject_data);
    
    if (is_wp_error($csr)) {
      return $csr;
    }
    
    $cert = openssl_csr_sign($csr, null, $private_key, $days);
    
    if (!$cert) {
      return new WP_Error('cert_generation_failed', openssl_error_string());
    }
    
    openssl_x509_export($cert, $cert_string);
    
    return $cert_string;
  }
  
  /**
   * Validate certificate
   */
  public function validate_certificate($cert_data) {
    $cert = openssl_x509_read($cert_data);
    
    if (!$cert) {
      return new WP_Error('invalid_certificate', 'Invalid certificate format');
    }
    
    $cert_info = openssl_x509_parse($cert);
    
    return array(
      'subject' => $cert_info['subject'],
      'issuer' => $cert_info['issuer'],
      'valid_from' => date('Y-m-d H:i:s', $cert_info['validFrom_time_t']),
      'valid_to' => date('Y-m-d H:i:s', $cert_info['validTo_time_t']),
      'serial_number' => $cert_info['serialNumber'],
      'signature_algorithm' => $cert_info['signatureTypeSN']
    );
  }
}
```

### WordPress Integration

```php
// Automatic certificate management
add_action('baum_mail_daily_maintenance', function() {
  $ssl = baum_mail()->get_component('ssl');
  
  // Check certificate expiration
  $cert_info = $ssl->validate_certificate('/etc/ssl/certs/mail.crt');
  
  if (!is_wp_error($cert_info)) {
    $expires = strtotime($cert_info['valid_to']);
    $days_until_expiry = ($expires - time()) / (24 * 60 * 60);
    
    // Alert if certificate expires within 30 days
    if ($days_until_expiry < 30) {
      wp_mail(
        get_option('admin_email'),
        'SSL Certificate Expiring Soon',
        "Your mail server SSL certificate expires in {$days_until_expiry} days."
      );
    }
    
    // Auto-renew Let's Encrypt certificates
    if ($days_until_expiry < 7 && get_option('baum_mail_auto_renew_ssl', false)) {
      $ssl->renew_letsencrypt_certificate();
    }
  }
});
```

### REST API Integration

```php
// SSL certificate management endpoints
add_action('rest_api_init', function() {
  register_rest_route('baum-mail/v1', '/ssl/status', array(
    'methods' => 'GET',
    'callback' => function() {
      $ssl = baum_mail()->get_component('ssl');
      return $ssl->get_certificate_status();
    },
    'permission_callback' => 'baum_mail_check_permissions'
  ));
  
  register_rest_route('baum-mail/v1', '/ssl/generate', array(
    'methods' => 'POST',
    'callback' => function($request) {
      $ssl = baum_mail()->get_component('ssl');
      
      $hostname = $request->get_param('hostname');
      $result = $ssl->generate_self_signed_cert($hostname);
      
      if (is_wp_error($result)) {
        return $result;
      }
      
      return array('success' => true, 'message' => 'Certificate generated successfully');
    },
    'permission_callback' => 'baum_mail_check_permissions'
  ));
});
```

## End-User Usage

### Certificate Installation

**Manual Certificate Installation:**
1. Obtain certificate from Certificate Authority
2. Upload certificate and private key to server
3. Configure Postfix and Dovecot to use certificates
4. Restart email services
5. Test connections with email clients

**Let's Encrypt Certificate:**
1. Install Certbot on server
2. Run certificate generation command
3. Configure automatic renewal
4. Update email server configurations
5. Test SSL/TLS connections

### Email Client Configuration

**Verifying SSL/TLS in Email Clients:**

**Thunderbird:**
1. Account Settings → Server Settings
2. Connection security: SSL/TLS
3. Click "Advanced" to view certificate details
4. Verify certificate information

**Outlook:**
1. File → Account Settings → Change
2. More Settings → Advanced
3. Check SSL/TLS settings
4. Test account settings to verify connection

**Apple Mail:**
1. Mail → Preferences → Accounts
2. Advanced → Use SSL/TLS
3. Connection Doctor to test SSL/TLS

### Certificate Troubleshooting

**Common Certificate Issues:**

**Self-Signed Certificate Warnings:**
- Email clients will show security warnings
- Users must manually accept certificate
- Not recommended for production use

**Certificate Name Mismatch:**
- Certificate CN must match server hostname
- Use Subject Alternative Names (SAN) for multiple domains
- Configure email clients with correct server names

**Expired Certificates:**
- Monitor certificate expiration dates
- Set up automatic renewal for Let's Encrypt
- Plan certificate replacement before expiration

## Configuration Examples

### OpenSSL Configuration File (openssl.cnf)

```ini
[req]
default_bits = 4096
default_keyfile = private.key
distinguished_name = req_distinguished_name
req_extensions = v3_req
prompt = no

[req_distinguished_name]
C = US
ST = State
L = City
O = Organization
OU = IT Department
CN = mail.example.com

[v3_req]
basicConstraints = CA:FALSE
keyUsage = nonRepudiation, digitalSignature, keyEncipherment
subjectAltName = @alt_names

[alt_names]
DNS.1 = mail.example.com
DNS.2 = smtp.example.com
DNS.3 = imap.example.com
DNS.4 = pop3.example.com
IP.1 = *************
```

### Certificate Signing Request with SAN

```bash
# Generate CSR with Subject Alternative Names
openssl req -new -key private.key -out certificate.csr -config openssl.cnf

# Generate self-signed certificate with SAN
openssl req -new -x509 -key private.key -out certificate.crt -days 365 -config openssl.cnf -extensions v3_req
```

### Postfix SSL/TLS Configuration

```
# /etc/postfix/main.cf
smtpd_tls_cert_file = /etc/ssl/certs/mail.crt
smtpd_tls_key_file = /etc/ssl/private/mail.key
smtpd_tls_CAfile = /etc/ssl/certs/ca-bundle.crt
smtpd_use_tls = yes
smtpd_tls_security_level = may
smtp_tls_security_level = may
smtpd_tls_protocols = !SSLv2, !SSLv3, !TLSv1, !TLSv1.1
smtpd_tls_ciphers = high
smtpd_tls_exclude_ciphers = aNULL, MD5, DES, 3DES, DES-CBC3-SHA, RC4-SHA, AES256-SHA, AES128-SHA
```

### Dovecot SSL/TLS Configuration

```
# /etc/dovecot/conf.d/10-ssl.conf
ssl = required
ssl_cert = </etc/ssl/certs/mail.crt
ssl_key = </etc/ssl/private/mail.key
ssl_ca = </etc/ssl/certs/ca-bundle.crt
ssl_protocols = !SSLv3 !TLSv1 !TLSv1.1
ssl_cipher_list = ECDHE+AESGCM:ECDHE+AES256:ECDHE+AES128:!aNULL:!MD5:!DSS
ssl_prefer_server_ciphers = yes
ssl_dh_parameters_length = 2048
```

## Security Best Practices

### Key Security
- Use strong key lengths (4096-bit RSA minimum)
- Protect private keys with proper file permissions (600)
- Store private keys in secure locations
- Use hardware security modules (HSM) for high-security environments
- Regular key rotation (every 2-3 years)

### Certificate Security
- Use certificates from trusted Certificate Authorities
- Implement Certificate Transparency monitoring
- Use Subject Alternative Names for multiple domains
- Monitor certificate expiration dates
- Implement proper certificate chain validation

### SSL/TLS Configuration
- Disable weak protocols (SSL 2.0, 3.0, TLS 1.0, 1.1)
- Use strong cipher suites
- Enable Perfect Forward Secrecy
- Implement HSTS for web interfaces
- Regular security audits and penetration testing

## Troubleshooting

### Common OpenSSL Issues

**Certificate Verification Failed:**
```bash
# Check certificate chain
openssl verify -CAfile ca-bundle.crt certificate.crt

# Check certificate against private key
openssl x509 -noout -modulus -in certificate.crt | openssl md5
openssl rsa -noout -modulus -in private.key | openssl md5
```

**SSL/TLS Connection Errors:**
```bash
# Test connection with detailed output
openssl s_client -connect mail.example.com:587 -starttls smtp -debug

# Check supported protocols and ciphers
nmap --script ssl-enum-ciphers -p 587 mail.example.com
```

**Performance Issues:**
- Enable SSL session caching
- Use ECDHE ciphers for better performance
- Consider SSL/TLS acceleration hardware
- Monitor CPU usage during SSL/TLS handshakes

### Log Analysis

```bash
# Common SSL/TLS error patterns
grep -i "ssl\|tls" /var/log/mail.log
grep -i "certificate" /var/log/mail.log
grep -i "handshake" /var/log/mail.log
```

## Integration with Other Services

- **Let's Encrypt**: Automated certificate management
- **Certificate Authorities**: Commercial SSL certificates
- **Load Balancers**: SSL/TLS termination
- **CDN Services**: SSL/TLS acceleration
- **Monitoring Tools**: Certificate expiration monitoring
- **Hardware Security Modules**: Secure key storage

# SpamAssassin Setup Guide for Baum Mail

This guide will help you set up SpamAssassin spam filtering to work with the Baum Mail WordPress plugin and your email server.

## Prerequisites

- Postfix and Dovecot already configured and running
- ClamAV installed and configured (recommended)
- Ubuntu/Debian Linux server with root access
- At least 1GB RAM available for SpamAssassin
- Perl modules and dependencies

## Installation

### 1. Install SpamAssassin

```bash
sudo apt-get update
sudo apt-get install spamassassin spamc spamass-milter
```

### 2. Install Additional Perl Modules

```bash
sudo apt-get install libmail-spf-perl libmail-dkim-perl libdbd-mysql-perl
sudo cpan install Mail::SpamAssassin::Plugin::Rule2XSBody
sudo cpan install Mail::SpamAssassin::Plugin::DCC
sudo cpan install Mail::SpamAssassin::Plugin::Pyzor
sudo cpan install Mail::SpamAssassin::Plugin::Razor2::Client
```

### 3. Configure SpamAssassin

Edit the main SpamAssassin configuration:

```bash
sudo nano /etc/spamassassin/local.cf
```

Add these settings:

```
# Basic settings
required_score 5.0
report_safe 0
rewrite_header Subject [SPAM]
use_bayes 1
use_bayes_rules 1
bayes_auto_learn 1
bayes_auto_learn_threshold_nonspam 0.1
bayes_auto_learn_threshold_spam 12.0

# Network tests
skip_rbl_checks 0
use_razor2 1
use_pyzor 1
use_dcc 1
use_auto_whitelist 1

# Performance settings
bayes_ignore_header X-Bogosity
bayes_ignore_header X-Spam-Flag
bayes_ignore_header X-Spam-Status

# Custom rules directory
include /etc/spamassassin/custom.cf

# Whitelist settings
whitelist_from *@yourdomain.com
whitelist_from_rcvd *@yourdomain.com yourdomain.com

# Blacklist settings
blacklist_from *@spam-domain.com

# Language settings
ok_languages en
ok_locales en

# DNS settings
dns_available yes
dns_test_interval 600
dns_options bgsend

# Trusted networks (adjust for your network)
trusted_networks *********/8 10.0.0.0/8 **********/12 ***********/16

# AWL (Auto-Whitelist) settings
use_auto_whitelist 1
auto_whitelist_factor 0.5
auto_whitelist_ipv4_mask_len 16
auto_whitelist_ipv6_mask_len 48

# Bayes database settings
bayes_path /var/lib/spamassassin/.spamassassin/bayes
bayes_file_mode 0660
bayes_store_module Mail::SpamAssassin::BayesStore::MySQL
bayes_sql_dsn DBI:mysql:spamassassin:localhost
bayes_sql_username spamassassin
bayes_sql_password your_password_here

# Learning settings
bayes_learn_during_report 1
bayes_learn_to_journal 1

# Plugin settings
loadplugin Mail::SpamAssassin::Plugin::SPF
loadplugin Mail::SpamAssassin::Plugin::DKIM
loadplugin Mail::SpamAssassin::Plugin::DCC
loadplugin Mail::SpamAssassin::Plugin::Pyzor
loadplugin Mail::SpamAssassin::Plugin::Razor2
loadplugin Mail::SpamAssassin::Plugin::AutoLearnThreshold
loadplugin Mail::SpamAssassin::Plugin::WhiteListSubject
loadplugin Mail::SpamAssassin::Plugin::MIMEHeader
loadplugin Mail::SpamAssassin::Plugin::ReplaceTags
```

### 4. Create Custom Rules File

```bash
sudo nano /etc/spamassassin/custom.cf
```

Add custom rules:

```
# Custom scoring adjustments
score BAYES_99 3.5
score BAYES_95 2.5
score BAYES_80 1.5
score URIBL_BLACK 2.5
score RAZOR2_CHECK 1.5
score PYZOR_CHECK 1.5

# Custom rules for common spam patterns
header LOCAL_SPAM_SUBJECT Subject =~ /\b(viagra|cialis|pharmacy|casino|lottery|winner)\b/i
describe LOCAL_SPAM_SUBJECT Contains common spam keywords
score LOCAL_SPAM_SUBJECT 2.0

body LOCAL_SPAM_BODY /\b(click here|act now|limited time|urgent|congratulations)\b/i
describe LOCAL_SPAM_BODY Contains spam phrases in body
score LOCAL_SPAM_BODY 1.5

# Whitelist legitimate services
whitelist_from <EMAIL>
whitelist_from *@amazon.com
whitelist_from *@github.com

# Custom header rules
header LOCAL_MISSING_DATE exists:Date
describe LOCAL_MISSING_DATE Missing Date header
score LOCAL_MISSING_DATE 1.0
```

### 5. Configure SpamAssassin Daemon

Edit the daemon configuration:

```bash
sudo nano /etc/default/spamassassin
```

Configure these settings:

```
# Enable SpamAssassin daemon
ENABLED=1

# Options for the daemon
OPTIONS="--create-prefs --max-children 5 --helper-home-dir"

# PID file location
PIDFILE="/var/run/spamd.pid"

# Additional options
CRON=1
NICE="--nicelevel 15"
```

### 6. Configure Spamass-Milter

Edit the milter configuration:

```bash
sudo nano /etc/default/spamass-milter
```

Configure these settings:

```
# Enable spamass-milter
ENABLED=1

# Socket for communication with Postfix
OPTIONS="-p /var/run/spamass-milter/spamass-milter.sock -f -r 15 -u spamass-milter -g spamass-milter"

# Additional options
SOCKET="/var/run/spamass-milter/spamass-milter.sock"
SOCKETOWNER="postfix:postfix"
SOCKETMODE="660"
```

### 7. Create Database for Bayes (Optional but Recommended)

Create MySQL database for Bayes learning:

```bash
mysql -u root -p
```

```sql
CREATE DATABASE spamassassin;
CREATE USER 'spamassassin'@'localhost' IDENTIFIED BY 'your_password_here';
GRANT ALL PRIVILEGES ON spamassassin.* TO 'spamassassin'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

Initialize Bayes database:

```bash
sudo sa-learn --sync
```

### 8. Configure Postfix Integration

Edit Postfix main configuration:

```bash
sudo nano /etc/postfix/main.cf
```

Add or modify milter settings:

```
# Milter settings (add to existing milter configuration)
smtpd_milters = unix:/var/run/clamav/clamav-milter.ctl, unix:/var/run/spamass-milter/spamass-milter.sock
non_smtpd_milters = unix:/var/run/clamav/clamav-milter.ctl, unix:/var/run/spamass-milter/spamass-milter.sock
milter_connect_timeout = 60s
milter_command_timeout = 60s
milter_content_timeout = 300s
milter_default_action = tempfail
```

### 9. Set Up Directories and Permissions

```bash
sudo mkdir -p /var/run/spamass-milter
sudo chown spamass-milter:spamass-milter /var/run/spamass-milter
sudo chmod 755 /var/run/spamass-milter

sudo mkdir -p /var/lib/spamassassin/.spamassassin
sudo chown debian-spamd:debian-spamd /var/lib/spamassassin/.spamassassin
sudo chmod 755 /var/lib/spamassassin/.spamassassin
```

### 10. Update SpamAssassin Rules

```bash
sudo sa-update
sudo systemctl restart spamassassin
```

### 11. Start and Enable Services

```bash
sudo systemctl start spamassassin
sudo systemctl enable spamassassin
sudo systemctl start spamass-milter
sudo systemctl enable spamass-milter
sudo systemctl restart postfix
```

## Testing

### 1. Check Service Status

```bash
sudo systemctl status spamassassin
sudo systemctl status spamass-milter
```

### 2. Test SpamAssassin Directly

```bash
# Test with GTUBE (Generic Test for Unsolicited Bulk Email)
echo "XJS*C4JDBQADN1.NSBN3*2IDNEN*GTUBE-STANDARD-ANTI-UBE-TEST-EMAIL*C.34X" | spamassassin -D
```

### 3. Check Socket Permissions

```bash
ls -la /var/run/spamass-milter/
```

### 4. Test Email Processing

Send test emails and check headers for SpamAssassin processing.

### 5. Monitor Logs

```bash
sudo tail -f /var/log/mail.log
sudo tail -f /var/log/syslog | grep spamd
```

## Bayes Learning

### 1. Train Bayes with Existing Mail

```bash
# Learn spam
sudo sa-learn --spam /path/to/spam/mailbox
sudo sa-learn --spam /var/mail/spam-folder/

# Learn ham (legitimate email)
sudo sa-learn --ham /path/to/ham/mailbox
sudo sa-learn --ham /var/mail/inbox/

# Check Bayes database
sudo sa-learn --dump magic
```

### 2. Automated Learning

Create a script for automated learning:

```bash
sudo nano /usr/local/bin/sa-learn-auto.sh
```

```bash
#!/bin/bash
# Automated SpamAssassin learning script

# Learn from user-reported spam
if [ -d "/var/mail/reported-spam" ]; then
    sa-learn --spam /var/mail/reported-spam/*
    rm -f /var/mail/reported-spam/*
fi

# Learn from user-reported ham
if [ -d "/var/mail/reported-ham" ]; then
    sa-learn --ham /var/mail/reported-ham/*
    rm -f /var/mail/reported-ham/*
fi

# Sync database
sa-learn --sync

# Update rules
sa-update --nogpg
```

Make executable and add to cron:

```bash
sudo chmod +x /usr/local/bin/sa-learn-auto.sh
sudo crontab -e
```

Add:

```
# Run SpamAssassin learning daily at 2 AM
0 2 * * * /usr/local/bin/sa-learn-auto.sh
```

## Configuration for Baum Mail Plugin

### 1. WordPress Settings

In the Baum Mail settings page:
- Enable SpamAssassin integration
- Set SpamAssassin socket path
- Configure spam threshold scores
- Set up learning directories

### 2. Programmatic Access

The plugin provides SpamAssassin integration:

```php
// Check spam score
$result = baum_mail()->get_component('security')->check_spam($email_content);

// Learn spam/ham
baum_mail()->get_component('security')->learn_spam($email_content);
baum_mail()->get_component('security')->learn_ham($email_content);
```

## Maintenance

### 1. Regular Rule Updates

```bash
# Update rules manually
sudo sa-update
sudo systemctl restart spamassassin

# Check for updates
sudo sa-update --checkonly
```

### 2. Bayes Database Maintenance

```bash
# Check Bayes statistics
sudo sa-learn --dump magic

# Backup Bayes database
sudo sa-learn --backup > /backup/bayes-backup.txt

# Restore Bayes database
sudo sa-learn --restore /backup/bayes-backup.txt
```

### 3. Performance Monitoring

```bash
# Check SpamAssassin performance
sudo spamassassin --lint

# Monitor memory usage
ps aux | grep spamd

# Check processing times
grep "spamd: processing message" /var/log/mail.log
```

## Troubleshooting

### Common Issues

1. **High CPU Usage**
   - Reduce max-children in daemon options
   - Disable expensive rules if needed
   - Consider using Rspamd as alternative

2. **Socket Permission Errors**
   - Check ownership and permissions on socket files
   - Ensure postfix user can access spamass-milter socket

3. **Bayes Learning Issues**
   - Check database permissions
   - Verify MySQL connection if using SQL storage
   - Ensure sufficient training data (200+ spam, 200+ ham)

4. **Rule Update Failures**
   - Check internet connectivity
   - Verify GPG keys for rule updates
   - Check disk space

### Performance Optimization

1. **Memory Settings**
```bash
# For limited memory servers
OPTIONS="--create-prefs --max-children 2 --helper-home-dir"
```

2. **Rule Optimization**
```bash
# Disable expensive rules
score RAZOR2_CHECK 0
score PYZOR_CHECK 0
score DCC_CHECK 0
```

3. **Network Test Optimization**
```bash
# Reduce DNS timeouts
dns_query_restriction allow
rbl_timeout 10
```

## Alternative: Rspamd

For better performance, consider Rspamd:

```bash
# Install Rspamd
sudo apt-get install rspamd

# Basic configuration
sudo nano /etc/rspamd/local.d/worker-normal.conf
```

```
bind_socket = "localhost:11333";
milter = yes;
timeout = 120s;
upstream "local" {
  default = yes;
  hosts = "localhost";
}
```

## Security Considerations

1. **Regular Updates**: Keep SpamAssassin rules updated
2. **Bayes Protection**: Secure Bayes database from unauthorized access
3. **Resource Limits**: Monitor CPU and memory usage
4. **Log Monitoring**: Watch for processing errors and performance issues

## Integration with Other Tools

SpamAssassin works well with:
- ClamAV for antivirus scanning
- Postfix for mail transport
- Dovecot for mail delivery
- Amavis for additional content filtering
- Fail2ban for IP blocking based on spam patterns

## Next Steps

After SpamAssassin setup:
1. Train Bayes classifier with your email data
2. Fine-tune scoring rules for your environment
3. Set up automated learning processes
4. Monitor performance and adjust settings
5. Consider implementing user-level spam controls

# Network Ports Used by Email Services

## Overview

The Baum Mail plugin uses various network ports for different email services and protocols. Understanding these ports is crucial for proper firewall configuration, network security, and troubleshooting connectivity issues. This document covers all ports used by the email system components.

## Standard Email Ports

### SMTP (Simple Mail Transfer Protocol)

**Port 25 - SMTP (Unencrypted)**
- **Purpose**: Standard SMTP for server-to-server communication
- **Security**: Unencrypted (plain text)
- **Usage**: Mail server to mail server delivery
- **Firewall**: Should be open for incoming connections from other mail servers
- **Client Usage**: Not recommended for client connections

**Port 587 - SMTP Submission (STARTTLS)**
- **Purpose**: Mail submission from clients to server
- **Security**: STARTTLS encryption (upgrades to TLS)
- **Usage**: Email clients sending outbound mail
- **Firewall**: Open for authenticated users
- **Recommended**: Primary port for client SMTP connections

**Port 465 - SMTPS (SSL/TLS Wrapper)**
- **Purpose**: SMTP over SSL/TLS from the start
- **Security**: Full SSL/TLS encryption
- **Usage**: Legacy secure SMTP (still widely used)
- **Firewall**: Open for authenticated users
- **Note**: Direct SSL connection, no plain text

### IMAP (Internet Message Access Protocol)

**Port 143 - IMAP (STARTTLS)**
- **Purpose**: IMAP with optional STARTTLS encryption
- **Security**: Can upgrade to TLS encryption
- **Usage**: Email clients accessing mailboxes
- **Firewall**: Open for authenticated users
- **Recommendation**: Use with STARTTLS enabled

**Port 993 - IMAPS (SSL/TLS)**
- **Purpose**: IMAP over SSL/TLS
- **Security**: Full SSL/TLS encryption from start
- **Usage**: Secure email client access
- **Firewall**: Open for authenticated users
- **Recommended**: Primary port for IMAP connections

### POP3 (Post Office Protocol 3)

**Port 110 - POP3 (STARTTLS)**
- **Purpose**: POP3 with optional STARTTLS encryption
- **Security**: Can upgrade to TLS encryption
- **Usage**: Email clients downloading messages
- **Firewall**: Open for authenticated users
- **Note**: Downloads and typically deletes messages

**Port 995 - POP3S (SSL/TLS)**
- **Purpose**: POP3 over SSL/TLS
- **Security**: Full SSL/TLS encryption from start
- **Usage**: Secure email client download
- **Firewall**: Open for authenticated users
- **Recommended**: If using POP3, use this port

## How Baum Mail Uses Ports

### Port Configuration Management

```php
// Port configuration in Baum Mail
class BaumMail_Ports {
  
  /**
   * Get default port configuration
   */
  public function get_default_ports() {
    return array(
      'smtp' => array(
        'plain' => 25,
        'submission' => 587,
        'ssl' => 465
      ),
      'imap' => array(
        'plain' => 143,
        'ssl' => 993
      ),
      'pop3' => array(
        'plain' => 110,
        'ssl' => 995
      ),
      'web' => array(
        'http' => 80,
        'https' => 443
      ),
      'admin' => array(
        'ssh' => 22,
        'mysql' => 3306
      )
    );
  }
  
  /**
   * Check if port is open and listening
   */
  public function check_port($host, $port, $timeout = 5) {
    $connection = @fsockopen($host, $port, $errno, $errstr, $timeout);
    
    if (is_resource($connection)) {
      fclose($connection);
      return true;
    }
    
    return false;
  }
  
  /**
   * Get listening ports on server
   */
  public function get_listening_ports() {
    $output = shell_exec('netstat -tlnp 2>/dev/null | grep LISTEN');
    $ports = array();
    
    if ($output) {
      $lines = explode("\n", trim($output));
      foreach ($lines as $line) {
        if (preg_match('/:(\d+)\s+.*LISTEN/', $line, $matches)) {
          $ports[] = intval($matches[1]);
        }
      }
    }
    
    return array_unique($ports);
  }
}
```

### Service Port Monitoring

```php
/**
 * Monitor email service ports
 */
public function monitor_email_ports() {
  $ports_to_check = array(
    25 => 'SMTP',
    587 => 'SMTP Submission',
    465 => 'SMTPS',
    143 => 'IMAP',
    993 => 'IMAPS',
    110 => 'POP3',
    995 => 'POP3S'
  );
  
  $status = array();
  
  foreach ($ports_to_check as $port => $service) {
    $status[$port] = array(
      'service' => $service,
      'listening' => $this->check_port('localhost', $port),
      'external' => $this->check_port($_SERVER['SERVER_ADDR'], $port)
    );
  }
  
  return $status;
}
```

## Command Line Usage

### Port Checking Commands

```bash
# Check if specific port is listening
netstat -tlnp | grep :587

# Check all listening ports
netstat -tlnp | grep LISTEN

# Check specific service ports
ss -tlnp | grep -E ':(25|587|465|143|993|110|995)'

# Test port connectivity
telnet mail.example.com 587

# Test with timeout
timeout 5 bash -c "</dev/tcp/mail.example.com/587"

# Check port with nmap
nmap -p 587 mail.example.com

# Check multiple ports
nmap -p 25,587,465,143,993,110,995 mail.example.com
```

### Firewall Configuration

```bash
# UFW (Ubuntu Firewall) configuration
sudo ufw allow 25/tcp    # SMTP
sudo ufw allow 587/tcp   # SMTP Submission
sudo ufw allow 465/tcp   # SMTPS
sudo ufw allow 143/tcp   # IMAP
sudo ufw allow 993/tcp   # IMAPS
sudo ufw allow 110/tcp   # POP3
sudo ufw allow 995/tcp   # POP3S

# iptables configuration
sudo iptables -A INPUT -p tcp --dport 25 -j ACCEPT
sudo iptables -A INPUT -p tcp --dport 587 -j ACCEPT
sudo iptables -A INPUT -p tcp --dport 465 -j ACCEPT
sudo iptables -A INPUT -p tcp --dport 143 -j ACCEPT
sudo iptables -A INPUT -p tcp --dport 993 -j ACCEPT
sudo iptables -A INPUT -p tcp --dport 110 -j ACCEPT
sudo iptables -A INPUT -p tcp --dport 995 -j ACCEPT

# Save iptables rules
sudo iptables-save > /etc/iptables/rules.v4
```

### Service Port Configuration

```bash
# Check Postfix port configuration
sudo postconf | grep inet_interfaces
sudo postconf | grep smtp.*_port

# Check Dovecot port configuration
sudo doveconf | grep port
sudo doveconf | grep listen

# Restart services after port changes
sudo systemctl restart postfix
sudo systemctl restart dovecot
```

## Programmatic API Usage

### Port Testing and Monitoring

```php
class BaumMail_PortMonitor {
  
  /**
   * Test SMTP connection on specific port
   */
  public function test_smtp_port($host, $port, $use_ssl = false) {
    $context = stream_context_create();
    
    if ($use_ssl) {
      stream_context_set_option($context, 'ssl', 'verify_peer', false);
      stream_context_set_option($context, 'ssl', 'verify_peer_name', false);
      $host = "ssl://{$host}";
    }
    
    $connection = stream_socket_client(
      "{$host}:{$port}",
      $errno,
      $errstr,
      10,
      STREAM_CLIENT_CONNECT,
      $context
    );
    
    if (!$connection) {
      return array(
        'success' => false,
        'error' => "Connection failed: {$errstr} ({$errno})"
      );
    }
    
    // Read SMTP greeting
    $greeting = fgets($connection);
    
    // Send QUIT command
    fwrite($connection, "QUIT\r\n");
    $response = fgets($connection);
    
    fclose($connection);
    
    return array(
      'success' => true,
      'greeting' => trim($greeting),
      'quit_response' => trim($response)
    );
  }
  
  /**
   * Test IMAP connection
   */
  public function test_imap_port($host, $port, $use_ssl = false) {
    $mailbox = $use_ssl ? 
      "{{$host}:{$port}/imap/ssl/novalidate-cert}" : 
      "{{$host}:{$port}/imap/novalidate-cert}";
    
    // Test connection without authentication
    $connection = @imap_open($mailbox, '', '', OP_HALFOPEN);
    
    if ($connection) {
      $check = imap_check($connection);
      imap_close($connection);
      
      return array(
        'success' => true,
        'driver' => $check->Driver ?? 'Unknown'
      );
    }
    
    return array(
      'success' => false,
      'error' => imap_last_error()
    );
  }
  
  /**
   * Comprehensive port health check
   */
  public function health_check() {
    $results = array();
    
    // SMTP ports
    $results['smtp_25'] = $this->test_smtp_port('localhost', 25);
    $results['smtp_587'] = $this->test_smtp_port('localhost', 587);
    $results['smtp_465'] = $this->test_smtp_port('localhost', 465, true);
    
    // IMAP ports
    $results['imap_143'] = $this->test_imap_port('localhost', 143);
    $results['imap_993'] = $this->test_imap_port('localhost', 993, true);
    
    // POP3 ports (basic connectivity test)
    $results['pop3_110'] = $this->test_port_connectivity('localhost', 110);
    $results['pop3_995'] = $this->test_port_connectivity('localhost', 995);
    
    return $results;
  }
  
  /**
   * Basic port connectivity test
   */
  private function test_port_connectivity($host, $port) {
    $connection = @fsockopen($host, $port, $errno, $errstr, 5);
    
    if ($connection) {
      fclose($connection);
      return array('success' => true);
    }
    
    return array(
      'success' => false,
      'error' => "{$errstr} ({$errno})"
    );
  }
}
```

### WordPress Integration

```php
// Add port monitoring to WordPress admin
add_action('wp_dashboard_setup', function() {
  wp_add_dashboard_widget(
    'baum_mail_port_status',
    'Email Port Status',
    'baum_mail_port_status_widget'
  );
});

function baum_mail_port_status_widget() {
  $monitor = new BaumMail_PortMonitor();
  $results = $monitor->health_check();
  
  echo '<table class="widefat">';
  echo '<thead><tr><th>Service</th><th>Port</th><th>Status</th></tr></thead>';
  echo '<tbody>';
  
  foreach ($results as $service => $result) {
    $status = $result['success'] ? 
      '<span style="color: green;">✓ Online</span>' : 
      '<span style="color: red;">✗ Offline</span>';
    
    $port = preg_replace('/.*_(\d+)$/', '$1', $service);
    $service_name = str_replace('_', ' ', strtoupper($service));
    
    echo "<tr><td>{$service_name}</td><td>{$port}</td><td>{$status}</td></tr>";
  }
  
  echo '</tbody></table>';
}
```

## End-User Configuration

### Email Client Port Settings

**Recommended Secure Configuration:**

**Outgoing Mail (SMTP):**
- Server: mail.yourdomain.com
- Port: 587 (STARTTLS) or 465 (SSL/TLS)
- Security: STARTTLS or SSL/TLS
- Authentication: Required

**Incoming Mail (IMAP):**
- Server: mail.yourdomain.com
- Port: 993 (SSL/TLS) or 143 (STARTTLS)
- Security: SSL/TLS or STARTTLS
- Authentication: Required

**Incoming Mail (POP3):**
- Server: mail.yourdomain.com
- Port: 995 (SSL/TLS) or 110 (STARTTLS)
- Security: SSL/TLS or STARTTLS
- Authentication: Required

### Common Client Configurations

**Thunderbird:**
```
Incoming Server (IMAP):
- Server: mail.yourdomain.com
- Port: 993
- Connection security: SSL/TLS
- Authentication method: Normal password

Outgoing Server (SMTP):
- Server: mail.yourdomain.com
- Port: 587
- Connection security: STARTTLS
- Authentication method: Normal password
```

**Outlook:**
```
Incoming mail server (IMAP): mail.yourdomain.com:993 (SSL)
Outgoing mail server (SMTP): mail.yourdomain.com:587 (STARTTLS)
```

**Apple Mail:**
```
Incoming Mail Server:
- Host Name: mail.yourdomain.com
- Port: 993
- Use SSL: Yes

Outgoing Mail Server:
- Host Name: mail.yourdomain.com
- Port: 587
- Use SSL: Yes (STARTTLS)
```

## Security Considerations

### Port Security Best Practices

**Firewall Configuration:**
- Only open necessary ports
- Restrict access by IP when possible
- Use fail2ban for brute force protection
- Monitor port access logs regularly

**SSL/TLS Encryption:**
- Always use encrypted ports for client connections
- Disable unencrypted protocols when possible
- Use strong SSL/TLS configurations
- Regular certificate updates

**Access Control:**
- Implement authentication for all client ports
- Use strong passwords and consider 2FA
- Limit connection attempts
- Monitor for suspicious activity

### Port-Specific Security

```bash
# Secure SMTP configuration
# /etc/postfix/main.cf
smtpd_tls_security_level = may
smtp_tls_security_level = may
smtpd_tls_auth_only = yes

# Secure Dovecot configuration
# /etc/dovecot/conf.d/10-ssl.conf
ssl = required
ssl_protocols = !SSLv3 !TLSv1 !TLSv1.1

# Disable plain text authentication
# /etc/dovecot/conf.d/10-auth.conf
disable_plaintext_auth = yes
```

## Troubleshooting Port Issues

### Common Port Problems

**Port Not Listening:**
```bash
# Check if service is running
systemctl status postfix
systemctl status dovecot

# Check configuration
postconf | grep inet_interfaces
doveconf | grep listen

# Restart services
systemctl restart postfix dovecot
```

**Connection Refused:**
```bash
# Check firewall
ufw status
iptables -L

# Test local connectivity
telnet localhost 587

# Test external connectivity
telnet mail.yourdomain.com 587
```

**SSL/TLS Errors:**
```bash
# Test SSL connection
openssl s_client -connect mail.yourdomain.com:993

# Check certificate
openssl x509 -in /etc/ssl/certs/mail.crt -text -noout
```

### Port Monitoring Scripts

```bash
#!/bin/bash
# Email port monitoring script

PORTS="25 587 465 143 993 110 995"
HOST="localhost"

for PORT in $PORTS; do
    if nc -z $HOST $PORT 2>/dev/null; then
        echo "Port $PORT: OPEN"
    else
        echo "Port $PORT: CLOSED"
    fi
done
```

### Log Analysis

```bash
# Check connection logs
grep "connect from" /var/log/mail.log

# Check authentication failures
grep "authentication failed" /var/log/mail.log

# Check SSL/TLS connections
grep -i "tls\|ssl" /var/log/mail.log
```

## Integration with Other Services

### Load Balancer Configuration

```
# HAProxy configuration for email ports
frontend smtp_frontend
    bind *:587
    mode tcp
    default_backend smtp_backend

backend smtp_backend
    mode tcp
    balance roundrobin
    server mail1 ************:587 check
    server mail2 ************:587 check
```

### Docker Port Mapping

```yaml
# docker-compose.yml
version: '3'
services:
  mail:
    image: baum-mail:latest
    ports:
      - "25:25"
      - "587:587"
      - "465:465"
      - "143:143"
      - "993:993"
      - "110:110"
      - "995:995"
```

### Cloud Provider Security Groups

**AWS Security Group Rules:**
- Type: Custom TCP, Port: 25, Source: 0.0.0.0/0
- Type: Custom TCP, Port: 587, Source: 0.0.0.0/0
- Type: Custom TCP, Port: 465, Source: 0.0.0.0/0
- Type: Custom TCP, Port: 143, Source: 0.0.0.0/0
- Type: Custom TCP, Port: 993, Source: 0.0.0.0/0
- Type: Custom TCP, Port: 110, Source: 0.0.0.0/0
- Type: Custom TCP, Port: 995, Source: 0.0.0.0/0

**Note**: Many cloud providers block port 25 by default for anti-spam reasons.

# SpamAssassin Spam Filtering

## Overview

SpamAssassin is a powerful spam filtering system that integrates with the Baum Mail plugin to automatically detect and filter unwanted email. It uses a combination of rule-based filtering, Bayesian analysis, and network-based tests to identify spam with high accuracy while minimizing false positives.

## What SpamAssassin Does

- **Spam Detection**: Analyzes email content using multiple filtering techniques
- **Bayesian Learning**: Learns from spam and ham (legitimate email) patterns
- **Rule-Based Filtering**: Uses predefined rules to identify spam characteristics
- **Network Tests**: Checks sender reputation and blacklists
- **Content Analysis**: Examines headers, body text, and attachments
- **Score-Based Classification**: Assigns spam scores for flexible filtering

## How Baum Mail Uses SpamAssassin

### Core Integration

The Baum Mail plugin integrates with Spam<PERSON><PERSON><PERSON> through the `BaumMail_Security` class:

```php
// Get security component
$security = baum_mail()->get_component('security');

// Check SpamAssassin status
$spam_status = $security->get_spamassassin_status();

// Update spam rules
$security->update_spam_rules();

// Train SpamAssassin with spam/ham
$security->train_spamassassin($email_content, $is_spam);
```

### Automatic Spam Filtering

```php
// Process incoming email through SpamAssassin
class BaumMail_SpamFilter {
  
  /**
   * Filter email through SpamAssassin
   */
  public function filter_email($email_content) {
    if (!get_option('baum_mail_enable_spamassassin', true)) {
      return array('is_spam' => false, 'score' => 0);
    }
    
    // Save email to temporary file
    $temp_file = tempnam(sys_get_temp_dir(), 'spam_check_');
    file_put_contents($temp_file, $email_content);
    
    // Run SpamAssassin check
    $command = "spamc < {$temp_file}";
    $output = shell_exec($command);
    
    // Parse SpamAssassin headers
    $spam_score = $this->extract_spam_score($output);
    $spam_threshold = get_option('baum_mail_spam_threshold', 5.0);
    
    unlink($temp_file);
    
    return array(
      'is_spam' => $spam_score >= $spam_threshold,
      'score' => $spam_score,
      'filtered_content' => $output
    );
  }
  
  /**
   * Extract spam score from SpamAssassin headers
   */
  private function extract_spam_score($content) {
    if (preg_match('/X-Spam-Score:\s*([0-9.-]+)/', $content, $matches)) {
      return floatval($matches[1]);
    }
    return 0;
  }
}
```

### Bayesian Training

```php
/**
 * Train SpamAssassin with spam and ham samples
 */
public function train_spamassassin($email_content, $is_spam) {
  $temp_file = tempnam(sys_get_temp_dir(), 'train_');
  file_put_contents($temp_file, $email_content);
  
  if ($is_spam) {
    // Train as spam
    $command = "sa-learn --spam {$temp_file}";
  } else {
    // Train as ham (legitimate email)
    $command = "sa-learn --ham {$temp_file}";
  }
  
  $output = shell_exec($command);
  unlink($temp_file);
  
  return strpos($output, 'Learned tokens') !== false;
}
```

## Command Line Usage

### Service Management

```bash
# Check SpamAssassin status
sudo systemctl status spamassassin

# Start SpamAssassin daemon
sudo systemctl start spamassassin

# Stop SpamAssassin daemon
sudo systemctl stop spamassassin

# Restart SpamAssassin
sudo systemctl restart spamassassin

# Enable auto-start
sudo systemctl enable spamassassin
```

### Email Testing

```bash
# Test email with SpamAssassin
spamassassin < email.txt

# Test with spamc (client mode)
spamc < email.txt

# Check spam score only
spamassassin -t < email.txt

# Test with specific configuration
spamassassin -C /etc/spamassassin/local.cf < email.txt

# Debug mode (verbose output)
spamassassin -D < email.txt
```

### Bayesian Training

```bash
# Train with spam samples
sa-learn --spam /path/to/spam/folder/

# Train with ham (legitimate email) samples
sa-learn --ham /path/to/ham/folder/

# Train with single email file
sa-learn --spam spam_email.txt
sa-learn --ham legitimate_email.txt

# Check Bayesian database statistics
sa-learn --dump magic

# Rebuild Bayesian database
sa-learn --rebuild

# Backup Bayesian database
sa-learn --backup > bayes_backup.txt

# Restore Bayesian database
sa-learn --restore < bayes_backup.txt
```

### Rule Management

```bash
# Update SpamAssassin rules
sa-update

# Update rules and restart daemon
sa-update && systemctl restart spamassassin

# Check for rule updates without applying
sa-update --checkonly

# Update from specific channel
sa-update --channel updates.spamassassin.org

# List installed rule channels
sa-update --list

# Force rule update
sa-update --nogpg
```

### Configuration Testing

```bash
# Test SpamAssassin configuration
spamassassin --lint

# Test specific configuration file
spamassassin -C /etc/spamassassin/local.cf --lint

# Show configuration settings
spamassassin -D config 2>&1 | grep "config:"

# Test rules against sample email
spamassassin -t -D < sample_email.txt
```

## Programmatic API Usage

### PHP Integration

```php
class BaumMail_SpamAssassin {
  
  /**
   * Check email for spam using spamc
   */
  public function check_spam($email_content) {
    $descriptors = array(
      0 => array('pipe', 'r'),  // stdin
      1 => array('pipe', 'w'),  // stdout
      2 => array('pipe', 'w')   // stderr
    );
    
    $process = proc_open('spamc', $descriptors, $pipes);
    
    if (is_resource($process)) {
      // Send email content to spamc
      fwrite($pipes[0], $email_content);
      fclose($pipes[0]);
      
      // Read filtered output
      $filtered_content = stream_get_contents($pipes[1]);
      fclose($pipes[1]);
      
      // Read any errors
      $errors = stream_get_contents($pipes[2]);
      fclose($pipes[2]);
      
      $return_code = proc_close($process);
      
      return array(
        'filtered_content' => $filtered_content,
        'is_spam' => $return_code !== 0,
        'errors' => $errors
      );
    }
    
    return new WP_Error('spamc_failed', 'Failed to execute spamc');
  }
  
  /**
   * Train SpamAssassin with email samples
   */
  public function train_with_samples($spam_dir, $ham_dir) {
    $results = array('spam_trained' => 0, 'ham_trained' => 0);
    
    // Train with spam samples
    if (is_dir($spam_dir)) {
      $command = sprintf('sa-learn --spam %s 2>&1', escapeshellarg($spam_dir));
      $output = shell_exec($command);
      
      if (preg_match('/Learned tokens from (\d+) message/', $output, $matches)) {
        $results['spam_trained'] = intval($matches[1]);
      }
    }
    
    // Train with ham samples
    if (is_dir($ham_dir)) {
      $command = sprintf('sa-learn --ham %s 2>&1', escapeshellarg($ham_dir));
      $output = shell_exec($command);
      
      if (preg_match('/Learned tokens from (\d+) message/', $output, $matches)) {
        $results['ham_trained'] = intval($matches[1]);
      }
    }
    
    return $results;
  }
  
  /**
   * Get SpamAssassin statistics
   */
  public function get_statistics() {
    $stats_output = shell_exec('sa-learn --dump magic 2>&1');
    
    $stats = array(
      'spam_messages' => 0,
      'ham_messages' => 0,
      'tokens' => 0,
      'database_age' => 0
    );
    
    if (preg_match('/nspam\s+(\d+)/', $stats_output, $matches)) {
      $stats['spam_messages'] = intval($matches[1]);
    }
    
    if (preg_match('/nham\s+(\d+)/', $stats_output, $matches)) {
      $stats['ham_messages'] = intval($matches[1]);
    }
    
    if (preg_match('/ntokens\s+(\d+)/', $stats_output, $matches)) {
      $stats['tokens'] = intval($matches[1]);
    }
    
    return $stats;
  }
}
```

### WordPress Integration

```php
// Hook into email processing
add_filter('baum_mail_process_incoming_email', function($email_data) {
  if (get_option('baum_mail_enable_spamassassin', true)) {
    $spam_filter = new BaumMail_SpamAssassin();
    $result = $spam_filter->check_spam($email_data['content']);
    
    if (!is_wp_error($result)) {
      $email_data['spam_score'] = $spam_filter->extract_spam_score($result['filtered_content']);
      $email_data['is_spam'] = $result['is_spam'];
      $email_data['content'] = $result['filtered_content'];
      
      // Move spam to spam folder
      if ($result['is_spam']) {
        $email_data['folder'] = 'INBOX.Spam';
      }
    }
  }
  
  return $email_data;
});

// Auto-training based on user actions
add_action('baum_mail_email_moved_to_spam', function($email_id) {
  $email_content = baum_mail_get_email_content($email_id);
  $spam_filter = new BaumMail_SpamAssassin();
  $spam_filter->train_with_content($email_content, true);
});

add_action('baum_mail_email_moved_from_spam', function($email_id) {
  $email_content = baum_mail_get_email_content($email_id);
  $spam_filter = new BaumMail_SpamAssassin();
  $spam_filter->train_with_content($email_content, false);
});
```

### REST API Integration

```php
// SpamAssassin management endpoints
add_action('rest_api_init', function() {
  register_rest_route('baum-mail/v1', '/spamassassin/status', array(
    'methods' => 'GET',
    'callback' => function() {
      $security = baum_mail()->get_component('security');
      return $security->get_spamassassin_status();
    },
    'permission_callback' => 'baum_mail_check_permissions'
  ));
  
  register_rest_route('baum-mail/v1', '/spamassassin/train', array(
    'methods' => 'POST',
    'callback' => function($request) {
      $spam_filter = new BaumMail_SpamAssassin();
      
      $email_content = $request->get_param('email_content');
      $is_spam = $request->get_param('is_spam');
      
      $result = $spam_filter->train_with_content($email_content, $is_spam);
      
      return array('success' => $result);
    },
    'permission_callback' => 'baum_mail_check_permissions'
  ));
  
  register_rest_route('baum-mail/v1', '/spamassassin/update-rules', array(
    'methods' => 'POST',
    'callback' => function() {
      $security = baum_mail()->get_component('security');
      $result = $security->update_spam_rules();
      
      if (is_wp_error($result)) {
        return $result;
      }
      
      return array('success' => true, 'message' => 'Rules updated successfully');
    },
    'permission_callback' => 'baum_mail_check_permissions'
  ));
});
```

## End-User Usage

### Email Client Integration

**Automatic Spam Filtering:**
- SpamAssassin runs transparently on the server
- Spam emails are automatically moved to spam folder
- Users can review and manage spam in their email client
- False positives can be moved back to inbox for training

**Manual Spam Management:**
1. **Mark as Spam**: Move unwanted emails to spam folder
2. **Mark as Not Spam**: Move legitimate emails from spam to inbox
3. **Training**: Server learns from user actions to improve filtering
4. **Whitelist**: Add trusted senders to whitelist
5. **Blacklist**: Block specific senders or domains

### Webmail Integration

```php
// Webmail spam management interface
class BaumMail_WebmailSpam {
  
  /**
   * Get spam folder contents
   */
  public function get_spam_emails($user_email) {
    // Connect to IMAP and get spam folder
    $imap = imap_open(
      '{localhost:993/imap/ssl}INBOX.Spam',
      $user_email,
      $user_password
    );
    
    $emails = imap_search($imap, 'ALL');
    $spam_list = array();
    
    foreach ($emails as $email_id) {
      $header = imap_headerinfo($imap, $email_id);
      $spam_list[] = array(
        'id' => $email_id,
        'from' => $header->from[0]->mailbox . '@' . $header->from[0]->host,
        'subject' => $header->subject,
        'date' => $header->date
      );
    }
    
    imap_close($imap);
    return $spam_list;
  }
  
  /**
   * Move email between spam and inbox
   */
  public function move_email($user_email, $email_id, $to_spam = true) {
    $source_folder = $to_spam ? 'INBOX' : 'INBOX.Spam';
    $dest_folder = $to_spam ? 'INBOX.Spam' : 'INBOX';
    
    // Move email and train SpamAssassin
    $this->move_imap_email($user_email, $email_id, $source_folder, $dest_folder);
    
    // Train SpamAssassin based on user action
    $email_content = $this->get_email_content($user_email, $email_id, $source_folder);
    $spam_filter = new BaumMail_SpamAssassin();
    $spam_filter->train_with_content($email_content, $to_spam);
  }
}
```

## Configuration Files

### Main Configuration (/etc/spamassassin/local.cf)

```
# SpamAssassin Configuration for Baum Mail

# Spam threshold (default: 5.0)
required_score 5.0

# Rewrite subject line for spam
rewrite_header Subject [SPAM]

# Report safe (don't modify non-spam)
report_safe 0

# Use Bayesian filtering
use_bayes 1
bayes_auto_learn 1
bayes_auto_learn_threshold_nonspam 0.1
bayes_auto_learn_threshold_spam 12.0

# Network tests
skip_rbl_checks 0
use_razor2 1
use_dcc 1
use_pyzor 1

# Custom rules directory
include /etc/spamassassin/custom.cf

# Whitelist trusted domains
whitelist_from *@yourdomain.com
whitelist_from *@trusteddomain.com

# Blacklist known spam domains
blacklist_from *@spammydomain.com

# Custom scoring
score BAYES_99 3.5
score BAYES_95 2.5
score RAZOR2_CHECK 1.5
```

### Custom Rules (/etc/spamassassin/custom.cf)

```
# Custom SpamAssassin rules for Baum Mail

# Block emails with suspicious subjects
header SUSPICIOUS_SUBJECT Subject =~ /\b(urgent|winner|lottery|prize)\b/i
describe SUSPICIOUS_SUBJECT Contains suspicious subject keywords
score SUSPICIOUS_SUBJECT 2.0

# Block emails with excessive caps
body EXCESSIVE_CAPS /[A-Z]{10,}/
describe EXCESSIVE_CAPS Contains excessive capital letters
score EXCESSIVE_CAPS 1.5

# Block emails with suspicious attachments
body SUSPICIOUS_ATTACHMENT /\.(exe|scr|bat|com|pif|vbs)$/i
describe SUSPICIOUS_ATTACHMENT Contains suspicious attachment
score SUSPICIOUS_ATTACHMENT 3.0

# Whitelist internal emails
header INTERNAL_EMAIL From =~ /@yourdomain\.com$/i
describe INTERNAL_EMAIL Email from internal domain
score INTERNAL_EMAIL -5.0
```

### Postfix Integration (/etc/postfix/main.cf)

```
# SpamAssassin integration with Postfix
smtpd_milters = unix:/spamass/spamass.sock
non_smtpd_milters = unix:/spamass/spamass.sock
milter_connect_macros = i j {daemon_name} v {if_name} _
```

## Performance Optimization

### Memory and CPU Tuning

```
# /etc/default/spamassassin
ENABLED=1
OPTIONS="--create-prefs --max-children 5 --helper-home-dir"
PIDFILE="/var/run/spamd.pid"
CRON=1

# Limit memory usage
OPTIONS="$OPTIONS --max-spare=3 --min-spare=1"

# Enable compression
OPTIONS="$OPTIONS --compress"
```

### Database Optimization

```bash
# Optimize Bayesian database
sa-learn --sync

# Rebuild database for better performance
sa-learn --rebuild

# Clean old tokens
sa-learn --force-expire

# Set database permissions
chown -R debian-spamd:debian-spamd /var/lib/spamassassin/.spamassassin/
```

## Troubleshooting

### Common Issues

**SpamAssassin Not Starting:**
```bash
# Check configuration
spamassassin --lint

# Check service status
systemctl status spamassassin

# Check logs
tail -f /var/log/mail.log | grep spamd
```

**Poor Spam Detection:**
```bash
# Check Bayesian database
sa-learn --dump magic

# Train with more samples
sa-learn --spam /path/to/spam/samples/
sa-learn --ham /path/to/ham/samples/

# Update rules
sa-update
```

**High CPU Usage:**
- Reduce max-children in configuration
- Enable rule compilation with re2c
- Use faster storage for Bayesian database
- Consider using Redis for Bayesian storage

**False Positives:**
- Lower spam threshold
- Add senders to whitelist
- Train with false positive samples
- Review and adjust custom rules

### Log Analysis

```bash
# SpamAssassin logs
tail -f /var/log/mail.log | grep spamd

# Training logs
grep "sa-learn" /var/log/mail.log

# Rule update logs
grep "sa-update" /var/log/mail.log
```

## Integration with Other Services

- **Postfix**: Milter integration for automatic filtering
- **Dovecot**: Sieve rules for spam folder management
- **ClamAV**: Combined antivirus and spam filtering
- **Amavis**: Content filtering framework integration
- **Razor/Pyzor/DCC**: Network-based spam detection
- **Roundcube**: Webmail spam management interface

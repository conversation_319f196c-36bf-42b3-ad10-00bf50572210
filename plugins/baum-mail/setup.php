<?php
/**
 * Baum Mail Setup Script
 *
 * Run this script to set up the complete mail server environment
 *
 * @package BaumMail
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
  exit;
}

/**
 * Baum Mail Setup Class
 */
class BaumMail_Setup {

  /**
   * Run complete setup
   */
  public static function run() {
    echo "🚀 Starting Baum Mail Setup...\n\n";

    // Step 1: Install required packages
    self::install_packages();

    // Step 2: Create system user
    self::create_system_user();

    // Step 3: Generate SSL certificates
    self::generate_ssl_certificates();

    // Step 4: Configure Postfix
    self::configure_postfix();

    // Step 5: Configure Dovecot
    self::configure_dovecot();

    // Step 6: Set up encryption
    self::setup_encryption();

    // Step 7: Configure firewall
    self::configure_firewall();

    // Step 8: Configure logging
    self::configure_logging();

    // Step 9: Configure email tracking
    self::configure_tracking();

    // Step 10: Start services
    self::start_services();

    // Step 11: Test configuration
    self::test_configuration();

    echo "✅ Baum Mail setup completed successfully!\n\n";
    self::display_summary();
  }

  /**
   * Install required packages
   */
  private static function install_packages() {
    echo "📦 Installing required packages...\n";

    $packages = array(
      'postfix',
      'dovecot-core',
      'dovecot-imapd',
      'dovecot-pop3d',
      'dovecot-lmtpd',
      'dovecot-mysql',
      'php-gnupg',
      'php-imap',
      'gnupg',
      'openssl',
      'mysql-client'
    );

    $package_list = implode(' ', $packages);
    
    // Update package list
    self::execute_command('apt-get update');
    
    // Install packages
    self::execute_command("apt-get install -y {$package_list}");

    echo "✅ Packages installed successfully\n\n";
  }

  /**
   * Create system user for mail
   */
  private static function create_system_user() {
    echo "👤 Creating system user...\n";

    // Create vmail user and group
    self::execute_command('groupadd -g 1001 vmail');
    self::execute_command('useradd -g vmail -u 1001 vmail -d /var/mail/vhosts -m');
    
    // Set permissions
    self::execute_command('chown -R vmail:vmail /var/mail/vhosts');
    self::execute_command('chmod -R 755 /var/mail/vhosts');

    echo "✅ System user created successfully\n\n";
  }

  /**
   * Generate SSL certificates
   */
  private static function generate_ssl_certificates() {
    echo "🔐 Generating SSL certificates...\n";

    $hostname = get_option('baum_mail_hostname', 'mail.example.com');
    $cert_path = get_option('baum_mail_ssl_cert_path', '/etc/ssl/certs/mail.crt');
    $key_path = get_option('baum_mail_ssl_key_path', '/etc/ssl/private/mail.key');

    // Create directories
    self::execute_command('mkdir -p /etc/ssl/certs /etc/ssl/private');

    // Generate private key
    self::execute_command("openssl genrsa -out {$key_path} 4096");

    // Generate certificate signing request
    $csr_path = '/tmp/mail.csr';
    $subject = "/C=US/ST=State/L=City/O=Organization/OU=IT/CN={$hostname}";
    self::execute_command("openssl req -new -key {$key_path} -out {$csr_path} -subj '{$subject}'");

    // Generate self-signed certificate (replace with proper CA certificate in production)
    self::execute_command("openssl x509 -req -days 365 -in {$csr_path} -signkey {$key_path} -out {$cert_path}");

    // Set permissions
    self::execute_command("chmod 600 {$key_path}");
    self::execute_command("chmod 644 {$cert_path}");

    // Clean up
    self::execute_command("rm {$csr_path}");

    echo "✅ SSL certificates generated successfully\n\n";
  }

  /**
   * Configure Postfix
   */
  private static function configure_postfix() {
    echo "📮 Configuring Postfix...\n";

    $baum_mail = baum_mail();
    $core = $baum_mail->get_component('core');

    // Generate Postfix configuration
    $core->generate_postfix_config();

    // Create mail directories
    self::execute_command('mkdir -p /var/mail/vhosts');
    self::execute_command('chown -R vmail:vmail /var/mail/vhosts');

    echo "✅ Postfix configured successfully\n\n";
  }

  /**
   * Configure Dovecot
   */
  private static function configure_dovecot() {
    echo "📬 Configuring Dovecot...\n";

    $baum_mail = baum_mail();
    $core = $baum_mail->get_component('core');

    // Generate Dovecot configuration
    $core->generate_dovecot_config();

    // Set permissions
    self::execute_command('chown -R dovecot:dovecot /etc/dovecot');
    self::execute_command('chmod 600 /etc/dovecot/dovecot-sql.conf.ext');

    echo "✅ Dovecot configured successfully\n\n";
  }

  /**
   * Set up encryption
   */
  private static function setup_encryption() {
    echo "🔒 Setting up encryption...\n";

    // Create GPG directory
    self::execute_command('mkdir -p /var/lib/baum-mail/gnupg');
    self::execute_command('chown -R www-data:www-data /var/lib/baum-mail');
    self::execute_command('chmod 700 /var/lib/baum-mail/gnupg');

    // Set GPG home directory
    putenv('GNUPGHOME=/var/lib/baum-mail/gnupg');

    // Enable encryption by default
    update_option('baum_mail_encryption_enabled', true);
    update_option('baum_mail_default_encryption', true);

    echo "✅ Encryption set up successfully\n\n";
  }

  /**
   * Configure firewall
   */
  private static function configure_firewall() {
    echo "🔥 Configuring firewall...\n";

    // Allow mail ports
    $ports = array(25, 587, 465, 993, 995, 143, 110);
    
    foreach ($ports as $port) {
      self::execute_command("ufw allow {$port}");
    }

    echo "✅ Firewall configured successfully\n\n";
  }

  /**
   * Start services
   */
  private static function start_services() {
    echo "🚀 Starting services...\n";

    $services = array('postfix', 'dovecot');
    
    foreach ($services as $service) {
      self::execute_command("systemctl enable {$service}");
      self::execute_command("systemctl start {$service}");
      self::execute_command("systemctl reload {$service}");
    }

    echo "✅ Services started successfully\n\n";
  }

  /**
   * Test configuration
   */
  private static function test_configuration() {
    echo "🧪 Testing configuration...\n";

    $baum_mail = baum_mail();
    
    // Test SMTP
    $smtp = $baum_mail->get_component('smtp');
    $smtp_status = $smtp->get_server_status();
    
    // Test IMAP
    $imap = $baum_mail->get_component('imap');
    $imap_status = $imap->get_server_status();
    
    // Test encryption
    $encryption = $baum_mail->get_component('encryption');
    $encryption_status = $encryption->get_encryption_status();

    echo "SMTP Status: " . ($smtp_status['postfix_running'] ? '✅ Running' : '❌ Not running') . "\n";
    echo "IMAP Status: " . ($imap_status['server_running'] ? '✅ Running' : '❌ Not running') . "\n";
    echo "Encryption: " . ($encryption_status['available'] ? '✅ Available' : '❌ Not available') . "\n";

    echo "✅ Configuration tested successfully\n\n";
  }

  /**
   * Display setup summary
   */
  private static function display_summary() {
    $hostname = get_option('baum_mail_hostname', 'mail.example.com');
    
    echo "📋 Setup Summary:\n";
    echo "================\n\n";
    echo "🌐 Hostname: {$hostname}\n";
    echo "📮 SMTP Ports: 25 (plain), 587 (submission), 465 (smtps)\n";
    echo "📬 IMAP Ports: 143 (plain), 993 (imaps)\n";
    echo "📪 POP3 Ports: 110 (plain), 995 (pop3s)\n";
    echo "🔐 SSL/TLS: Enabled\n";
    echo "🔒 GPG Encryption: Available\n";
    echo "📊 Admin Interface: /wp-admin/admin.php?page=baum-mail\n\n";
    
    echo "🎯 Next Steps:\n";
    echo "=============\n";
    echo "1. Create your first domain via admin interface\n";
    echo "2. Create email accounts for your domain\n";
    echo "3. Generate GPG keys for encryption\n";
    echo "4. Configure DNS records (MX, SPF, DKIM, DMARC)\n";
    echo "5. Test email sending and receiving\n\n";
    
    echo "📚 Documentation: See README.md for programmatic API usage\n";
  }

  /**
   * Execute system command
   */
  private static function execute_command($command) {
    echo "  → {$command}\n";
    $output = shell_exec($command . ' 2>&1');
    if ($output) {
      echo "    " . trim($output) . "\n";
    }
  }

  /**
   * Configure logging for all services
   */
  private static function configure_logging() {
    echo "📝 Configuring logging...\n";

    $plugin_logs_dir = dirname(__FILE__) . '/logs';

    // Ensure logs directory exists and is writable
    if (!is_dir($plugin_logs_dir)) {
      mkdir($plugin_logs_dir, 0755, true);
    }

    // Make logs directory writable by mail services
    exec("chown -R postfix:postfix $plugin_logs_dir");
    exec("chmod -R 755 $plugin_logs_dir");

    // Configure Postfix logging
    self::configure_postfix_logging($plugin_logs_dir);

    // Configure Dovecot logging
    self::configure_dovecot_logging($plugin_logs_dir);

    // Configure ClamAV logging
    self::configure_clamav_logging($plugin_logs_dir);

    // Configure SpamAssassin logging
    self::configure_spamassassin_logging($plugin_logs_dir);

    // Configure GPG logging
    self::configure_gpg_logging($plugin_logs_dir);

    // Configure rsyslog integration
    self::configure_rsyslog($plugin_logs_dir);

    echo "✅ Logging configuration completed\n\n";
  }

  /**
   * Configure Postfix logging
   */
  private static function configure_postfix_logging($logs_dir) {
    $postfix_log = $logs_dir . '/postfix.log';

    // Add logging configuration to main.cf
    $main_cf_additions = array(
      "maillog_file = $postfix_log",
      "maillog_file_prefixes = /var/log, $logs_dir"
    );

    foreach ($main_cf_additions as $config) {
      exec("postconf -e '$config'");
    }

    // Create log file
    touch($postfix_log);
    exec("chown postfix:postfix $postfix_log");
    exec("chmod 644 $postfix_log");
  }

  /**
   * Configure Dovecot logging
   */
  private static function configure_dovecot_logging($logs_dir) {
    $dovecot_log = $logs_dir . '/dovecot.log';
    $dovecot_conf = '/etc/dovecot/conf.d/10-logging.conf';

    $logging_config = "
# Baum Mail Dovecot Logging Configuration
log_path = $dovecot_log
info_log_path = $dovecot_log
debug_log_path = $dovecot_log
log_timestamp = \"%Y-%m-%d %H:%M:%S \"
";

    file_put_contents($dovecot_conf, $logging_config);

    // Create log file
    touch($dovecot_log);
    exec("chown dovecot:dovecot $dovecot_log");
    exec("chmod 644 $dovecot_log");
  }

  /**
   * Configure ClamAV logging
   */
  private static function configure_clamav_logging($logs_dir) {
    $clamav_log = $logs_dir . '/clamav.log';
    $clamd_conf = '/etc/clamav/clamd.conf';

    // Update ClamAV configuration
    $config_updates = array(
      "LogFile $clamav_log",
      "LogTime yes",
      "LogFileUnlock yes",
      "LogFileMaxSize 10M",
      "LogRotate yes"
    );

    foreach ($config_updates as $config) {
      exec("echo '$config' >> $clamd_conf");
    }

    // Create log file
    touch($clamav_log);
    exec("chown clamav:clamav $clamav_log");
    exec("chmod 644 $clamav_log");
  }

  /**
   * Configure SpamAssassin logging
   */
  private static function configure_spamassassin_logging($logs_dir) {
    $spamassassin_log = $logs_dir . '/spamassassin.log';
    $sa_conf = '/etc/spamassassin/local.cf';

    $logging_config = "
# Baum Mail SpamAssassin Logging Configuration
use_auto_whitelist 1
auto_whitelist_path $logs_dir/auto-whitelist
auto_whitelist_file_mode 0666
";

    file_put_contents($sa_conf, $logging_config, FILE_APPEND);

    // Create log file
    touch($spamassassin_log);
    exec("chown debian-spamd:debian-spamd $spamassassin_log");
    exec("chmod 644 $spamassassin_log");
  }

  /**
   * Configure GPG logging
   */
  private static function configure_gpg_logging($logs_dir) {
    $gpg_log = $logs_dir . '/gpg.log';

    // Create GPG logging wrapper script
    $gpg_wrapper = '/usr/local/bin/baum-mail-gpg';
    $wrapper_content = "#!/bin/bash
# Baum Mail GPG Logging Wrapper
echo \"[\$(date)] GPG command: \$@\" >> $gpg_log
/usr/bin/gpg \"\$@\" 2>&1 | tee -a $gpg_log
";

    file_put_contents($gpg_wrapper, $wrapper_content);
    exec("chmod +x $gpg_wrapper");

    // Create log file
    touch($gpg_log);
    exec("chmod 644 $gpg_log");
  }

  /**
   * Configure rsyslog integration
   */
  private static function configure_rsyslog($logs_dir) {
    $rsyslog_conf = '/etc/rsyslog.d/50-baum-mail.conf';

    $rsyslog_config = "
# Baum Mail rsyslog Configuration
# Forward mail-related logs to plugin directory

# Postfix logs
:programname, isequal, \"postfix\" $logs_dir/postfix.log
& stop

# Dovecot logs
:programname, isequal, \"dovecot\" $logs_dir/dovecot.log
& stop

# ClamAV logs
:programname, isequal, \"clamd\" $logs_dir/clamav.log
:programname, isequal, \"clamav-daemon\" $logs_dir/clamav.log
& stop

# SpamAssassin logs
:programname, isequal, \"spamd\" $logs_dir/spamassassin.log
& stop

# Create daily log rotation
\$ModLoad imfile
\$InputFileName $logs_dir/postfix.log
\$InputFileTag postfix:
\$InputFileStateFile postfix-state
\$InputFileSeverity info
\$InputFileFacility mail
\$InputRunFileMonitor

\$InputFileName $logs_dir/dovecot.log
\$InputFileTag dovecot:
\$InputFileStateFile dovecot-state
\$InputFileSeverity info
\$InputFileFacility mail
\$InputRunFileMonitor
";

    file_put_contents($rsyslog_conf, $rsyslog_config);

    // Restart rsyslog
    exec('systemctl restart rsyslog');
  }

  /**
   * Configure email tracking
   */
  private static function configure_tracking() {
    echo "📊 Configuring email tracking...\n";

    if (get_option('baum_mail_tracking_enabled', false)) {
      // Configure Postfix tracking
      $postfix = new BaumMail_Postfix();
      $result = $postfix->configure_tracking();

      if (is_wp_error($result)) {
        echo "⚠️  Warning: Could not configure email tracking: " . $result->get_error_message() . "\n";
      } else {
        echo "✅ Email tracking configured successfully\n";
      }
    } else {
      echo "ℹ️  Email tracking is disabled\n";
    }

    echo "\n";
  }
}

// Run setup if called directly
if (php_sapi_name() === 'cli' && isset($argv[0]) && basename($argv[0]) === 'setup.php') {
  // Load WordPress
  require_once dirname(__FILE__) . '/../../../wp-load.php';
  
  // Run setup
  BaumMail_Setup::run();
}
